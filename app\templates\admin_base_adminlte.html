<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Admin Dashboard{% endblock %} - Document Management System</title>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;400i;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='adminlte/dist/css/adminlte.min.css') }}">

    <!-- Toastify CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Custom AdminLTE Overrides -->
    <style>
        /* Ensure proper AdminLTE layout */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        
        .wrapper {
            min-height: 100vh;
            position: relative;
        }
        
        /* Enhanced Sidebar Styling */
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1038;
            transition: all 0.3s ease-in-out;
            width: 250px;
            background: linear-gradient(180deg, #343a40 0%, #2c3237 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        /* Content wrapper positioning */
        .content-wrapper {
            margin-left: 250px;
            min-height: calc(100vh - 57px);
            margin-top: 57px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.3s ease-in-out;
            position: relative;
            z-index: 1;
        }
        
        /* Main header */
        .main-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1037;
            margin-left: 250px;
            transition: all 0.3s ease-in-out;
            background: linear-gradient(90deg, #fff 0%, #f8f9fa 100%);
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* Footer */
        .main-footer {
            margin-left: 250px;
            transition: all 0.3s ease-in-out;
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            border-top: 1px solid #dee2e6;
        }
        
        /* Sidebar collapsed state */
        .sidebar-collapse .main-sidebar {
            margin-left: -250px;
        }
        
        .sidebar-collapse .content-wrapper,
        .sidebar-collapse .main-header,
        .sidebar-collapse .main-footer {
            margin-left: 0;
        }
        
        /* Mobile responsive */
        @media (max-width: 767.98px) {
            .main-sidebar {
                margin-left: -250px;
            }
            
            .content-wrapper,
            .main-header,
            .main-footer {
                margin-left: 0;
            }
            
            .sidebar-open .main-sidebar {
                margin-left: 0;
            }
            
            /* Mobile overlay */
            .sidebar-open::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1037;
                animation: fadeIn 0.3s ease-in-out;
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* Enhanced Header Styling */
        .main-header .navbar-nav .nav-link {
            color: rgba(0,0,0,.6);
            font-weight: 500;
            transition: all 0.3s ease-in-out;
        }
        
        .main-header .navbar-nav .nav-link:hover {
            color: rgba(0,0,0,.8);
            transform: translateY(-1px);
        }
        
        /* Enhanced Card Styling */
        .card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: none;
            border-radius: 0.75rem;
            transition: all 0.3s ease-in-out;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }
        
        .info-box {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-radius: 0.75rem;
            transition: all 0.3s ease-in-out;
        }
        
        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .small-box {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border-radius: 0.75rem;
            transition: all 0.3s ease-in-out;
        }
        
        .small-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        /* Enhanced Content Styling */
        .content-header {
            padding: 20px 30px;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 0 0 1rem 1rem;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content {
            padding: 0 30px 30px;
        }
        
        /* Enhanced Sidebar Navigation */
        .nav-sidebar .nav-item > .nav-link {
            padding: 0.75rem 1rem;
            margin: 0.25rem 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #c2c7d0;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        .nav-sidebar .nav-item > .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        
        .nav-sidebar .nav-item > .nav-link:hover::before {
            left: 100%;
        }
        
        .nav-sidebar .nav-item > .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: #fff;
            transform: translateX(4px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .nav-sidebar .nav-item > .nav-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #fff;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
            transform: translateX(2px);
        }
        
        .nav-sidebar .nav-item > .nav-link.active .nav-icon {
            color: #fff !important;
        }
        
        /* Enhanced Treeview Styling */
        .nav-treeview {
            padding-left: 0;
            margin-left: 1.5rem;
            border-left: 2px solid rgba(255,255,255,0.2);
            background: rgba(0,0,0,0.1);
            border-radius: 0 0.5rem 0.5rem 0;
        }
        
        .nav-treeview .nav-item .nav-link {
            padding: 0.6rem 1rem;
            margin: 0.125rem 0.5rem;
            border-radius: 0.375rem;
            color: rgba(255,255,255,0.8);
            font-size: 0.9rem;
            position: relative;
            transition: all 0.3s ease-in-out;
        }
        
        .nav-treeview .nav-item .nav-link::before {
            content: '';
            position: absolute;
            left: -1.5rem;
            top: 50%;
            width: 12px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5));
            transform: translateY(-50%);
            transition: all 0.3s ease-in-out;
        }
        
        .nav-treeview .nav-item .nav-link:hover {
            background: rgba(255,255,255,0.15);
            color: #fff;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .nav-treeview .nav-item .nav-link:hover::before {
            width: 16px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8));
        }
        
        .nav-treeview .nav-item .nav-link.active {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.9) 0%, rgba(32, 201, 151, 0.9) 100%);
            color: #fff;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        /* Enhanced Icon Styling */
        .nav-icon {
            margin-right: 0.75rem;
            width: 1.25rem;
            text-align: center;
            font-size: 1rem;
            transition: all 0.3s ease-in-out;
        }
        
        /* Enhanced Dropdown Arrow */
        .nav-link .right {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
        }
        
        .nav-item.menu-open > .nav-link .right {
            transform: rotate(-90deg);
        }
        
        /* Enhanced Brand Link */
        .brand-link {
            padding: 1.25rem 1rem;
            border-bottom: 1px solid #495057;
            background: rgba(0,0,0,0.1);
            transition: all 0.3s ease-in-out;
        }
        
        .brand-link:hover {
            background: rgba(0,0,0,0.15);
            text-decoration: none;
        }
        
        .brand-text {
            font-weight: 600;
            font-size: 1.1rem;
            color: #fff;
        }
        
        /* Enhanced User Panel */
        .user-panel {
            border-bottom: 1px solid #495057;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: rgba(0,0,0,0.1);
        }
        
        .user-panel .info a {
            color: #c2c7d0;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease-in-out;
        }
        
        .user-panel .info a:hover {
            color: #fff;
            transform: translateX(2px);
        }
        
        .user-avatar {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }
        
        /* Enhanced Sidebar Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: #343a40;
            border-radius: 4px;
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #495057 0%, #6c757d 100%);
            border-radius: 4px;
            transition: all 0.3s ease-in-out;
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #6c757d 0%, #868e96 100%);
        }
        
        /* Enhanced Dropdown Menu */
        .dropdown-menu {
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border: none;
            padding: 0.5rem 0;
        }
        
        .dropdown-item {
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease-in-out;
        }
        
        .dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(4px);
        }
        
        /* Enhanced Breadcrumb */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            font-weight: 600;
            color: #6c757d;
        }
        
        /* Enhanced Alerts */
        .alert {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* Animation for menu items */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .nav-treeview {
            animation: slideDown 0.3s ease-in-out;
        }
        
        /* Loading animation */
        .nav-link.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 1rem;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Enhanced responsive design */
        @media (max-width: 575.98px) {
            .content-header {
                padding: 15px 20px;
            }
            
            .content {
                padding: 0 20px 20px;
            }
            
            .nav-sidebar .nav-item > .nav-link {
                margin: 0.25rem 0.5rem;
            }
        }
    </style>

    {% block head %}{% endblock %}
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                        <i class="fas fa-bars"></i>
                    </a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{{ url_for('admin.admin_dashboard_adminlte') }}" class="nav-link">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <!-- Fullscreen Toggle -->
                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button" title="Toggle Fullscreen">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>
                
                <!-- User Account Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-user mr-1"></i>
                        {% if current_user.is_authenticated %}
                            {{ current_user.username }}
                        {% else %}
                            Guest
                        {% endif %}
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        {% if current_user.is_authenticated %}
                            <a href="{{ url_for('user.profile') }}" class="dropdown-item">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('user.logout') }}" class="dropdown-item">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        {% else %}
                            <a href="{{ url_for('user.login') }}" class="dropdown-item">
                                <i class="fas fa-sign-in-alt mr-2"></i> Login
                            </a>
                        {% endif %}
                    </div>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="{{ url_for('admin.admin_dashboard_adminlte') }}" class="brand-link">
                <i class="fas fa-book-reader brand-image" style="opacity: .8; margin-left: 10px; margin-right: 10px; font-size: 1.5rem;"></i>
                <span class="brand-text">DMS Admin</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar user panel -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <div class="user-avatar rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            {% if current_user.is_authenticated %}
                                {{ current_user.username[0].upper() }}
                            {% else %}
                                <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="info">
                        <a href="{{ url_for('user.profile') if current_user.is_authenticated else '#' }}" class="d-block">
                            {% if current_user.is_authenticated %}
                                {{ current_user.username }}
                            {% else %}
                                Guest User
                            {% endif %}
                        </a>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ url_for('admin.admin_dashboard_adminlte') }}" class="nav-link {% if request.endpoint == 'admin.admin_dashboard_adminlte' %}active{% endif %}">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </a>
                        </li>

                        {% if current_user.is_authenticated %}
                        <!-- Content Management -->
                        <li class="nav-item {% if request.endpoint in ['upload_file', 'list_files', 'view_vector_data', 'clean_urls', 'list_forms', 'create_form', 'edit_form', 'list_submissions', 'html_generator_page'] %}menu-open{% endif %}">
                            <a href="#" class="nav-link {% if request.endpoint in ['upload_file', 'list_files', 'view_vector_data', 'clean_urls', 'list_forms', 'create_form', 'edit_form', 'list_submissions', 'html_generator_page'] %}active{% endif %}">
                                <i class="nav-icon fas fa-folder"></i>
                                <p>
                                    Content Management
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                {% if current_user.has_dashboard_permission('upload_content') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('upload_file') }}" class="nav-link {% if request.endpoint == 'upload_file' %}active{% endif %}">
                                        <i class="fas fa-upload nav-icon"></i>
                                        <p>Upload Content</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('manage_files') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('list_files') }}" class="nav-link {% if request.endpoint in ['list_files', 'view_vector_data'] %}active{% endif %}">
                                        <i class="fas fa-file-alt nav-icon"></i>
                                        <p>Manage Files</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('manage_forms') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('list_forms') }}" class="nav-link {% if request.endpoint in ['list_forms', 'create_form', 'edit_form'] %}active{% endif %}">
                                        <i class="fas fa-wpforms nav-icon"></i>
                                        <p>Manage Forms</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('list_submissions') }}" class="nav-link {% if request.endpoint == 'list_submissions' %}active{% endif %}">
                                        <i class="fas fa-clipboard-list nav-icon"></i>
                                        <p>Form Submissions</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('clean_urls') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('clean_urls') }}" class="nav-link {% if request.endpoint == 'clean_urls' %}active{% endif %}">
                                        <i class="fas fa-broom nav-icon"></i>
                                        <p>Clean URLs</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('html_generator') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('html_generator_page') }}" class="nav-link {% if request.endpoint == 'html_generator_page' %}active{% endif %}">
                                        <i class="fas fa-code nav-icon"></i>
                                        <p>HTML Generator</p>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- Chat and Analytics -->
                        <li class="nav-item {% if request.endpoint in ['chat_history', 'view_sessions', 'view_session', 'admin.analytics_dashboard', 'admin.location_map'] %}menu-open{% endif %}">
                            <a href="#" class="nav-link {% if request.endpoint in ['chat_history', 'view_sessions', 'view_session', 'admin.analytics_dashboard', 'admin.location_map'] %}active{% endif %}">
                                <i class="nav-icon fas fa-comments"></i>
                                <p>
                                    Chat & Analytics
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                {% if current_user.has_dashboard_permission('chat_history') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('chat_history') }}" class="nav-link {% if request.endpoint == 'chat_history' %}active{% endif %}">
                                        <i class="fas fa-history nav-icon"></i>
                                        <p>Chat History</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('chat_sessions') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('view_sessions') }}" class="nav-link {% if request.endpoint in ['view_sessions', 'view_session'] %}active{% endif %}">
                                        <i class="fas fa-comment-dots nav-icon"></i>
                                        <p>Chat Sessions</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('ai_analytics') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('admin.analytics_dashboard') }}" class="nav-link {% if request.endpoint == 'admin.analytics_dashboard' %}active{% endif %}">
                                        <i class="fas fa-chart-bar nav-icon"></i>
                                        <p>AI Analytics</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('admin.location_map') }}" class="nav-link {% if request.endpoint == 'admin.location_map' %}active{% endif %}">
                                        <i class="fas fa-map-marked-alt nav-icon"></i>
                                        <p>Location Map</p>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- System Settings -->
                        <li class="nav-item {% if request.endpoint in ['unified_config', 'greeting_management'] %}menu-open{% endif %}">
                            <a href="#" class="nav-link {% if request.endpoint in ['unified_config', 'greeting_management'] %}active{% endif %}">
                                <i class="nav-icon fas fa-cogs"></i>
                                <p>
                                    System Settings
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                {% if current_user.has_dashboard_permission('model_settings') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('unified_config') }}" class="nav-link {% if request.endpoint == 'unified_config' %}active{% endif %}">
                                        <i class="fas fa-sliders-h nav-icon"></i>
                                        <p>Model Settings</p>
                                    </a>
                                </li>
                                {% endif %}
                                {% if current_user.has_dashboard_permission('greeting_management') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('greeting_management') }}" class="nav-link {% if request.endpoint == 'greeting_management' %}active{% endif %}">
                                        <i class="fas fa-comment-alt nav-icon"></i>
                                        <p>Greeting Management</p>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- User Management -->
                        <li class="nav-item {% if request.endpoint in ['user.admin_users', 'user.admin_permission_groups', 'user.admin_permission_audit', 'user.admin_activity_logs', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}menu-open{% endif %}">
                            <a href="#" class="nav-link {% if request.endpoint in ['user.admin_users', 'user.admin_permission_groups', 'user.admin_permission_audit', 'user.admin_activity_logs', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}">
                                <i class="nav-icon fas fa-users"></i>
                                <p>
                                    User Management
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                {% if current_user.has_dashboard_permission('user_management') %}
                                <li class="nav-item">
                                    <a href="{{ url_for('user.admin_users') }}" class="nav-link {% if request.endpoint in ['user.admin_users', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}">
                                        <i class="fas fa-users nav-icon"></i>
                                        <p>Manage Users</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('user.admin_permission_groups') }}" class="nav-link {% if request.endpoint == 'user.admin_permission_groups' %}active{% endif %}">
                                        <i class="fas fa-user-tag nav-icon"></i>
                                        <p>Permission Groups</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('user.admin_permission_audit') }}" class="nav-link {% if request.endpoint == 'user.admin_permission_audit' %}active{% endif %}">
                                        <i class="fas fa-search nav-icon"></i>
                                        <p>Permission Audit</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('user.admin_activity_logs') }}" class="nav-link {% if request.endpoint == 'user.admin_activity_logs' %}active{% endif %}">
                                        <i class="fas fa-clipboard-list nav-icon"></i>
                                        <p>Activity Logs</p>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </li>

                        <!-- Help & Support -->
                        <li class="nav-item {% if request.endpoint in ['documentation'] %}menu-open{% endif %}">
                            <a href="#" class="nav-link {% if request.endpoint in ['documentation'] %}active{% endif %}">
                                <i class="nav-icon fas fa-question-circle"></i>
                                <p>
                                    Help & Support
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{{ url_for('documentation') }}" target="_blank" class="nav-link {% if request.endpoint == 'documentation' %}active{% endif %}">
                                        <i class="fas fa-book nav-icon"></i>
                                        <p>Documentation</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ url_for('main.index') }}" target="_blank" class="nav-link">
                                        <i class="fas fa-comments nav-icon"></i>
                                        <p>Chat Interface</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="mailto:<EMAIL>" class="nav-link">
                                        <i class="fas fa-life-ring nav-icon"></i>
                                        <p>Contact Support</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" onclick="showSystemInfo()" class="nav-link">
                                        <i class="fas fa-info-circle nav-icon"></i>
                                        <p>System Info</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0 text-dark font-weight-bold">{% block page_title %}Admin Dashboard{% endblock %}</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="{{ url_for('admin.admin_dashboard_adminlte') }}">Home</a></li>
                                {% block breadcrumb %}{% endblock %}
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} mr-2"></i>
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>Copyright &copy; 2024 <a href="#" class="text-primary">Document Management System</a>.</strong>
            All rights reserved.
            <div class="float-right d-none d-sm-inline-block">
                <b>Version</b> 1.0.0 | <small class="text-muted">AdminLTE v3.2.0</small>
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="{{ url_for('static', filename='adminlte/dist/js/adminlte.min.js') }}"></script>
    
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Utilities -->
    <script src="{{ url_for('static', filename='js/utilities.js') }}"></script>
    
    <!-- Enhanced AdminLTE Scripts -->
    <script>
        $(document).ready(function() {
            console.log('AdminLTE Enhanced Script loaded');
            console.log('jQuery version:', $.fn.jquery);
            console.log('AdminLTE loaded:', typeof AdminLTE !== 'undefined' ? 'Yes' : 'No');
            
            // Initialize AdminLTE with enhanced features
            if (typeof AdminLTE !== 'undefined') {
                AdminLTE.init();
                console.log('AdminLTE initialized successfully');
            }
            
            // Enhanced sidebar functionality
            initializeSidebar();
            
            // Enhanced responsive behavior
            handleResponsiveLayout();
            
            // Add loading states
            addLoadingStates();
            
            // Add accessibility features
            addAccessibilityFeatures();
        });
        
        function initializeSidebar() {
            // AdminLTE handles the treeview automatically with proper markup
            // We just need to ensure smooth animations and proper state management
            
            // Add smooth scrolling to active items
            const activeItem = $('.nav-sidebar .nav-link.active');
            if (activeItem.length > 0) {
                setTimeout(() => {
                    activeItem[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }, 500);
            }
            
            // Add hover effects for better UX
            $('.nav-sidebar .nav-link').hover(
                function() {
                    $(this).find('.nav-icon').addClass('animated-icon');
                },
                function() {
                    $(this).find('.nav-icon').removeClass('animated-icon');
                }
            );
            
            // Enhanced pushmenu functionality
            $('[data-widget="pushmenu"]').on('click', function(e) {
                e.preventDefault();
                
                // Add loading state
                $(this).addClass('loading');
                
                setTimeout(() => {
                    $(this).removeClass('loading');
                }, 300);
            });
        }
        
        function handleResponsiveLayout() {
            // Enhanced mobile responsiveness
            function checkScreenSize() {
                if ($(window).width() <= 767) {
                    // Mobile layout adjustments
                    $('body').addClass('mobile-layout');
                    
                    // Close sidebar when clicking on content
                    $('.content-wrapper').on('click.mobile', function(e) {
                        if ($('body').hasClass('sidebar-open')) {
                            $('body').removeClass('sidebar-open');
                        }
                    });
                } else {
                    // Desktop layout
                    $('body').removeClass('mobile-layout');
                    $('.content-wrapper').off('click.mobile');
                }
            }
            
            // Check on load and resize
            checkScreenSize();
            $(window).on('resize', debounce(checkScreenSize, 250));
        }
        
        function addLoadingStates() {
            // Add loading states to navigation links
            $('.nav-sidebar .nav-link[href]:not([href="#"])').on('click', function() {
                const $link = $(this);
                const $icon = $link.find('.nav-icon');
                
                // Don't add loading to external links
                if ($link.attr('target') === '_blank') return;
                
                // Add loading state
                $link.addClass('loading');
                $icon.addClass('fa-spin');
                
                // Remove loading state after navigation (fallback)
                setTimeout(() => {
                    $link.removeClass('loading');
                    $icon.removeClass('fa-spin');
                }, 2000);
            });
        }
        
        function addAccessibilityFeatures() {
            // Add ARIA labels and keyboard navigation
            $('.nav-sidebar .nav-link').each(function() {
                const $link = $(this);
                const text = $link.find('p').text().trim();
                
                if (text) {
                    $link.attr('aria-label', text);
                }
                
                // Add keyboard navigation
                $link.on('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        $(this)[0].click();
                    }
                });
            });
            
            // Add focus indicators
            $('.nav-sidebar .nav-link').on('focus', function() {
                $(this).addClass('keyboard-focus');
            }).on('blur', function() {
                $(this).removeClass('keyboard-focus');
            });
        }
        
        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Enhanced system info function
        function showSystemInfo() {
            const systemInfo = {
                'User Agent': navigator.userAgent,
                'Screen Resolution': screen.width + 'x' + screen.height,
                'Window Size': $(window).width() + 'x' + $(window).height(),
                'jQuery Version': $.fn.jquery,
                'AdminLTE Status': typeof AdminLTE !== 'undefined' ? 'Loaded' : 'Not Loaded',
                'Current Time': new Date().toLocaleString()
            };
            
            let infoText = 'System Information:\n\n';
            for (const [key, value] of Object.entries(systemInfo)) {
                infoText += `${key}: ${value}\n`;
            }
            
            alert(infoText);
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html> 
</html> 
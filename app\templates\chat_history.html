{% extends "admin_base.html" %}

{% block title %}Chat History{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <style>
        /* Style for cover images */
        .cover-image {
            order: -1;
            border: 2px solid #0d6efd; /* Bootstrap primary */
            border-radius: 0.5rem;
            padding: 0.25rem;
            background-color: #e7f1ff; /* Bootstrap light */
        }
        .document-thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }
        /* Markdown content contrast fixes */
        .markdown-content p,
        .markdown-content li,
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            color: #212529 !important;
        }
        em {
            font-style: italic;
            font-weight: normal;
        }
        .markdown-content em {
            font-style: italic;
            color: inherit;
        }
        /* Dark mode customizations (if needed, handled by Bootstrap 5 utilities) */
    </style>
    <script>
        marked.setOptions({
            breaks: true,
            gfm: true,
            headerIds: false,
            mangle: false,
            sanitize: false,
            renderer: (function() {
                const renderer = new marked.Renderer();
                renderer.link = function(href, title, text) {
                    const link = marked.Renderer.prototype.link.call(this, href, title, text);
                    return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="link-primary" ');
                };
                return renderer;
            })()
        });
    </script>
{% endblock %}

{% block content %}
    <div class="bg-white rounded shadow p-4">
        <h1 class="h4 fw-bold text-dark mb-4">Chat History</h1>
        {% if history %}
            <div class="d-flex flex-column gap-4">
                {% for entry in history %}
                    {% set entry_index = loop.index %}
                    <div class="bg-light rounded p-4 border mb-3">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <span class="badge bg-primary text-light me-2">{{ entry.category }}</span>
                                {% if entry.client_name %}
                                <span class="badge bg-success text-light me-2">{{ entry.client_name }}</span>
                                {% endif %}
                                <span class="text-muted small">{{ entry.timestamp }}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <h3 class="h6 fw-semibold text-dark mb-2">
                                {% if entry.client_name %}
                                Question from {{ entry.client_name }}:
                                {% else %}
                                Question:
                                {% endif %}
                            </h3>
                            <div class="bg-primary bg-opacity-10 p-3 rounded border border-primary-subtle">
                                {{ entry.question }}
                            </div>
                        </div>
                        {% if entry.document_thumbnails %}
                            <div class="mb-3">
                                <h3 class="h6 fw-semibold text-dark mb-2">Document Thumbnails:</h3>
                                <div class="document-thumbnails-container">
                                    {% for img in entry.document_thumbnails %}
                                        {% if img.startswith('<div class="image-container">') or img.startswith('<div class="image-container cover-image">') %}
                                            <div id="doc-thumbnail-{{ entry_index }}-{{ loop.index }}"></div>
                                            <script>
                                                document.getElementById('doc-thumbnail-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                            </script>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        {% if entry.url_images %}
                            <div class="mb-3">
                                <h3 class="h6 fw-semibold text-dark mb-2">URL Images:</h3>
                                <div class="row g-3">
                                    {% for img in entry.url_images %}
                                        {% if img.startswith('<div class="image-container">') %}
                                            <div id="url-image-{{ entry_index }}-{{ loop.index }}"></div>
                                            <script>
                                                document.getElementById('url-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                            </script>
                                        {% elif img is string and img.startswith('http') and "'" not in img and "{" not in img and "}" not in img %}
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from URL" class="img-fluid rounded shadow-sm" />
                                                    </a>
                                                    <div class="small text-muted mt-1">Image from URL</div>
                                                </div>
                                            </div>
                                        {% elif img is string %}
                                            <div class="col-12">
                                                <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="link-primary">{{ img }}</a>
                                            </div>
                                        {% else %}
                                            <div class="col-12 text-danger small">Invalid image URL format</div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <div class="mb-3">
                            <h3 class="h6 fw-semibold text-dark mb-2">Answer:</h3>
                            <div class="bg-white p-3 rounded border prose max-w-none">
                                {% if entry.client_name %}
                                    <p class="fw-medium text-success">Hello {{ entry.client_name }},</p>
                                {% endif %}
                                <div class="markdown-content">
                                    {{ entry.answer|markdown|safe }}
                                </div>
                                {% if not entry.answer or entry.answer.strip() == '' %}
                                <p class="text-danger">No answer content available</p>
                                {% endif %}
                            </div>
                        </div>
                        {% if entry.sources %}
                            <div class="mb-3">
                                <h3 class="h6 fw-semibold text-dark mb-2">Sources:</h3>
                                <div class="bg-white p-3 rounded border">
                                    <div class="d-flex flex-column gap-2">
                                        {% set grouped_sources = {} %}
                                        {# Group sources by display_name #}
                                        {% for source in entry.sources %}
                                            {% if source is not string %}
                                                {% set display_name = source.display_name|default(source.source|default('Unknown')) %}
                                                {% if display_name not in grouped_sources %}
                                                    {% set _ = grouped_sources.update({
                                                        display_name: {
                                                            'type': source.type|default('unknown'),
                                                            'pages': [],
                                                            'file_path': source.file_path,
                                                            'original_url': source.original_url,
                                                            'form_id': source.form_id,
                                                            'image_count': 0,
                                                            'link_count': 0,
                                                            'sources': []
                                                        }
                                                    }) %}
                                                {% endif %}
                                                {% if source.page and source.page not in grouped_sources[display_name].pages %}
                                                    {% set _ = grouped_sources[display_name].pages.append(source.page) %}
                                                {% endif %}
                                                {% set _ = grouped_sources[display_name].update({
                                                    'image_count': grouped_sources[display_name].image_count + (source.image_count|default(0)),
                                                    'link_count': grouped_sources[display_name].link_count + (source.link_count|default(0))
                                                }) %}
                                                {% set _ = grouped_sources[display_name].sources.append(source) %}
                                            {% endif %}
                                        {% endfor %}
                                        {# Display grouped sources #}
                                        {% for source_name, source_group in grouped_sources|dictsort %}
                                            <div class="bg-light rounded p-2 border mb-1">
                                                <div class="d-flex flex-wrap align-items-center gap-2">
                                                    <div class="fw-medium">
                                                        {% if source_group.type == 'pdf' %}
                                                            <a href="/download_gated/{{ source_group.sources[0].source }}" target="_blank" rel="noopener noreferrer" class="link-primary fw-medium" title="View PDF">{{ source_name }}</a>
                                                        {% elif source_group.type == 'url' %}
                                                            {% if source_group.original_url %}
                                                                <a href="{{ source_group.original_url }}" target="_blank" rel="noopener noreferrer" class="link-primary fw-medium" title="{{ source_group.original_url }}">{{ source_name }}</a>
                                                            {% elif source_name.startswith('http') %}
                                                                <a href="{{ source_name }}" target="_blank" rel="noopener noreferrer" class="link-primary fw-medium" title="{{ source_name }}">{{ source_name }}</a>
                                                            {% else %}
                                                                {{ source_name }}
                                                            {% endif %}
                                                        {% else %}
                                                            {{ source_name }}
                                                        {% endif %}
                                                    </div>
                                                    <span class="badge bg-secondary">{{ source_group.type }}</span>
                                                    {% if source_group.image_count > 0 %}
                                                        <span class="badge bg-primary ms-1">{{ source_group.image_count }} images</span>
                                                    {% endif %}
                                                    {% if source_group.link_count > 0 %}
                                                        <span class="badge bg-success ms-1">{{ source_group.link_count }} links</span>
                                                    {% endif %}
                                                </div>
                                                {% if source_group.pages and source_group.pages|length > 0 %}
                                                    <div class="small text-muted mt-1">Pages: {{ source_group.pages|sort|join(', ') }}</div>
                                                {% endif %}
                                            </div>
                                        {% endfor %}
                                        {# Display string sources if any #}
                                        {% for source in entry.sources %}
                                            {% if source is string %}
                                                <div class="bg-light rounded p-2 border mb-1">
                                                    <div class="small text-dark">{{ source }}</div>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        {% if entry.pdf_images %}
                            <div class="mb-3">
                                <h3 class="h6 fw-semibold text-dark mb-2">Images from PDFs:</h3>
                                <div class="row g-3">
                                    {% for img in entry.pdf_images %}
                                        {% if img.startswith('<div class="image-container">') %}
                                            <div id="pdf-image-{{ entry_index }}-{{ loop.index }}"></div>
                                            <script>
                                                document.getElementById('pdf-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                            </script>
                                        {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from PDF" class="img-fluid rounded shadow-sm" />
                                                    </a>
                                                    <div class="small text-muted mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            </div>
                                        {% elif img is string %}
                                            <div class="col-12">
                                                <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="link-primary">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                            </div>
                                        {% else %}
                                            <div class="col-12 text-danger small">Invalid image URL format</div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% elif entry.images %}
                            <div class="mb-3">
                                <h3 class="h6 fw-semibold text-dark mb-2">Images:</h3>
                                <div class="row g-3">
                                    {% for img in entry.images %}
                                        {% if img.startswith('<div class="image-container">') %}
                                            <div id="fallback-image-{{ entry_index }}-{{ loop.index }}"></div>
                                            <script>
                                                document.getElementById('fallback-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                            </script>
                                        {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image" class="img-fluid rounded shadow-sm" />
                                                    </a>
                                                    <div class="small text-muted mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            </div>
                                        {% elif img is string %}
                                            <div class="col-12">
                                                <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="link-primary">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                            </div>
                                        {% else %}
                                            <div class="col-12 text-danger small">Invalid image URL format</div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        {% if entry.pdf_links %}
                            <div>
                                <h3 class="h6 fw-semibold text-dark mb-2">Document Links:</h3>
                                <div class="bg-white p-3 rounded border">
                                    <ul class="ps-3 mb-0">
                                        {% for link in entry.pdf_links %}
                                            <li class="small">
                                                <a href="{{ link }}" target="_blank" rel="noopener noreferrer" class="link-primary">{{ link }}</a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-warning d-flex align-items-center" role="alert">
                <svg class="bi flex-shrink-0 me-2" width="24" height="24" role="img" aria-label="Warning:"><use xlink:href="#exclamation-triangle-fill"/></svg>
                <div>
                    No chat history found. Start a conversation to see history here.
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}
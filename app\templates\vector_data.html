{% extends "admin_base.html" %}

{% block title %}Vector Data for {{ filename }}{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">

    <!-- utilities.js is already included in admin_base.html -->

    <!-- Include dark-mode.css for consistent dark mode styling -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* Dark mode styles for vector data page */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #1f2937 !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* Info alert in dark mode */
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .border-blue-500 { border-color: #3b82f6 !important; }
        .dark .text-blue-700 { color: #93c5fd !important; }
        .dark .text-blue-500 { color: #60a5fa !important; }

        /* Warning alert in dark mode */
        .dark .bg-yellow-50 { background-color: #92400e !important; }
        .dark .border-yellow-400 { border-color: #f59e0b !important; }
        .dark .text-yellow-700 { color: #fcd34d !important; }
        .dark .text-yellow-400 { color: #fbbf24 !important; }

        /* Score badge in dark mode */
        .dark .bg-blue-100 { background-color: #1e40af !important; }
        .dark .text-blue-800 { color: #bfdbfe !important; }

        /* Form elements in dark mode */
        .dark select {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
            color: #f3f4f6 !important;
        }

        .dark select:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
        }

        /* Code blocks in dark mode */
        .dark pre {
            background-color: #111827 !important;
            color: #f3f4f6 !important;
        }

        /* Prism.js dark theme override */
        .dark .language-json {
            background-color: #111827 !important;
        }

        .dark .token.property { color: #60a5fa !important; }
        .dark .token.string { color: #34d399 !important; }
        .dark .token.number { color: #fbbf24 !important; }
        .dark .token.boolean { color: #f87171 !important; }
        .dark .token.null { color: #9ca3af !important; }
        .dark .token.punctuation { color: #d1d5db !important; }

        /* Hover effects in dark mode */
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Link colors in dark mode */
        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .hover\:underline:hover { text-decoration: underline !important; }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Vector Data for {{ filename }}</h1>
            <div class="flex space-x-4 items-center">
                <a href="{{ url_for('list_files') }}" class="text-blue-600 dark:text-blue-400 hover:underline">&larr; Back to Files</a>
                <a href="{{ url_for('admin.admin_dashboard') }}" class="text-blue-600 dark:text-blue-400 hover:underline">Admin Dashboard</a>
                <button id="theme-toggle" class="ml-2 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <span id="theme-icon" class="text-xl">🌙</span>
                </button>
            </div>
        </div>

            <div class="bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-500 dark:border-blue-400 p-4 mb-6 rounded">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            Viewing vector data for <span class="font-semibold">{{ filename }}</span> in category <span class="font-semibold">{{ category }}</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="mb-4 flex justify-between items-center">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Showing all {{ vector_data|length }} chunks
                </div>
                <div>
                    <select id="chunkSelector" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400" onchange="scrollToChunk(this.value)">
                        <option value="">Jump to chunk...</option>
                        {% for item in vector_data %}
                            <option value="chunk-{{ loop.index }}">Chunk {{ loop.index }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="space-y-6" id="chunk-list">
                {% for item in vector_data %}
                    <div id="chunk-{{ loop.index }}" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100">Chunk {{ loop.index }}</h3>
                            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs font-semibold rounded-full">
                                Score: {{ "%.4f" | format(item.score) }}
                            </span>
                        </div>
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">Content:</h4>
                            <div class="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600 max-h-60 overflow-y-auto">
                                <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ item.content }}</pre>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">Metadata:</h4>
                            <div class="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                                <pre class="language-json text-sm"><code>{{ item.metadata | prettyjson }}</code></pre>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            {% if vector_data %}
                {% else %}
                <div class="bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 dark:border-yellow-500 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700 dark:text-yellow-300">
                                No vector data found for this file. This might indicate an issue with the embedding process.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
{% endblock %}

{% block scripts %}
    <script>
        // Initialize theme and functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();

            // Add theme toggle button event listener
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');
                    DMSUtils.toggleDarkMode(!isDarkMode);

                    // Update Prism.js syntax highlighting for new theme
                    setTimeout(() => {
                        Prism.highlightAll();
                    }, 100);
                });
            }

            // Set initial theme icon based on current theme
            const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                              document.documentElement.classList.contains('dark');
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = isDarkMode ? '☀️' : '🌙';
            }

            // Highlight JSON syntax
            Prism.highlightAll();
        });

        // Function to scroll to a specific chunk
        function scrollToChunk(chunkId) {
            if (!chunkId) return;
            const element = document.getElementById(chunkId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                // Add a highlight effect with theme-aware colors
                const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                  document.documentElement.classList.contains('dark');

                if (isDarkMode) {
                    element.classList.add('ring-2', 'ring-blue-400');
                } else {
                    element.classList.add('ring-2', 'ring-blue-500');
                }

                setTimeout(() => {
                    element.classList.remove('ring-2', 'ring-blue-500', 'ring-blue-400');
                }, 1500);
            }
        }
    </script>
{% endblock %}
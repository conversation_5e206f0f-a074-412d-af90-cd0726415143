#!/usr/bin/env python3
"""
Simple Enhanced Metadata Extraction Test

This script demonstrates the enhanced metadata extraction capabilities
by using the existing embedding service which automatically integrates
the enhanced metadata extraction.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.embedding_service import embed_file_task
from app.services.semantic_chunking_service import create_semantic_chunking_service
from langchain.schema import Document

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_enhanced_metadata_with_embedding_service():
    """Test enhanced metadata extraction through the embedding service."""
    
    # Create a simple test document
    test_content = """
# EFFECTS OF CLIMATE CHANGE ON FOREST ECOSYSTEMS

**Authors: <AUTHORS>

**Affiliations:** 
- Department of Environmental Science, University of California
- Institute of Forest Research, Stanford University
- Center for Climate Studies, MIT

**Abstract:** This study investigates the long-term effects of climate change on forest ecosystems in North America. We analyzed data from 50 forest plots over a 20-year period to understand how temperature and precipitation changes affect biodiversity and ecosystem health.

**Keywords:** climate change, forest ecosystems, biodiversity, temperature, precipitation

**Introduction:** Climate change represents one of the most significant challenges facing forest ecosystems today. Our research focuses on understanding the mechanisms by which climate change affects forest biodiversity and ecosystem function.

**Methods:** We conducted field surveys across 50 forest plots in North America, collecting data on species composition, biomass, and environmental variables over a 20-year period.

**Results:** Our analysis revealed significant changes in forest composition, with shifts in species distributions and changes in ecosystem productivity.

**Discussion:** The findings suggest that climate change is having profound effects on forest ecosystems, with implications for biodiversity conservation and ecosystem management.

**References:**
1. Smith, J. et al. (2020). Climate Change and Forests. Nature, 45(2), 123-145.
2. Brown, A. et al. (2019). Ecosystem Responses. Science, 78(4), 234-256.

**DOI:** 10.1234/forest.2023.001
**Published:** 2023
**Journal:** Journal of Environmental Science
**Volume:** 15, Issue: 3
"""
    
    # Create a test document
    test_document = Document(
        page_content=test_content,
        metadata={'source': 'test_document.txt', 'type': 'research_article'}
    )
    
    logger.info("Testing enhanced metadata extraction with embedding service")
    logger.info("=" * 80)
    
    try:
        # Step 1: Test semantic chunking service directly
        logger.info("Step 1: Testing semantic chunking service directly...")
        
        semantic_chunker = create_semantic_chunking_service(
            strategy="hybrid",
            chunk_size=800,
            chunk_overlap=160,
            extract_article_metadata=True,
            extract_publication_info=True,
            validate_metadata=True,
            confidence_threshold=0.7,
            enable_cross_reference=True
        )
        
        # Extract metadata
        article_metadata = semantic_chunker.extract_article_metadata([test_document])
        
        # Display results
        print(f"\n📄 Test Document: Enhanced Metadata Extraction Results")
        print(f"🔍 Extraction Method: {article_metadata.extraction_method}")
        
        print(f"\n📝 Title: {article_metadata.title}")
        print(f"👥 Authors ({len(article_metadata.authors)}):")
        for i, author in enumerate(article_metadata.authors, 1):
            print(f"   {i}. {author}")
        
        print(f"\n🏢 Affiliations ({len(article_metadata.affiliations)}):")
        for i, affiliation in enumerate(article_metadata.affiliations, 1):
            print(f"   {i}. {affiliation}")
        
        if article_metadata.abstract:
            print(f"\n📋 Abstract Preview:")
            abstract_preview = article_metadata.abstract[:200] + "..." if len(article_metadata.abstract) > 200 else article_metadata.abstract
            print(f"   {abstract_preview}")
        
        print(f"\n🏷️ Keywords ({len(article_metadata.keywords)}):")
        for i, keyword in enumerate(article_metadata.keywords[:10], 1):
            print(f"   {i}. {keyword}")
        if len(article_metadata.keywords) > 10:
            print(f"   ... and {len(article_metadata.keywords) - 10} more")
        
        print(f"\n📚 Publication Information:")
        for key, value in article_metadata.publication_info.items():
            print(f"   {key}: {value}")
        
        print(f"\n🎯 Confidence Scores:")
        for key, value in article_metadata.confidence_scores.items():
            confidence_bar = "█" * int(value * 10) + "░" * (10 - int(value * 10))
            print(f"   {key}: {confidence_bar} {value:.3f}")
        
        # Step 2: Test chunking with enhanced metadata
        logger.info("Step 2: Testing chunking with enhanced metadata...")
        
        chunks = semantic_chunker.chunk_documents([test_document])
        
        # Count chunks with enhanced metadata
        chunks_with_metadata = 0
        for chunk in chunks:
            if any(key in chunk.metadata for key in ['article_title', 'article_authors', 'article_abstract']):
                chunks_with_metadata += 1
        
        print(f"\n📦 Chunk Processing Results:")
        print(f"   Total Chunks: {len(chunks)}")
        print(f"   Chunks with Enhanced Metadata: {chunks_with_metadata}")
        print(f"   Metadata Coverage: {chunks_with_metadata/len(chunks)*100:.1f}%" if chunks else "N/A")
        
        # Step 3: Show sample chunks with metadata
        print(f"\n📋 Sample Chunks with Enhanced Metadata:")
        for i, chunk in enumerate(chunks[:3], 1):  # Show first 3 chunks
            print(f"\n   Chunk {i}:")
            print(f"   Content Preview: {chunk.page_content[:100]}...")
            print(f"   Metadata Keys: {list(chunk.metadata.keys())}")
            
            # Show enhanced metadata if present
            if 'article_title' in chunk.metadata:
                print(f"   Article Title: {chunk.metadata['article_title']}")
            if 'article_authors' in chunk.metadata:
                print(f"   Article Authors: <AUTHORS>
            if 'article_abstract' in chunk.metadata:
                abstract = chunk.metadata['article_abstract']
                print(f"   Article Abstract: {abstract[:100]}..." if len(abstract) > 100 else f"   Article Abstract: {abstract}")
        
        # Step 4: Save results
        results = {
            'test_timestamp': datetime.now().isoformat(),
            'test_document': 'synthetic_research_article',
            'total_chunks': len(chunks),
            'chunks_with_metadata': chunks_with_metadata,
            'metadata_coverage': chunks_with_metadata/len(chunks)*100 if chunks else 0,
            'article_metadata': {
                'title': article_metadata.title,
                'authors': article_metadata.authors,
                'affiliations': article_metadata.affiliations,
                'abstract': article_metadata.abstract,
                'keywords': article_metadata.keywords,
                'publication_info': article_metadata.publication_info,
                'confidence_scores': article_metadata.confidence_scores,
                'extraction_method': article_metadata.extraction_method
            },
            'sample_chunks': []
        }
        
        # Add sample chunks with metadata
        for i, chunk in enumerate(chunks[:5]):
            chunk_info = {
                'chunk_id': i + 1,
                'content_preview': chunk.page_content[:100] + "..." if len(chunk.page_content) > 100 else chunk.page_content,
                'metadata': {k: v for k, v in chunk.metadata.items() if k.startswith('article_') or k in ['publication_info', 'metadata_confidence']}
            }
            results['sample_chunks'].append(chunk_info)
        
        # Save results to file
        output_file = f"enhanced_metadata_simple_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        # Step 5: Summary and recommendations
        print(f"\n🎯 Summary:")
        avg_confidence = sum(article_metadata.confidence_scores.values()) / len(article_metadata.confidence_scores) if article_metadata.confidence_scores else 0
        print(f"   Average Confidence: {avg_confidence:.3f}")
        print(f"   Metadata Extraction: {'✅ SUCCESS' if article_metadata.title or article_metadata.authors else '❌ FAILED'}")
        print(f"   Chunk Integration: {'✅ SUCCESS' if chunks_with_metadata > 0 else '❌ FAILED'}")
        
        if avg_confidence < 0.5:
            print(f"\n⚠️  Recommendations:")
            print(f"   - Consider adjusting confidence threshold")
            print(f"   - Check document format and structure")
            print(f"   - Verify that document contains clear title/author information")
        
        logger.info("Enhanced metadata extraction test completed successfully!")
        
        return results
        
    except Exception as e:
        logger.error(f"Error during enhanced metadata extraction test: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_enhanced_metadata_integration():
    """Test that the enhanced metadata extraction is properly integrated."""
    
    logger.info("Testing enhanced metadata extraction integration")
    logger.info("=" * 80)
    
    try:
        # Create semantic chunking service
        semantic_chunker = create_semantic_chunking_service(
            strategy="hybrid",
            chunk_size=800,
            chunk_overlap=160,
            extract_article_metadata=True,
            extract_publication_info=True,
            validate_metadata=True,
            confidence_threshold=0.7,
            enable_cross_reference=True
        )
        
        # Test with different document types
        test_documents = [
            {
                'name': 'Scientific Paper',
                'content': """
# MACHINE LEARNING APPLICATIONS IN BIODIVERSITY CONSERVATION

Authors: <AUTHORS>
Department of Computer Science, Harvard University
Department of Biology, Yale University

Abstract: We present a novel machine learning approach for biodiversity conservation...

Keywords: machine learning, biodiversity, conservation, AI

1. INTRODUCTION
Biodiversity conservation is critical for ecosystem health...

2. METHODOLOGY
Our approach combines deep learning with ecological data...

3. RESULTS
The model achieved 85% accuracy in species identification...

4. CONCLUSION
Machine learning shows promise for conservation efforts...

References:
[1] Garcia, M. et al. (2022). ML in Conservation. ICML Proceedings.
[2] Wilson, J. (2021). AI for Ecology. AAAI Conference.

Conference: International Conference on Machine Learning 2023
Date: July 15-21, 2023
Location: Honolulu, Hawaii
"""
            },
            {
                'name': 'Technical Report',
                'content': """
TECHNICAL REPORT: IMPLEMENTATION OF SEMANTIC CHUNKING FOR DOCUMENT PROCESSING

Prepared by: Dr. Robert Kim, Senior Research Scientist
Technical Lead: Lisa Thompson, PhD
Contributors: David Park, Jennifer Lee

Organization: Advanced Research Institute
Department: Natural Language Processing Lab
Date: December 2023

Executive Summary:
This report presents the implementation and evaluation of semantic chunking...

1. Background
Document processing systems traditionally rely on character-based chunking...

2. Implementation
We developed a hybrid semantic chunking pipeline...

3. Results
The semantic chunking approach improved retrieval accuracy by 40%...

4. Recommendations
Based on our findings, we recommend...

Appendix A: Technical Specifications
Appendix B: Performance Metrics
Appendix C: Code Implementation

Contact: <EMAIL>
Project Code: SC-2023-001
"""
            }
        ]
        
        all_results = []
        
        for test_doc in test_documents:
            logger.info(f"Testing: {test_doc['name']}")
            
            document = Document(
                page_content=test_doc['content'],
                metadata={'source': f"{test_doc['name'].lower().replace(' ', '_')}.txt", 'type': 'test_document'}
            )
            
            # Extract metadata
            article_metadata = semantic_chunker.extract_article_metadata([document])
            
            # Process chunks
            chunks = semantic_chunker.chunk_documents([document])
            
            # Count chunks with metadata
            chunks_with_metadata = 0
            for chunk in chunks:
                if any(key in chunk.metadata for key in ['article_title', 'article_authors', 'article_abstract']):
                    chunks_with_metadata += 1
            
            result = {
                'document_type': test_doc['name'],
                'title': article_metadata.title,
                'authors': article_metadata.authors,
                'affiliations': article_metadata.affiliations,
                'abstract': article_metadata.abstract,
                'keywords': article_metadata.keywords,
                'publication_info': article_metadata.publication_info,
                'confidence_scores': article_metadata.confidence_scores,
                'total_chunks': len(chunks),
                'chunks_with_metadata': chunks_with_metadata,
                'metadata_coverage': chunks_with_metadata/len(chunks)*100 if chunks else 0
            }
            
            all_results.append(result)
            
            print(f"\n📄 {test_doc['name']}:")
            print(f"   Title: {article_metadata.title}")
            print(f"   Authors: <AUTHORS>
            print(f"   Affiliations: {len(article_metadata.affiliations)} found")
            print(f"   Abstract: {'✅ Found' if article_metadata.abstract else '❌ Not found'}")
            print(f"   Keywords: {len(article_metadata.keywords)} found")
            print(f"   Chunks: {len(chunks)} total, {chunks_with_metadata} with metadata")
            print(f"   Coverage: {chunks_with_metadata/len(chunks)*100:.1f}%" if chunks else "N/A")
        
        # Save integration test results
        integration_results = {
            'test_timestamp': datetime.now().isoformat(),
            'test_type': 'integration_test',
            'results': all_results
        }
        
        output_file = f"enhanced_metadata_integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(integration_results, f, indent=2, default=str)
        
        print(f"\n💾 Integration test results saved to: {output_file}")
        
        logger.info("Enhanced metadata extraction integration test completed successfully!")
        
        return integration_results
        
    except Exception as e:
        logger.error(f"Error during integration test: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main function to run the enhanced metadata extraction tests."""
    print("Enhanced Metadata Extraction Simple Test")
    print("=" * 50)
    print("This script demonstrates the enhanced metadata extraction")
    print("capabilities using the semantic chunking service.")
    print()
    
    try:
        # Test 1: Basic functionality
        print("Test 1: Basic Enhanced Metadata Extraction")
        print("-" * 40)
        results1 = test_enhanced_metadata_with_embedding_service()
        
        print("\n" + "=" * 60)
        
        # Test 2: Integration test
        print("Test 2: Integration Test with Multiple Document Types")
        print("-" * 40)
        results2 = test_enhanced_metadata_integration()
        
        print("\n✅ All tests completed successfully!")
        
        if results1 and results2:
            print(f"\n📊 Summary:")
            print(f"   - Basic test: {'✅ PASSED' if results1 else '❌ FAILED'}")
            print(f"   - Integration test: {'✅ PASSED' if results2 else '❌ FAILED'}")
            print(f"   - Enhanced metadata extraction is working correctly!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"\n❌ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 
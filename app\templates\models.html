<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Models</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <style>
        :root {
            --erdb-primary: #378C47;
            --erdb-secondary: #0267B6;
            --erdb-light-green: #5BA85B;
            --erdb-light-blue: #3CA6D6;
            --erdb-orange: #FFBD5C;
            --erdb-red: #C12323;
        }

        .model-card {
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
            cursor: pointer;
        }

        .model-card:hover {
            border-color: var(--erdb-light-blue);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .model-card.selected {
            border-color: var(--erdb-primary);
            background-color: #f8f9fa;
        }

        .model-card.default-model {
            border-color: var(--erdb-secondary);
            background-color: #e3f2fd;
        }

        .model-card.default-model.selected {
            border-color: var(--erdb-primary);
            background-color: #e8f5e8;
        }

        .default-badge {
            background-color: var(--erdb-secondary);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }

        .system-active-badge {
            background-color: var(--erdb-primary);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }

        .selected-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 1rem;
            height: 1rem;
            background-color: var(--erdb-primary);
            border-radius: 50%;
            display: none;
        }

        .model-card.selected .selected-indicator {
            display: block;
        }

        .info-alert {
            background-color: #e3f2fd;
            border-left: 4px solid var(--erdb-light-blue);
            color: #1565c0;
        }

        .btn-erdb-primary {
            background-color: var(--erdb-primary);
            border-color: var(--erdb-primary);
            color: white;
        }

        .btn-erdb-primary:hover {
            background-color: var(--erdb-light-green);
            border-color: var(--erdb-light-green);
            color: white;
        }

        .btn-erdb-secondary {
            background-color: var(--erdb-secondary);
            border-color: var(--erdb-secondary);
            color: white;
        }

        .btn-erdb-secondary:hover {
            background-color: var(--erdb-light-blue);
            border-color: var(--erdb-light-blue);
            color: white;
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <div class="card shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h3 mb-0 text-dark fw-bold">
                                <i class="bi bi-cpu me-2"></i>Manage Models
                            </h1>
                            <div class="d-flex gap-2">
                                <button id="refreshModels" class="btn btn-erdb-secondary btn-sm">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh Models
                                </button>
                                <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- System Status Alert -->
                        <div class="alert info-alert mb-4" role="alert">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-info-circle-fill me-3 mt-1"></i>
                                <div>
                                    <h6 class="alert-heading mb-2">Model Configuration</h6>
                                    <p class="mb-2">Select the models to use for chat responses, text embeddings, and image analysis. Changes will apply to all new queries.</p>
                                    <div class="row g-2 mt-2">
                                        <div class="col-auto">
                                            <span class="badge default-badge">
                                                <i class="bi bi-star-fill me-1"></i>System Default
                                            </span>
                                        </div>
                                        <div class="col-auto">
                                            <span class="badge system-active-badge">
                                                <i class="bi bi-check-circle-fill me-1"></i>Currently Active
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current System Status -->
                        <div class="card mb-4 border-success">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="bi bi-gear-fill me-2"></i>Current System Configuration
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <i class="bi bi-chat-dots-fill text-primary fs-4 mb-2"></i>
                                            <h6 class="mb-1">LLM Model</h6>
                                            <code class="text-success">{{ selected_model or default_llm }}</code>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <i class="bi bi-vector-pen text-info fs-4 mb-2"></i>
                                            <h6 class="mb-1">Embedding Model</h6>
                                            <code class="text-success">{{ selected_embedding or default_embedding }}</code>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <i class="bi bi-eye-fill text-warning fs-4 mb-2"></i>
                                            <h6 class="mb-1">Vision Model</h6>
                                            <code class="text-success">{{ selected_vision or default_vision }}</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form id="modelForm">
                            <!-- LLM Model Selection -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="bi bi-chat-dots-fill me-2"></i>LLM Model Selection
                                    </h5>
                                    <small class="text-muted">Select the language model that will generate responses to user queries.</small>
                                </div>
                                <div class="card-body">
                                    {% if models %}
                                        <div class="row g-3">
                                            {% for model in models %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="model-card card h-100 position-relative
                                                                {% if model.name == selected_model %}selected{% endif %}
                                                                {% if model.name == default_llm %}default-model{% endif %}"
                                                         onclick="selectModel('llm_{{ loop.index }}')">
                                                        <div class="card-body">
                                                            <input type="radio" id="llm_{{ loop.index }}" name="llm_model"
                                                                   value="{{ model.name }}" class="d-none"
                                                                   {% if model.name == selected_model %}checked{% endif %}>

                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="card-title mb-0 fw-bold">{{ model.name }}</h6>
                                                                <div class="selected-indicator"></div>
                                                            </div>

                                                            <p class="card-text text-muted small mb-2">
                                                                <i class="bi bi-hdd-fill me-1"></i>Size: {{ model.size | filesizeformat }}
                                                            </p>

                                                            <div class="d-flex flex-wrap gap-1">
                                                                {% if model.name == default_llm %}
                                                                    <span class="badge default-badge">
                                                                        <i class="bi bi-star-fill me-1"></i>System Default
                                                                    </span>
                                                                {% endif %}
                                                                {% if model.name == selected_model %}
                                                                    <span class="badge system-active-badge">
                                                                        <i class="bi bi-check-circle-fill me-1"></i>Currently Active
                                                                    </span>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning" role="alert">
                                            <div class="d-flex align-items-start">
                                                <i class="bi bi-exclamation-triangle-fill me-3 mt-1"></i>
                                                <div>
                                                    <h6 class="alert-heading">No Ollama Models Found</h6>
                                                    <p class="mb-2">Please make sure Ollama is running and has models installed.</p>
                                                    <div class="mb-3">
                                                        <strong>Troubleshooting steps:</strong>
                                                        <ol class="mb-0 mt-1">
                                                            <li>Verify that Ollama is running on your system</li>
                                                            <li>Check that Ollama is accessible at http://localhost:11434</li>
                                                            <li>Make sure you have at least one model installed in Ollama</li>
                                                            <li>Try restarting the application after confirming Ollama is running</li>
                                                        </ol>
                                                    </div>
                                                    <button id="useDefaultModels" class="btn btn-warning btn-sm">
                                                        <i class="bi bi-download me-1"></i>Use Default Models
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Embedding Model Selection -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="bi bi-vector-pen me-2"></i>Embedding Model Selection
                                    </h5>
                                    <small class="text-muted">Select the embedding model used to vectorize documents for retrieval.</small>
                                </div>
                                <div class="card-body">
                                    {% if embeddings %}
                                        <div class="row g-3">
                                            {% for embedding in embeddings %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="model-card card h-100 position-relative
                                                                {% if embedding.name == selected_embedding %}selected{% endif %}
                                                                {% if embedding.name == default_embedding %}default-model{% endif %}"
                                                         onclick="selectModel('embed_{{ loop.index }}')">
                                                        <div class="card-body">
                                                            <input type="radio" id="embed_{{ loop.index }}" name="embedding_model"
                                                                   value="{{ embedding.name }}" class="d-none"
                                                                   {% if embedding.name == selected_embedding %}checked{% endif %}>

                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="card-title mb-0 fw-bold">{{ embedding.name }}</h6>
                                                                <div class="selected-indicator"></div>
                                                            </div>

                                                            <p class="card-text text-muted small mb-2">
                                                                <i class="bi bi-hdd-fill me-1"></i>Size: {{ embedding.size | filesizeformat }}
                                                            </p>

                                                            <div class="d-flex flex-wrap gap-1">
                                                                {% if embedding.name == default_embedding %}
                                                                    <span class="badge default-badge">
                                                                        <i class="bi bi-star-fill me-1"></i>System Default
                                                                    </span>
                                                                {% endif %}
                                                                {% if embedding.name == selected_embedding %}
                                                                    <span class="badge system-active-badge">
                                                                        <i class="bi bi-check-circle-fill me-1"></i>Currently Active
                                                                    </span>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning" role="alert">
                                            <div class="d-flex align-items-start">
                                                <i class="bi bi-exclamation-triangle-fill me-3 mt-1"></i>
                                                <div>
                                                    <h6 class="alert-heading">No Embedding Models Found</h6>
                                                    <p class="mb-0">Please make sure Ollama is running and has embedding models installed.</p>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Model Parameters Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="bi bi-sliders me-2"></i>Model Parameters
                                    </h5>
                                    <small class="text-muted">Configure parameters for the LLM model to adjust its behavior.</small>
                                </div>
                                <div class="card-body">
                                    <div class="row g-4 mb-4">
                                        <!-- Temperature -->
                                        <div class="col-md-6">
                                            <label for="temperature" class="form-label fw-semibold">Temperature</label>
                                            <div class="d-flex align-items-center">
                                                <input type="range" id="temperature" name="temperature" min="0" max="1" step="0.05"
                                                       value="{{ temperature|default(0.7) }}" class="form-range me-3">
                                                <span id="temperatureValue" class="badge bg-secondary">{{ temperature|default(0.7) }}</span>
                                            </div>
                                            <small class="text-muted">Controls randomness (0 = deterministic, 1 = creative)</small>
                                        </div>

                                        <!-- Top P -->
                                        <div class="col-md-6">
                                            <label for="top_p" class="form-label fw-semibold">Top P</label>
                                            <div class="d-flex align-items-center">
                                                <input type="range" id="top_p" name="top_p" min="0" max="1" step="0.05"
                                                       value="{{ top_p|default(0.9) }}" class="form-range me-3">
                                                <span id="topPValue" class="badge bg-secondary">{{ top_p|default(0.9) }}</span>
                                            </div>
                                            <small class="text-muted">Controls diversity via nucleus sampling</small>
                                        </div>

                                        <!-- Top K -->
                                        <div class="col-md-6">
                                            <label for="top_k" class="form-label fw-semibold">Top K</label>
                                            <div class="d-flex align-items-center">
                                                <input type="range" id="top_k" name="top_k" min="1" max="100" step="1"
                                                       value="{{ top_k|default(40) }}" class="form-range me-3">
                                                <span id="topKValue" class="badge bg-secondary">{{ top_k|default(40) }}</span>
                                            </div>
                                            <small class="text-muted">Limits vocabulary to top K tokens</small>
                                        </div>

                                        <!-- Repeat Penalty -->
                                        <div class="col-md-6">
                                            <label for="repeat_penalty" class="form-label fw-semibold">Repeat Penalty</label>
                                            <div class="d-flex align-items-center">
                                                <input type="range" id="repeat_penalty" name="repeat_penalty" min="1" max="2" step="0.05"
                                                       value="{{ repeat_penalty|default(1.1) }}" class="form-range me-3">
                                                <span id="repeatPenaltyValue" class="badge bg-secondary">{{ repeat_penalty|default(1.1) }}</span>
                                            </div>
                                            <small class="text-muted">Penalizes repetition (higher = less repetition)</small>
                                        </div>
                                    </div>

                                    <div class="row g-4 mb-4">
                                        <!-- Context Window -->
                                        <div class="col-md-6">
                                            <label for="num_ctx" class="form-label fw-semibold">Context Window Size</label>
                                            <input type="number" id="num_ctx" name="num_ctx" min="512" max="32768" step="512"
                                                   value="{{ num_ctx|default(4096) }}" class="form-control">
                                            <small class="text-muted">Maximum context window size in tokens</small>
                                        </div>

                                        <!-- Max Tokens to Generate -->
                                        <div class="col-md-6">
                                            <label for="num_predict" class="form-label fw-semibold">Max Tokens to Generate</label>
                                            <input type="number" id="num_predict" name="num_predict" min="64" max="4096" step="64"
                                                   value="{{ num_predict|default(256) }}" class="form-control">
                                            <small class="text-muted">Maximum number of tokens to generate</small>
                                        </div>
                                    </div>

                                    <!-- System Prompt -->
                                    <div class="mb-0">
                                        <label for="system_prompt" class="form-label fw-semibold">System Prompt</label>
                                        <textarea id="system_prompt" name="system_prompt" rows="3" class="form-control">{{ system_prompt|default('You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.') }}</textarea>
                                        <small class="text-muted">System instructions for the model</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Vision Model Selection -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="bi bi-eye-fill me-2"></i>Vision Model Selection
                                    </h5>
                                    <small class="text-muted">Select the vision model used for analyzing images in documents.</small>
                                </div>
                                <div class="card-body">
                                    <!-- Vision Model Toggle Switches -->
                                    <div class="mb-4">
                                        <!-- Vision Model for Chat Toggle -->
                                        <div class="card mb-3 border-info">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="form-check form-switch me-3">
                                                        <input class="form-check-input" type="checkbox" id="use_vision" name="use_vision"
                                                               value="true" {% if use_vision %}checked{% endif %}>
                                                        <label class="form-check-label fw-semibold" for="use_vision">
                                                            Enable Vision Model for Chat
                                                        </label>
                                                    </div>
                                                    <div class="ms-auto">
                                                        <small class="text-muted">
                                                            {% if use_vision %}
                                                                <i class="bi bi-check-circle-fill text-success me-1"></i>
                                                                Vision model is enabled for chat responses. Disable to speed up query processing.
                                                            {% else %}
                                                                <i class="bi bi-x-circle-fill text-warning me-1"></i>
                                                                Vision model is disabled for chat responses. Enable for image analysis during queries.
                                                            {% endif %}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Vision Model for Embedding Toggle (Global Control) -->
                                        <div class="card mb-3 border-success">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="form-check form-switch me-3">
                                                        <input class="form-check-input" type="checkbox" id="use_vision_during_embedding"
                                                               name="use_vision_during_embedding" value="true" {% if use_vision_during_embedding %}checked{% endif %}>
                                                        <label class="form-check-label fw-semibold" for="use_vision_during_embedding">
                                                            Enable Vision Model During Embedding
                                                        </label>
                                                    </div>
                                                    <div class="ms-auto">
                                                        <small class="text-muted">
                                                            {% if use_vision_during_embedding %}
                                                                <i class="bi bi-check-circle-fill text-success me-1"></i>
                                                                <strong>GLOBAL SETTING:</strong> Vision model will analyze and filter images during PDF embedding. Disable for faster embedding.
                                                            {% else %}
                                                                <i class="bi bi-x-circle-fill text-warning me-1"></i>
                                                                <strong>GLOBAL SETTING:</strong> Vision model is disabled during embedding. Enable to filter irrelevant images during PDF upload.
                                                            {% endif %}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Explanation of hierarchical relationship -->
                                        <div class="alert alert-info py-2">
                                            <small>
                                                <i class="bi bi-info-circle me-1"></i>
                                                <strong>Note:</strong> The "Enable Vision Model During Embedding" toggle is a global setting that controls whether the vision model option is available during PDF uploads. When disabled, users cannot enable vision analysis for individual uploads.
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Image Filtering Settings -->
                                    <div id="imageFilteringSettings" class="card border-secondary">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="bi bi-funnel me-2"></i>Image Filtering Settings
                                            </h6>
                                            <button type="button" id="toggleFilterSettings" class="btn btn-sm btn-outline-secondary">
                                                <span id="toggleFilterText">Hide Settings</span>
                                                <i class="bi bi-chevron-up ms-1"></i>
                                            </button>
                                        </div>

                                        <div id="filterSettingsContent" class="card-body">
                                            <!-- Enable Image Filtering Toggle -->
                                            <div class="mb-4">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="filter_pdf_images"
                                                           name="filter_pdf_images" value="true" {% if filter_pdf_images %}checked{% endif %}>
                                                    <label class="form-check-label fw-semibold" for="filter_pdf_images">
                                                        Enable Intelligent Image Filtering
                                                    </label>
                                                </div>
                                                <small class="text-muted">
                                                    Automatically filter out decorative images, logos, and irrelevant graphics
                                                </small>
                                            </div>

                                            <!-- Filter Sensitivity -->
                                            <div class="mb-4">
                                                <label class="form-label fw-semibold">Filter Sensitivity</label>
                                                <div class="d-flex flex-wrap gap-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" id="sensitivity_low"
                                                               name="filter_sensitivity" value="low" {% if filter_sensitivity == 'low' %}checked{% endif %}>
                                                        <label class="form-check-label" for="sensitivity_low">
                                                            Low <small class="text-muted">(Keep most images)</small>
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" id="sensitivity_medium"
                                                               name="filter_sensitivity" value="medium" {% if filter_sensitivity == 'medium' or not filter_sensitivity %}checked{% endif %}>
                                                        <label class="form-check-label" for="sensitivity_medium">
                                                            Medium <small class="text-muted">(Balanced)</small>
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" id="sensitivity_high"
                                                               name="filter_sensitivity" value="high" {% if filter_sensitivity == 'high' %}checked{% endif %}>
                                                        <label class="form-check-label" for="sensitivity_high">
                                                            High <small class="text-muted">(Only keep highly relevant images)</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <small class="text-muted">Controls how aggressively to filter out decorative or less relevant images</small>
                                            </div>

                                            <!-- Maximum Images to Analyze -->
                                            <div class="mb-4">
                                                <label for="max_pdf_images" class="form-label fw-semibold">Maximum Images to Analyze</label>
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-auto">
                                                        <input type="number" id="max_pdf_images" name="max_pdf_images"
                                                               min="1" max="50" value="{{ max_pdf_images }}" class="form-control" style="width: 80px;">
                                                    </div>
                                                    <div class="col">
                                                        <small class="text-muted">Higher values may slow down processing</small>
                                                    </div>
                                                </div>
                                                <small class="text-muted">Limits the number of images analyzed per document (1-50)</small>
                                            </div>

                                            <!-- Show Filtered Images Toggle -->
                                            <div class="mb-0">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="show_filtered_images"
                                                           name="show_filtered_images" value="true" {% if show_filtered_images %}checked{% endif %}>
                                                    <label class="form-check-label fw-semibold" for="show_filtered_images">
                                                        Show Filtered Images
                                                    </label>
                                                </div>
                                                <small class="text-muted">
                                                    Display which images were filtered out and why
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    {% if vision_models %}
                                        <div class="row g-3 mt-3">
                                            {% for vision in vision_models %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="model-card card h-100 position-relative
                                                                {% if vision.name == selected_vision %}selected{% endif %}
                                                                {% if vision.name == default_vision %}default-model{% endif %}"
                                                         onclick="selectModel('vision_{{ loop.index }}')">
                                                        <div class="card-body">
                                                            <input type="radio" id="vision_{{ loop.index }}" name="vision_model"
                                                                   value="{{ vision.name }}" class="d-none"
                                                                   {% if vision.name == selected_vision %}checked{% endif %}>

                                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                                <h6 class="card-title mb-0 fw-bold">{{ vision.name }}</h6>
                                                                <div class="selected-indicator"></div>
                                                            </div>

                                                            <p class="card-text text-muted small mb-2">
                                                                <i class="bi bi-hdd-fill me-1"></i>Size: {{ vision.size | filesizeformat }}
                                                            </p>

                                                            <div class="d-flex flex-wrap gap-1">
                                                                {% if vision.name == default_vision %}
                                                                    <span class="badge default-badge">
                                                                        <i class="bi bi-star-fill me-1"></i>System Default
                                                                    </span>
                                                                {% endif %}
                                                                {% if vision.name == selected_vision %}
                                                                    <span class="badge system-active-badge">
                                                                        <i class="bi bi-check-circle-fill me-1"></i>Currently Active
                                                                    </span>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning mt-3" role="alert">
                                            <div class="d-flex align-items-start">
                                                <i class="bi bi-exclamation-triangle-fill me-3 mt-1"></i>
                                                <div>
                                                    <h6 class="alert-heading">No Vision Models Found</h6>
                                                    <p class="mb-0">Please make sure Ollama is running and has vision-capable models installed.</p>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-end gap-3 mt-4">
                                <button type="button" id="saveDefaultsButton" class="btn btn-erdb-primary">
                                    <i class="bi bi-star-fill me-2"></i>Save as Default
                                </button>
                                <button type="submit" class="btn btn-erdb-secondary">
                                    <i class="bi bi-check-circle-fill me-2"></i>Update Models
                                </button>
                            </div>
                        </form>

                        <div id="statusMessage" class="mt-4 d-none"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script>
        // Function to safely get an element by ID
        function safeGetElement(id) {
            return document.getElementById(id);
        }

        // Function to select a model card
        function selectModel(radioId) {
            const radio = document.getElementById(radioId);
            if (radio) {
                radio.checked = true;

                // Update visual states
                updateModelCardStates();

                // Trigger change event for any listeners
                radio.dispatchEvent(new Event('change'));
            }
        }

        // Function to update model card visual states
        function updateModelCardStates() {
            // Update all model cards
            document.querySelectorAll('.model-card').forEach(card => {
                const radio = card.querySelector('input[type="radio"]');
                if (radio && radio.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }

        // Function to check if either vision toggle is enabled
        function isAnyVisionEnabled() {
            const useVisionCheckbox = safeGetElement('use_vision');
            const useVisionDuringEmbeddingCheckbox = safeGetElement('use_vision_during_embedding');

            // If we can't find the checkboxes, default to showing the settings
            if (!useVisionCheckbox && !useVisionDuringEmbeddingCheckbox) {
                console.warn('Vision toggle checkboxes not found, defaulting to show filtering settings');
                return true;
            }

            return (useVisionCheckbox && useVisionCheckbox.checked) ||
                   (useVisionDuringEmbeddingCheckbox && useVisionDuringEmbeddingCheckbox.checked);
        }

        // Function to update filtering settings visibility
        function updateFilteringSettingsVisibility() {
            const filteringSettings = safeGetElement('imageFilteringSettings');
            if (!filteringSettings) {
                console.warn('Image filtering settings element not found');
                return;
            }

            if (isAnyVisionEnabled()) {
                filteringSettings.classList.remove('d-none');
                console.log('Image filtering settings are now visible');
            } else {
                filteringSettings.classList.add('d-none');
                console.log('Image filtering settings are now hidden');
            }
        }

        // Function to update slider value displays
        function updateSliderValueDisplay(sliderId, valueId) {
            const slider = safeGetElement(sliderId);
            const valueDisplay = safeGetElement(valueId);

            if (!slider || !valueDisplay) return;

            // Update initial value
            valueDisplay.textContent = slider.value;

            // Add event listener for changes
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }

        // Function to set up event listeners
        function setupEventListeners() {
            const useVisionCheckbox = safeGetElement('use_vision');
            const useVisionDuringEmbeddingCheckbox = safeGetElement('use_vision_during_embedding');
            const toggleFilterSettingsBtn = safeGetElement('toggleFilterSettings');

            // Add event listeners for vision toggles
            if (useVisionCheckbox) {
                useVisionCheckbox.addEventListener('change', updateFilteringSettingsVisibility);
            }

            if (useVisionDuringEmbeddingCheckbox) {
                useVisionDuringEmbeddingCheckbox.addEventListener('change', updateFilteringSettingsVisibility);
            }

            // Handle filter settings toggle
            if (toggleFilterSettingsBtn) {
                toggleFilterSettingsBtn.addEventListener('click', function() {
                    const filterContent = safeGetElement('filterSettingsContent');
                    const toggleText = safeGetElement('toggleFilterText');
                    const toggleIcon = this.querySelector('i');

                    if (!filterContent || !toggleText) return;

                    if (filterContent.classList.contains('d-none')) {
                        filterContent.classList.remove('d-none');
                        toggleText.textContent = 'Hide Settings';
                        if (toggleIcon) {
                            toggleIcon.className = 'bi bi-chevron-up ms-1';
                        }
                    } else {
                        filterContent.classList.add('d-none');
                        toggleText.textContent = 'Show Settings';
                        if (toggleIcon) {
                            toggleIcon.className = 'bi bi-chevron-down ms-1';
                        }
                    }
                });
            }

            // Set up slider value displays
            updateSliderValueDisplay('temperature', 'temperatureValue');
            updateSliderValueDisplay('top_p', 'topPValue');
            updateSliderValueDisplay('top_k', 'topKValue');
            updateSliderValueDisplay('repeat_penalty', 'repeatPenaltyValue');
        }

        // Initialize when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded, initializing settings');
            setupEventListeners();
            updateFilteringSettingsVisibility();
            updateModelCardStates();

            // Add event listeners to all radio buttons for model selection
            document.querySelectorAll('input[type="radio"][name="llm_model"], input[type="radio"][name="embedding_model"], input[type="radio"][name="vision_model"]').forEach(radio => {
                radio.addEventListener('change', updateModelCardStates);
            });
        });

        // Also run immediately in case DOMContentLoaded has already fired
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('Document already loaded, initializing immediately');
            setupEventListeners();
            updateFilteringSettingsVisibility();
            updateModelCardStates();

            // Add event listeners to all radio buttons for model selection
            document.querySelectorAll('input[type="radio"][name="llm_model"], input[type="radio"][name="embedding_model"], input[type="radio"][name="vision_model"]').forEach(radio => {
                radio.addEventListener('change', updateModelCardStates);
            });
        }
        // Function to handle model updates
        async function updateModels(llmModel, embeddingModel, visionModel) {
            if (!llmModel || !embeddingModel) {
                Toastify({
                    text: "Please select both an LLM model and an embedding model",
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
                return;
            }

            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = `
                <div class="alert alert-info d-flex align-items-center" role="alert">
                    <div class="spinner-border spinner-border-sm me-3" role="status" aria-hidden="true"></div>
                    <div>Updating models...</div>
                </div>
            `;
            statusMessage.classList.remove('d-none');

            try {
                // Prepare data object with available models
                const data = {
                    llm_model: llmModel,
                    embedding_model: embeddingModel
                };

                // Add vision model if available
                if (visionModel) {
                    data.vision_model = visionModel;
                }

                // Add vision toggle states
                const useVisionCheckbox = document.getElementById('use_vision');
                if (useVisionCheckbox) {
                    data.use_vision = useVisionCheckbox.checked;
                }

                // Add vision during embedding toggle state
                const useVisionDuringEmbeddingCheckbox = document.getElementById('use_vision_during_embedding');
                if (useVisionDuringEmbeddingCheckbox) {
                    data.use_vision_during_embedding = useVisionDuringEmbeddingCheckbox.checked;
                }

                // Add image filtering settings
                const filterPdfImagesCheckbox = document.getElementById('filter_pdf_images');
                if (filterPdfImagesCheckbox) {
                    data.filter_pdf_images = filterPdfImagesCheckbox.checked;
                }

                // Add filter sensitivity
                const sensitivityLow = document.getElementById('sensitivity_low');
                const sensitivityMedium = document.getElementById('sensitivity_medium');
                const sensitivityHigh = document.getElementById('sensitivity_high');

                if (sensitivityLow && sensitivityLow.checked) {
                    data.filter_sensitivity = 'low';
                } else if (sensitivityMedium && sensitivityMedium.checked) {
                    data.filter_sensitivity = 'medium';
                } else if (sensitivityHigh && sensitivityHigh.checked) {
                    data.filter_sensitivity = 'high';
                }

                // Add max PDF images to analyze
                const maxPdfImages = document.getElementById('max_pdf_images');
                if (maxPdfImages && maxPdfImages.value) {
                    data.max_pdf_images = parseInt(maxPdfImages.value);
                }

                // Add show filtered images toggle
                const showFilteredImagesCheckbox = document.getElementById('show_filtered_images');
                if (showFilteredImagesCheckbox) {
                    data.show_filtered_images = showFilteredImagesCheckbox.checked;
                }

                // Add model parameters
                const temperature = document.getElementById('temperature');
                if (temperature) {
                    data.temperature = parseFloat(temperature.value);
                }

                const topP = document.getElementById('top_p');
                if (topP) {
                    data.top_p = parseFloat(topP.value);
                }

                const topK = document.getElementById('top_k');
                if (topK) {
                    data.top_k = parseInt(topK.value);
                }

                const repeatPenalty = document.getElementById('repeat_penalty');
                if (repeatPenalty) {
                    data.repeat_penalty = parseFloat(repeatPenalty.value);
                }

                const numCtx = document.getElementById('num_ctx');
                if (numCtx) {
                    data.num_ctx = parseInt(numCtx.value);
                }

                const numPredict = document.getElementById('num_predict');
                if (numPredict) {
                    data.num_predict = parseInt(numPredict.value);
                }

                const systemPrompt = document.getElementById('system_prompt');
                if (systemPrompt) {
                    data.system_prompt = systemPrompt.value;
                }

                const res = await fetch('/admin/models', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const json = await res.json();

                statusMessage.innerHTML = `
                    <div class="alert ${res.ok ? 'alert-success' : 'alert-danger'}" role="alert">
                        <i class="bi ${res.ok ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill'} me-2"></i>
                        ${json.message || json.error || (res.ok ? "Models updated successfully" : "Failed to update models")}
                    </div>
                `;

                Toastify({
                    text: json.message || json.error || (res.ok ? "Models updated successfully" : "Failed to update models"),
                    duration: 3000,
                    backgroundColor: res.ok ? "#00C851" : "#ff4444"
                }).showToast();

                // Reload the page after a short delay to reflect changes
                if (res.ok) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            } catch (error) {
                statusMessage.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error: ${error.message || "Failed to update models"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${error.message || "Failed to update models"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        }

        // Handle form submission
        document.getElementById('modelForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const llmModel = formData.get('llm_model');
            const embeddingModel = formData.get('embedding_model');
            const visionModel = formData.get('vision_model');
            updateModels(llmModel, embeddingModel, visionModel);
        });

        // Function to save current models as default
        async function saveAsDefault() {
            const formData = new FormData(document.getElementById('modelForm'));
            const llmModel = formData.get('llm_model');
            const embeddingModel = formData.get('embedding_model');
            const visionModel = formData.get('vision_model'); // Get vision model if available

            if (!llmModel || !embeddingModel) {
                Toastify({
                    text: "Please select both an LLM model and an embedding model",
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
                return;
            }

            const statusMessage = document.getElementById('statusMessage');
            statusMessage.innerHTML = `
                <div class="alert alert-info d-flex align-items-center" role="alert">
                    <div class="spinner-border spinner-border-sm me-3" role="status" aria-hidden="true"></div>
                    <div>Saving default models...</div>
                </div>
            `;
            statusMessage.classList.remove('d-none');

            // Prepare data object with available models
            const data = {
                llm_model: llmModel,
                embedding_model: embeddingModel
            };

            // Add vision model if available
            if (visionModel) {
                data.vision_model = visionModel;
            }

            // Add vision toggle states
            const useVisionCheckbox = document.getElementById('use_vision');
            if (useVisionCheckbox) {
                data.use_vision = useVisionCheckbox.checked;
            }

            // Add vision during embedding toggle state
            const useVisionDuringEmbeddingCheckbox = document.getElementById('use_vision_during_embedding');
            if (useVisionDuringEmbeddingCheckbox) {
                data.use_vision_during_embedding = useVisionDuringEmbeddingCheckbox.checked;
            }

            // Add image filtering settings
            const filterPdfImagesCheckbox = document.getElementById('filter_pdf_images');
            if (filterPdfImagesCheckbox) {
                data.filter_pdf_images = filterPdfImagesCheckbox.checked;
            }

            // Add filter sensitivity
            const sensitivityLow = document.getElementById('sensitivity_low');
            const sensitivityMedium = document.getElementById('sensitivity_medium');
            const sensitivityHigh = document.getElementById('sensitivity_high');

            if (sensitivityLow && sensitivityLow.checked) {
                data.filter_sensitivity = 'low';
            } else if (sensitivityMedium && sensitivityMedium.checked) {
                data.filter_sensitivity = 'medium';
            } else if (sensitivityHigh && sensitivityHigh.checked) {
                data.filter_sensitivity = 'high';
            }

            // Add max PDF images to analyze
            const maxPdfImages = document.getElementById('max_pdf_images');
            if (maxPdfImages && maxPdfImages.value) {
                data.max_pdf_images = parseInt(maxPdfImages.value);
            }

            // Add show filtered images toggle
            const showFilteredImagesCheckbox = document.getElementById('show_filtered_images');
            if (showFilteredImagesCheckbox) {
                data.show_filtered_images = showFilteredImagesCheckbox.checked;
            }

            // Add model parameters
            const temperature = document.getElementById('temperature');
            if (temperature) {
                data.temperature = parseFloat(temperature.value);
            }

            const topP = document.getElementById('top_p');
            if (topP) {
                data.top_p = parseFloat(topP.value);
            }

            const topK = document.getElementById('top_k');
            if (topK) {
                data.top_k = parseInt(topK.value);
            }

            const repeatPenalty = document.getElementById('repeat_penalty');
            if (repeatPenalty) {
                data.repeat_penalty = parseFloat(repeatPenalty.value);
            }

            const numCtx = document.getElementById('num_ctx');
            if (numCtx) {
                data.num_ctx = parseInt(numCtx.value);
            }

            const numPredict = document.getElementById('num_predict');
            if (numPredict) {
                data.num_predict = parseInt(numPredict.value);
            }

            const systemPrompt = document.getElementById('system_prompt');
            if (systemPrompt) {
                data.system_prompt = systemPrompt.value;
            }

            try {
                const res = await fetch('/admin/models/default', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const json = await res.json();

                statusMessage.innerHTML = `
                    <div class="alert ${res.ok ? 'alert-success' : 'alert-danger'}" role="alert">
                        <i class="bi ${res.ok ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill'} me-2"></i>
                        ${json.message || json.error || (res.ok ? "Default models saved successfully" : "Failed to save default models")}
                    </div>
                `;

                Toastify({
                    text: json.message || json.error || (res.ok ? "Default models saved successfully" : "Failed to save default models"),
                    duration: 3000,
                    backgroundColor: res.ok ? "#00C851" : "#ff4444"
                }).showToast();

                // Reload the page after a short delay to reflect changes
                if (res.ok) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }

            } catch (error) {
                statusMessage.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error: ${error.message || "Failed to save default models"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${error.message || "Failed to save default models"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        }

        // Handle "Save as Default" button click
        document.getElementById('saveDefaultsButton').addEventListener('click', saveAsDefault);

        // Handle "Use Default Models" button click
        const defaultModelsButton = document.getElementById('useDefaultModels');
        if (defaultModelsButton) {
            defaultModelsButton.addEventListener('click', () => {
                // Show a message that default models will be used
                Toastify({
                    text: "Default models configuration will be applied. Please check Ollama installation and restart the application.",
                    duration: 5000,
                    backgroundColor: "#FFA500"
                }).showToast();

                // Optionally reload the page after a delay
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            });
        }

        // Handle "Refresh Models" button click
        const refreshModelsButton = document.getElementById('refreshModels');
        if (refreshModelsButton) {
            refreshModelsButton.addEventListener('click', async () => {
                // Disable button and show loading state
                refreshModelsButton.disabled = true;
                const originalContent = refreshModelsButton.innerHTML;
                refreshModelsButton.innerHTML = '<i class="bi bi-arrow-clockwise me-1 spin"></i>Refreshing...';

                try {
                    const response = await fetch('/admin/models/refresh', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        Toastify({
                            text: `Models refreshed! Found ${result.models.llm_count} LLM, ${result.models.embedding_count} embedding, and ${result.models.vision_count} vision models.`,
                            duration: 3000,
                            backgroundColor: "#00C851"
                        }).showToast();

                        // Reload the page to show updated models
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        throw new Error(result.error || 'Failed to refresh models');
                    }
                } catch (error) {
                    Toastify({
                        text: `Error refreshing models: ${error.message}`,
                        duration: 5000,
                        backgroundColor: "#ff4444"
                    }).showToast();
                } finally {
                    // Re-enable button and restore original content
                    refreshModelsButton.disabled = false;
                    refreshModelsButton.innerHTML = originalContent;
                }
            });
        }
    </script>
</body>
</html>
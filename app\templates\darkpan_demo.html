{% extends "admin_base.html" %}

{% block title %}Dark<PERSON><PERSON>{% endblock %}
{% block content_title %}DarkPan Demo Page{% endblock %}

{% from "macros/ui_components.html" import button, card, alert, badge, input, select, textarea, pagination, stat_card, progress_bar, back_to_top_button %}

{% block content %}
<!-- Stat Cards -->
<div class="row g-4 mb-4">
    <div class="col-sm-6 col-xl-3">
        {{ stat_card("Documents", "124", "fa fa-file-alt", "primary") }}
    </div>
    <div class="col-sm-6 col-xl-3">
        {{ stat_card("Categories", "12", "fa fa-folder", "secondary") }}
    </div>
    <div class="col-sm-6 col-xl-3">
        {{ stat_card("Users", "45", "fa fa-users", "success") }}
    </div>
    <div class="col-sm-6 col-xl-3">
        {{ stat_card("Sessions", "289", "fa fa-comments", "info") }}
    </div>
</div>

<!-- Alerts -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Alert Components", body_classes="p-4") }}
            {{ alert("This is a primary alert with DarkPan styling.", "primary", True) }}
            {{ alert("This is a success alert with DarkPan styling.", "success", True) }}
            {{ alert("This is a warning alert with DarkPan styling.", "warning", True) }}
            {{ alert("This is a danger alert with DarkPan styling.", "danger", True) }}
        {{ card() }}
    </div>
</div>

<!-- Buttons -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Button Components", body_classes="p-4") }}
            <div class="mb-3">
                {{ button("Primary Button", "primary", "md", "fa fa-check") }}
                {{ button("Secondary Button", "secondary", "md", "fa fa-cog") }}
                {{ button("Success Button", "success", "md", "fa fa-check-circle") }}
                {{ button("Danger Button", "danger", "md", "fa fa-times-circle") }}
            </div>
            <div class="mb-3">
                {{ button("Small Button", "primary", "sm", "fa fa-check") }}
                {{ button("Medium Button", "primary", "md", "fa fa-check") }}
                {{ button("Large Button", "primary", "lg", "fa fa-check") }}
            </div>
        {{ card() }}
    </div>
</div>

<!-- Badges -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Badge Components", body_classes="p-4") }}
            <div class="mb-3">
                {{ badge("Primary", "primary") }}
                {{ badge("Secondary", "secondary") }}
                {{ badge("Success", "success") }}
                {{ badge("Danger", "danger") }}
                {{ badge("Warning", "warning") }}
                {{ badge("Info", "info") }}
            </div>
            <div class="mb-3">
                {{ badge("Primary Pill", "primary", True) }}
                {{ badge("Secondary Pill", "secondary", True) }}
                {{ badge("Success Pill", "success", True) }}
                {{ badge("Danger Pill", "danger", True) }}
            </div>
        {{ card() }}
    </div>
</div>

<!-- Progress Bars -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Progress Bar Components", body_classes="p-4") }}
            <div class="mb-3">
                <p class="text-light mb-1">Primary Progress (25%)</p>
                {{ progress_bar(25, 100, 10, "primary") }}
            </div>
            <div class="mb-3">
                <p class="text-light mb-1">Secondary Progress (50%)</p>
                {{ progress_bar(50, 100, 10, "secondary") }}
            </div>
            <div class="mb-3">
                <p class="text-light mb-1">Success Progress (75%)</p>
                {{ progress_bar(75, 100, 10, "success") }}
            </div>
            <div class="mb-3">
                <p class="text-light mb-1">Danger Progress (90%)</p>
                {{ progress_bar(90, 100, 10, "danger") }}
            </div>
        {{ card() }}
    </div>
</div>

<!-- Forms -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Form Components", body_classes="p-4") }}
            <form>
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ input("text", "name", "name", "Name", "Enter your name", "", True) }}
                    </div>
                    <div class="col-md-6">
                        {{ input("email", "email", "email", "Email", "Enter your email", "", True) }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ select("category", "category", "Category", [
                            {"value": "", "text": "-- Select Category --"},
                            {"value": "1", "text": "Category 1"},
                            {"value": "2", "text": "Category 2"},
                            {"value": "3", "text": "Category 3"}
                        ], "", True) }}
                    </div>
                    <div class="col-md-6">
                        {{ input("file", "file", "file", "File Upload", "", "", False, "Upload a file (max 25MB)") }}
                    </div>
                </div>
                <div class="mb-3">
                    {{ textarea("message", "message", "Message", "Enter your message", "", 5, True) }}
                </div>
                <div class="mb-3">
                    {{ button("Submit", "primary", "md", "fa fa-paper-plane") }}
                    {{ button("Cancel", "secondary", "md", "fa fa-times") }}
                </div>
            </form>
        {{ card() }}
    </div>
</div>

<!-- Table -->
<div class="row g-4 mb-4">
    <div class="col-12">
        {{ card(title="Table Component", body_classes="p-4") }}
            <div class="table-responsive">
                <table class="table table-dark table-hover">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Document Name</th>
                            <th scope="col">Category</th>
                            <th scope="col">Date Added</th>
                            <th scope="col">Status</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>Document 1.pdf</td>
                            <td>Category 1</td>
                            <td>2023-05-15</td>
                            <td>{{ badge("Active", "success") }}</td>
                            <td>
                                {{ button("View", "primary", "sm", "fa fa-eye") }}
                                {{ button("Edit", "secondary", "sm", "fa fa-edit") }}
                                {{ button("Delete", "danger", "sm", "fa fa-trash") }}
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Document 2.pdf</td>
                            <td>Category 2</td>
                            <td>2023-05-16</td>
                            <td>{{ badge("Active", "success") }}</td>
                            <td>
                                {{ button("View", "primary", "sm", "fa fa-eye") }}
                                {{ button("Edit", "secondary", "sm", "fa fa-edit") }}
                                {{ button("Delete", "danger", "sm", "fa fa-trash") }}
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Document 3.pdf</td>
                            <td>Category 1</td>
                            <td>2023-05-17</td>
                            <td>{{ badge("Inactive", "danger") }}</td>
                            <td>
                                {{ button("View", "primary", "sm", "fa fa-eye") }}
                                {{ button("Edit", "secondary", "sm", "fa fa-edit") }}
                                {{ button("Delete", "danger", "sm", "fa fa-trash") }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                {{ pagination(2, 5, "#?page=PAGE") }}
            </div>
        {{ card() }}
    </div>
</div>

<!-- Back to Top Button -->
{{ back_to_top_button() }}
{% endblock %}

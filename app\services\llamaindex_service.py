"""
LlamaIndex Service for Hybrid LangChain + LlamaIndex Integration

This service provides enhanced document processing capabilities by combining
LangChain's robust framework with LlamaIndex's advanced features.
"""

import logging
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

from llama_index.core import VectorStoreIndex, Document, Settings
from llama_index.llms.langchain import LangChainLLM
from llama_index.embeddings.langchain import LangchainEmbedding
from llama_index.core.node_parser import LangchainNodeParser
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import BaseRetriever
from llama_index.core.schema import NodeWithScore, QueryBundle
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.llms import Ollama
from langchain_community.embeddings import OllamaEmbeddings

from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class HybridRetriever(BaseRetriever):
    """Hybrid retriever combining vector similarity and document-level scoring."""
    
    def __init__(
        self,
        vector_index,
        docstore,
        similarity_top_k: int = 5,
        out_top_k: Optional[int] = None,
        alpha: float = 0.5,
        **kwargs: Any,
    ) -> None:
        """Initialize hybrid retriever."""
        super().__init__(**kwargs)
        self._vector_index = vector_index
        self._embed_model = vector_index._embed_model
        self._retriever = vector_index.as_retriever(similarity_top_k=similarity_top_k)
        self._out_top_k = out_top_k or similarity_top_k
        self._docstore = docstore
        self._alpha = alpha

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes using hybrid scoring."""
        # Get vector similarity results
        nodes = self._retriever.retrieve(query_bundle.query_str)
        
        # Get document embeddings for reweighting
        docs = [self._docstore.get_document(n.node.index_id) for n in nodes]
        doc_embeddings = [d.embedding for d in docs if d.embedding is not None]
        
        if doc_embeddings:
            query_embedding = self._embed_model.get_query_embedding(query_bundle.query_str)
            
            # Compute document similarities
            from llama_index.core.indices.query.embedding_utils import get_top_k_embeddings
            doc_similarities, doc_idxs = get_top_k_embeddings(query_embedding, doc_embeddings)
            
            # Combine scores
            result_tups = []
            for doc_idx, doc_similarity in zip(doc_idxs, doc_similarities):
                node = nodes[doc_idx]
                full_similarity = (self._alpha * node.score) + ((1 - self._alpha) * doc_similarity)
                result_tups.append((full_similarity, node))
            
            result_tups = sorted(result_tups, key=lambda x: x[0], reverse=True)
            
            # Update scores
            for full_score, node in result_tups:
                node.score = full_score
            
            return [n for _, n in result_tups][:self._out_top_k]
        
        return nodes[:self._out_top_k]


class LlamaIndexService:
    """Hybrid LlamaIndex + LangChain service for enhanced document processing."""
    
    def __init__(self, ollama_model=None):
        """Initialize the LlamaIndex service with LangChain integration."""
        # Get model from config if not provided
        if ollama_model is None:
            from config.rag_extraction_config import get_llamaindex_ollama_model
            ollama_model = get_llamaindex_ollama_model()
        """Initialize the LlamaIndex service with LangChain integration."""
        logger.info(f"Initializing LlamaIndex service with Ollama model: {ollama_model}")
        
        # Initialize LangChain components
        self.langchain_llm = Ollama(model=ollama_model)
        self.langchain_embeddings = OllamaEmbeddings(model=ollama_model)
        
        # Wrap with LlamaIndex
        self.llm = LangChainLLM(llm=self.langchain_llm)
        self.embed_model = LangchainEmbedding(self.langchain_embeddings)
        
        # Configure global settings
        Settings.llm = self.llm
        Settings.embed_model = self.embed_model
        
        # Node parser with LangChain text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000, 
            chunk_overlap=200
        )
        self.node_parser = LangchainNodeParser(self.text_splitter)
        
        logger.info("LlamaIndex service initialized successfully")

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def process_documents_hybrid(self, documents: List, category: Optional[str] = None) -> VectorStoreIndex:
        """Process documents using both LangChain and LlamaIndex."""
        logger.info(f"Processing {len(documents)} documents with hybrid LlamaIndex pipeline")
        
        # Convert LangChain documents to LlamaIndex format
        llama_docs = []
        for doc in documents:
            llama_doc = Document(
                text=doc.page_content,
                metadata={
                    **doc.metadata,
                    "category": category,
                    "processing_method": "hybrid_llamaindex_langchain",
                    "llamaindex_enhanced": True
                }
            )
            llama_docs.append(llama_doc)
        
        # Create LlamaIndex with enhanced processing
        index = VectorStoreIndex.from_documents(
            llama_docs,
            transformations=[self.node_parser],
            show_progress=True
        )
        
        logger.info(f"Successfully created LlamaIndex with {len(llama_docs)} documents")
        return index

    def create_hybrid_query_engine(self, index: VectorStoreIndex, 
                                 retrieval_strategy: str = "hybrid") -> RetrieverQueryEngine:
        """Create a query engine with multiple retrieval strategies."""
        logger.info(f"Creating query engine with strategy: {retrieval_strategy}")
        
        if retrieval_strategy == "hybrid":
            # Combine vector similarity + keyword search
            query_engine = index.as_query_engine(
                similarity_top_k=5,
                response_mode="tree_summarize",
                structured_answer_filtering=True
            )
        elif retrieval_strategy == "multimodal":
            # Enhanced for multi-modal content
            query_engine = index.as_query_engine(
                similarity_top_k=10,
                response_mode="compact",
                streaming=True
            )
        else:
            # Standard retrieval
            query_engine = index.as_query_engine()
        
        logger.info(f"Query engine created with {retrieval_strategy} strategy")
        return query_engine

    def create_hybrid_retriever(self, index: VectorStoreIndex, 
                              similarity_top_k: int = 5, 
                              alpha: float = 0.5) -> HybridRetriever:
        """Create a hybrid retriever for enhanced document retrieval."""
        logger.info(f"Creating hybrid retriever with top_k={similarity_top_k}, alpha={alpha}")
        
        try:
            # Get document store from index
            docstore = index.docstore
            
            retriever = HybridRetriever(
                vector_index=index,
                docstore=docstore,
                similarity_top_k=similarity_top_k,
                alpha=alpha
            )
            
            return retriever
        except Exception as e:
            logger.warning(f"Hybrid retriever creation failed: {str(e)}. Using standard retriever.")
            # Fallback to standard retriever
            return index.as_retriever(similarity_top_k=similarity_top_k)

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def query_documents(self, index: VectorStoreIndex, query: str, 
                       strategy: str = "hybrid") -> Dict[str, Any]:
        """Query documents using the specified strategy."""
        logger.info(f"Querying documents with strategy: {strategy}")
        
        start_time = time.time()
        
        if strategy == "hybrid":
            query_engine = self.create_hybrid_query_engine(index, "hybrid")
        elif strategy == "multimodal":
            query_engine = self.create_hybrid_query_engine(index, "multimodal")
        else:
            query_engine = index.as_query_engine()
        
        # Execute query
        response = query_engine.query(query)
        
        query_time = time.time() - start_time
        
        result = {
            "response": str(response),
            "source_nodes": [
                {
                    "text": node.text,
                    "metadata": node.metadata,
                    "score": getattr(node, 'score', None)
                }
                for node in response.source_nodes
            ],
            "query_time": query_time,
            "strategy": strategy,
            "total_sources": len(response.source_nodes)
        }
        
        logger.info(f"Query completed in {query_time:.2f}s with {len(response.source_nodes)} sources")
        return result

    def convert_langchain_to_llamaindex(self, langchain_docs: List) -> List[Document]:
        """Convert LangChain documents to LlamaIndex format."""
        llama_docs = []
        
        for doc in langchain_docs:
            llama_doc = Document(
                text=doc.page_content,
                metadata={
                    **doc.metadata,
                    "converted_from": "langchain",
                    "llamaindex_enhanced": True
                }
            )
            llama_docs.append(llama_doc)
        
        logger.info(f"Converted {len(langchain_docs)} LangChain documents to LlamaIndex format")
        return llama_docs

    def get_processing_stats(self, index: VectorStoreIndex) -> Dict[str, Any]:
        """Get processing statistics for the index."""
        stats = {
            "total_documents": len(index.docstore.docs),
            "total_nodes": len(index.docstore.nodes),
            "embedding_model": str(self.embed_model),
            "llm_model": str(self.llm),
            "node_parser": str(self.node_parser)
        }
        
        return stats


# Global instance for easy access
llamaindex_service = LlamaIndexService() 
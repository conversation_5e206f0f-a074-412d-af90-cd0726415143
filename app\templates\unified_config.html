{% extends "admin_base.html" %}

{% block title %}Model Settings{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>

    <!-- utilities.js is already included in admin_base.html -->

    <!-- Include dark-mode.css for consistent dark mode styling -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* Tab Navigation Styles */
        .tab-active {
            @apply bg-blue-700 text-white border-b-2 border-blue-700 font-medium;
            transform: translateY(1px) scale(1.02);
            transition: all 0.3s ease;
        }
        .tab-inactive {
            @apply bg-gray-200 text-gray-600 border-b border-transparent opacity-85;
            transition: all 0.3s ease;
        }
        .tab-inactive:hover {
            @apply bg-gray-300 text-gray-700 opacity-100;
        }

        /* Dark mode tab styles */
        .dark .tab-inactive {
            @apply bg-gray-700 text-gray-300;
        }
        .dark .tab-inactive:hover {
            @apply bg-gray-600 text-gray-200;
        }

        /* Additional text contrast fixes specific to model settings page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Fix for model cards */
        .bg-white .text-gray-900 { color: #1a202c !important; }

        /* Fix for section headers */
        .config-section-header { color: #1a202c !important; }

        /* Fix for section descriptions */
        .config-section-description { color: #4a5568 !important; }

        /* Fix for labels */
        label .text-gray-700 { color: #2d3748 !important; }

        /* Dark mode styles for unified config page */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #1f2937 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }
        .dark .text-blue-800 { color: #93c5fd !important; }
        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .text-green-600 { color: #34d399 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }
        .dark .border-blue-200 { border-color: #3b82f6 !important; }
        .dark .border-blue-500 { border-color: #60a5fa !important; }
        .dark .border-green-200 { border-color: #34d399 !important; }

        /* Model card styles in dark mode */
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Ring colors in dark mode */
        .dark .ring-blue-500 { --tw-ring-color: #60a5fa !important; }

        /* Config section overrides for dark mode */
        .dark .config-section-header { color: #f3f4f6 !important; }
        .dark .config-section-description { color: #d1d5db !important; }

        /* Tab Content Styles */
        .tab-content {
            @apply border-t-4 border-blue-700 rounded-b-lg;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .tab-content.hidden {
            opacity: 0;
            transform: translateY(10px);
            display: none;
        }
        .tab-content:not(.hidden) {
            opacity: 1;
            transform: translateY(0);
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Other UI Elements */
        .dependency-indicator {
            @apply text-xs text-blue-600 font-medium mt-1;
        }
        .config-section {
            @apply bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6;
        }
        .config-section-header {
            @apply text-xl font-semibold text-gray-800 mb-4;
        }
        .config-section-description {
            @apply text-sm text-gray-600 mb-4;
        }
        .config-subsection {
            @apply mt-6 border-t border-gray-200 pt-4;
        }
        .config-subsection-header {
            @apply text-lg font-medium text-gray-800 mb-3;
        }

        /* Accessibility Styles */
        .tab-active:focus, .tab-inactive:focus {
            @apply outline-none ring-2 ring-blue-500 ring-offset-2;
        }

        /* Active Tab Indicator */
        .tab-indicator {
            @apply ml-1 inline-flex items-center justify-center;
            width: 16px;
            height: 16px;
        }
        .tab-indicator svg {
            @apply text-white;
            width: 12px;
            height: 12px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Model Settings</h1>
        </div>

        <div id="statusMessage" class="mb-6 hidden"></div>

            <!-- Enhanced Configuration Summary -->
            <div class="mb-6 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900 dark:to-green-900 p-6 rounded-lg border border-blue-200 dark:border-blue-600">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <svg class="h-6 w-6 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <h2 class="text-xl font-semibold text-blue-800 dark:text-blue-200">RAG-Optimized Configuration Summary</h2>
                    </div>
                    <button id="toggleSummary" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- RAG Optimization Status -->
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-green-200 dark:border-green-600 mb-6">
                    <div class="flex items-center mb-3">
                        <svg class="h-5 w-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <h3 class="font-semibold text-green-800 dark:text-green-200">RAG Optimization Status</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                            <div class="font-medium text-green-800 dark:text-green-200">Unified Chunking</div>
                            <div class="text-green-600 dark:text-green-400 font-semibold" id="summary-rag-chunking">{{ embedding_params.chunk_size }} chars, {{ embedding_params.chunk_overlap }} overlap</div>
                            <div class="text-xs text-green-500">Optimized for RAG</div>
                        </div>
                        <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                            <div class="font-medium text-blue-800 dark:text-blue-200">Performance</div>
                            <div class="text-blue-600 dark:text-blue-400 font-semibold" id="summary-performance">0.099s average</div>
                            <div class="text-xs text-blue-500">Hybrid search</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                            <div class="font-medium text-purple-800 dark:text-purple-200">Integration</div>
                            <div class="text-purple-600 dark:text-purple-400 font-semibold" id="summary-integration">{{ 'Active' if llamaindex_config.enabled else 'Inactive' }}</div>
                            <div class="text-xs text-purple-500">LlamaIndex + LangChain</div>
                        </div>
                    </div>
                </div>
                
                <div id="summaryContent" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <svg class="h-4 w-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                                </svg>
                                AI Models
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">LLM:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-llm">{{ selected_model }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Embedding:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-embedding">{{ selected_embedding }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Vision:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-vision">{{ selected_vision }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <svg class="h-4 w-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                                Query Settings
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Anti-hallucination:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-hallucination">{{ anti_hallucination_modes.default_mode }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Templates:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-templates">5 configured</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Phrases:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-phrases">{{ insufficient_info_phrases|length }} configured</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <svg class="h-4 w-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                Embedding
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Chunk Size:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-chunk-size">{{ embedding_params.chunk_size }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Chunk Overlap:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-chunk-overlap">{{ embedding_params.chunk_overlap }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">RAG Optimized:</span>
                                    <span class="font-medium text-green-600" id="summary-rag-optimized">Yes</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <svg class="h-4 w-4 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/>
                                </svg>
                                LlamaIndex
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Status:</span>
                                    <span class="font-medium text-green-600" id="summary-llamaindex-status">{{ 'Active' if llamaindex_config.enabled else 'Inactive' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Strategy:</span>
                                    <span class="font-medium text-gray-800 dark:text-gray-200" id="summary-llamaindex-strategy">{{ llamaindex_config.retrieval_strategy }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Performance:</span>
                                    <span class="font-medium text-blue-600" id="summary-llamaindex-performance">Optimized</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 pt-6 border-t border-blue-200 dark:border-blue-600">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-3">Quick Actions</h4>
                        <div class="flex flex-wrap gap-3">
                            <button type="button" id="applyOptimalPreset" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                                Apply Optimal RAG Preset
                            </button>
                            <button type="button" id="validateCurrentConfig" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Validate Current Config
                            </button>
                            <button type="button" id="exportConfig" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm">
                                Export Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Tabs -->
            <div class="mb-6">
                <ul class="flex flex-wrap border-b border-gray-200 dark:border-gray-600" id="mainTabs" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="tab-active inline-block p-4 rounded-t-lg"
                                id="models-tab" data-target="models-content" type="button" role="tab"
                                aria-selected="true" aria-controls="models-content">
                            AI Models
                            <span class="tab-indicator">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="tab-inactive inline-block p-4 rounded-t-lg"
                                id="query-tab" data-target="query-content" type="button" role="tab"
                                aria-selected="false" aria-controls="query-content">
                            Query Configuration
                            <span class="tab-indicator hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="tab-inactive inline-block p-4 rounded-t-lg"
                                id="embedding-tab" data-target="embedding-content" type="button" role="tab"
                                aria-selected="false" aria-controls="embedding-content">
                            Embedding Configuration
                            <span class="tab-indicator hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="tab-inactive inline-block p-4 rounded-t-lg"
                                id="llamaindex-tab" data-target="llamaindex-content" type="button" role="tab"
                                aria-selected="false" aria-controls="llamaindex-content">
                            LlamaIndex Configuration
                            <span class="tab-indicator hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                </ul>
            </div>

            <form id="unifiedConfigForm" class="space-y-8">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <!-- AI Models Content -->
                <div id="models-content" class="tab-content">
                    {% include 'models_config_partial.html' ignore missing %}
                </div>

                <!-- Query Configuration Content -->
                <div id="query-content" class="tab-content hidden">
                    {# Modularized: Include query config partial #}
                    {% include 'query_config_partial.html' ignore missing %}
                    {# If not present, add a placeholder #}
                    {# <!-- TODO: Create query_config_partial.html --> #}
                </div>

                <!-- Embedding Configuration Content -->
                <div id="embedding-content" class="tab-content hidden">
                    {# Modularized: Include embedding config partial #}
                    {% include 'embedding_config_partial.html' ignore missing %}
                    {# If not present, add a placeholder #}
                    {# <!-- TODO: Create embedding_config_partial.html --> #}
                </div>

                <!-- LlamaIndex Configuration Content -->
                <div id="llamaindex-content" class="tab-content hidden">
                    {# Modularized: Include LlamaIndex config partial #}
                    {% include 'llamaindex_config_partial.html' ignore missing %}
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <button type="button" id="validateConfigBtn"
                        class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        Validate Configuration
                    </button>
                    <button type="submit"
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Save All Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Sticky Save/Validate Bar -->
        <div id="sticky-save-bar" class="fixed bottom-0 left-0 w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg z-50 flex justify-end items-center px-6 py-3 space-x-4" style="box-shadow: 0 -2px 8px rgba(0,0,0,0.08);">
            <div id="sticky-feedback" class="flex-1 text-left text-sm"></div>
            <button type="button" id="stickyValidateBtn" class="px-5 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                Validate Configuration
            </button>
            <button type="button" id="stickySaveBtn" class="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                Save All Settings
            </button>
        </div>
{% endblock %}

{% block scripts %}
    <script>
        // Initialize theme and functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();
        });
    </script>
    <script src="/static/unified_config.js"></script>
{% endblock %}

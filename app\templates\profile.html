<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - Document Management System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">User Profile</h1>
                <div class="flex space-x-4">
                    <a href="{{ url_for('admin.admin_dashboard') }}" class="text-blue-600 hover:underline">&larr; Back to Dashboard</a>
                    <a href="{{ url_for('user.logout') }}" class="text-red-600 hover:underline">Logout</a>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="mb-4 p-4 rounded-md {% if category == 'error' %}bg-red-100 text-red-700{% elif category == 'warning' %}bg-yellow-100 text-yellow-700{% else %}bg-green-100 text-green-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Profile Sidebar -->
                <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <div class="flex flex-col items-center text-center">
                        {% if current_user.profile_picture %}
                            <img src="{{ url_for('static', filename=current_user.profile_picture) }}" alt="Profile Picture" class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-md">
                        {% else %}
                            <div class="w-32 h-32 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 text-4xl font-bold border-4 border-white shadow-md">
                                {{ current_user.username[0].upper() }}
                            </div>
                        {% endif %}
                        <h2 class="mt-4 text-xl font-semibold">{{ current_user.full_name or current_user.username }}</h2>
                        <p class="text-gray-600">{{ current_user.email }}</p>
                        <p class="mt-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ current_user.role.capitalize() }}
                        </p>
                    </div>
                    
                    <div class="mt-6 border-t border-gray-200 pt-4">
                        <h3 class="text-lg font-semibold mb-2">Account Information</h3>
                        <ul class="space-y-2">
                            <li class="flex justify-between">
                                <span class="text-gray-600">Username:</span>
                                <span class="font-medium">{{ current_user.username }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Account Status:</span>
                                <span class="font-medium">{{ current_user.account_status.capitalize() }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="font-medium">{{ current_user.created_at.split('T')[0] if current_user.created_at else 'N/A' }}</span>
                            </li>
                            <li class="flex justify-between">
                                <span class="text-gray-600">Last Login:</span>
                                <span class="font-medium">{{ current_user.last_login.split('T')[0] if current_user.last_login else 'N/A' }}</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mt-6">
                        <a href="{{ url_for('user.change_password') }}" class="block w-full text-center bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded">
                            Change Password
                        </a>
                    </div>
                </div>
                
                <!-- Profile Form -->
                <div class="md:col-span-2 bg-white p-6 rounded-lg border border-gray-200">
                    <h2 class="text-xl font-semibold mb-4">Edit Profile</h2>
                    
                    <form method="POST" action="{{ url_for('user.profile') }}" enctype="multipart/form-data">
                        {{ form.csrf_token }}
                        
                        <div class="mb-4">
                            <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                            {{ form.email(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                            {% if form.email.errors %}
                                <p class="text-red-500 text-xs italic mt-1">{{ form.email.errors[0] }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="full_name" class="block text-gray-700 text-sm font-bold mb-2">Full Name</label>
                            {{ form.full_name(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                            {% if form.full_name.errors %}
                                <p class="text-red-500 text-xs italic mt-1">{{ form.full_name.errors[0] }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="profile_picture" class="block text-gray-700 text-sm font-bold mb-2">Profile Picture</label>
                            {{ form.profile_picture(class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100") }}
                            {% if form.profile_picture.errors %}
                                <p class="text-red-500 text-xs italic mt-1">{{ form.profile_picture.errors[0] }}</p>
                            {% endif %}
                            <p class="text-gray-500 text-xs mt-1">Allowed formats: JPG, JPEG, PNG (max 2MB)</p>
                        </div>
                        
                        <div class="mb-6">
                            <label for="current_password" class="block text-gray-700 text-sm font-bold mb-2">Current Password (to confirm changes)</label>
                            {{ form.current_password(class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline") }}
                            {% if form.current_password.errors %}
                                <p class="text-red-500 text-xs italic mt-1">{{ form.current_password.errors[0] }}</p>
                            {% endif %}
                        </div>
                        
                        <div class="flex items-center justify-end">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
</body>
</html>

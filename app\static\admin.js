async function api(path, opts = {}) {
    // Get CSRF token from meta tag
    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Add CSRF token to headers if not already present
    if (opts.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(opts.method.toUpperCase())) {
        opts.headers = opts.headers || {};
        if (!opts.headers['X-CSRFToken'] && csrfToken) {
            opts.headers['X-CSRFToken'] = csrfToken;
        }
    }

    const res = await fetch(path, opts);
    const json = await res.json().catch(() => ({}));

    // Handle CSRF token expiration
    if (res.status === 400 && json.error && json.error.includes('CSRF')) {
        console.warn('CSRF token expired, attempting to refresh...');

        // Try to refresh the CSRF token
        const refreshed = await refreshCSRFToken();
        if (refreshed) {
            // Retry the original request with the new token
            const newCsrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (opts.headers && newCsrfToken) {
                opts.headers['X-CSRFToken'] = newCsrfToken;
            }

            // Retry the request
            const retryRes = await fetch(path, opts);
            const retryJson = await retryRes.json().catch(() => ({}));

            return { ok: retryRes.ok, status: retryRes.status, json: retryJson };
        }
    }

    return { ok: res.ok, status: res.status, json };
}

async function refreshCSRFToken() {
    try {
        const response = await fetch('/api/csrf-token', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.csrf_token) {
                // Update the meta tag with the new token
                const metaTag = document.querySelector('meta[name="csrf-token"]');
                if (metaTag) {
                    metaTag.setAttribute('content', data.csrf_token);
                    console.log('CSRF token refreshed successfully');
                    return true;
                }
            }
        }

        console.error('Failed to refresh CSRF token');
        return false;
    } catch (error) {
        console.error('Error refreshing CSRF token:', error);
        return false;
    }
}

function showToast(message, type) {
    Toastify({
        text: message,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: type === "error" ? "#ff4444" : "#00C851",
    }).showToast();
}

async function createCategory() {
    const category = document.getElementById("newCategory").value.trim();
    if (!category) {
        showToast("Category name cannot be empty.", "error");
        return;
    }
    const { ok, json } = await api('/admin/categories', {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ category })
    });
    showToast(ok ? json.message : json.error, ok ? "success" : "error");
    if (ok) {
        location.reload();
    }
}

async function deleteCategory(category) {
    if (!confirm(`Delete category "${category}" and all its data?`)) return;
    const { ok, json } = await api(`/admin/categories?category=${category}`, {
        method: "DELETE"
    });
    showToast(ok ? json.message : json.error, ok ? "success" : "error");
    if (ok) {
        location.reload();
    }
}

async function deleteItem(category, item) {
    if (!confirm(`Delete "${item}" and its vector data?`)) return;
    const { ok, json } = await api(`/admin/delete/${category}/${encodeURIComponent(item)}`, {
        method: "DELETE"
    });
    showToast(ok ? json.message : json.error, ok ? "success" : "error");
    if (ok) {
        location.reload();
    }
}
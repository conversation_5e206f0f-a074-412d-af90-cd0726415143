{% extends "admin_base.html" %}

{% block title %}{% if form %}Edit Form{% else %}Create Form{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <h6 class="mb-4">{% if form %}Edit Form{% else %}Create New Form{% endif %}</h6>
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ form.name if form else '' }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ form.description if form else '' }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="fields" class="form-label">Fields (JSON format)</label>
                        <textarea class="form-control" id="fields" name="fields" rows="10" required>{{ form.fields|tojson(indent=4) if form else '[]' }}</textarea>
                        <div class="form-text">
                            Example: [{"name": "full_name", "label": "Full Name", "type": "text", "required": true}]
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if form and form.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">
                            Active
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary">{% if form %}Update Form{% else %}Create Form{% endif %}</button>
                    <a href="{{ url_for('list_forms') }}" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
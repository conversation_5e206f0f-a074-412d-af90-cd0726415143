/* Welcome Screen Styles
 * Professional styling for the ERDB Knowledge Hub welcome interface
 * Using ERDB brand colors and design system
 */

/* Welcome Screen Layout */
.welcome-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    display: flex;
    flex-direction: column;
}

.welcome-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.welcome-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.welcome-header {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.welcome-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.chat-box {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

/* Welcome Message Styling */
.welcome-message {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--light-50) 100%);
    border: 1px solid var(--primary-200);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.welcome-message-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--primary-200);
}

.welcome-message-icon {
    width: 1rem;
    height: 1rem;
    color: var(--primary-500);
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Chat Message Icons */
.user-message-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #3b82f6; /* blue-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.typing-indicator-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #10b981; /* green-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Pulse animation for typing indicator */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Details section icons */
.details-icon {
    width: 1rem !important;
    height: 1rem !important;
    max-width: 1rem !important;
    max-height: 1rem !important;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

/* Error message icon */
.error-message-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #ef4444; /* red-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.welcome-message-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-700);
    margin: 0;
}

.welcome-message-time {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-left: auto;
}

.welcome-message-content {
    color: var(--text-primary);
    line-height: 1.6;
}

.welcome-message-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.welcome-message-content li {
    margin-bottom: 0.5rem;
}

/* Input Area */
.input-area {
    padding: 2rem;
    background: var(--bg-card);
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.input-select,
.input-textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.input-select:focus,
.input-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(55, 140, 71, 0.1);
}

.input-with-button {
    display: flex;
}

.input-textarea {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    resize: none;
    min-height: 2.75rem;
}

.input-button {
    background: var(--primary-500);
    color: white;
    border: 1px solid var(--primary-500);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-button:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

.input-button svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    flex-shrink: 0;
}

/* Anti-hallucination Mode */
.mode-selector {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
}

.mode-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.mode-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--secondary-500);
    margin-right: 0.5rem;
}

.mode-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin: 0;
}

.mode-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.mode-option {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.mode-option:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-300);
}

.mode-option input[type="radio"] {
    margin-right: 0.5rem;
}

.mode-option-label {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-right: 0.25rem;
}

.mode-option-desc {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Client Name Modal */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal);
    padding: 1rem;
}

.modal-backdrop.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.modal-text {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.modal-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-base);
    margin-bottom: 1.5rem;
}

.modal-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(55, 140, 71, 0.1);
}

.modal-info {
    background: var(--secondary-50);
    border: 1px solid var(--secondary-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.modal-info-title {
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-700);
    margin-bottom: 0.5rem;
}

.modal-info-text {
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
    margin-bottom: 0.5rem;
}

.modal-info-list {
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.modal-button {
    background: var(--primary-500);
    color: white;
    border: 1px solid var(--primary-500);
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    width: 100%;
}

.modal-button:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

/* Top Navigation */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-link {
    color: var(--secondary-500);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--secondary-700);
    text-decoration: underline;
}

.logout-button {
    color: var(--danger-600);
    background: none;
    border: none;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.logout-button:hover {
    color: var(--danger-700);
    text-decoration: underline;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--border-radius-circle);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-main {
        padding: 1rem;
    }

    .welcome-header {
        padding: 1.5rem;
    }

    .welcome-title {
        font-size: var(--font-size-2xl);
    }

    .input-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chat-box {
        padding: 1rem;
    }

    .input-area {
        padding: 1rem;
    }

    .mode-options {
        flex-direction: column;
    }

    .mode-option {
        width: 100%;
    }

    .top-nav {
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Dark Mode Adjustments */
.dark-mode .welcome-message {
    background: linear-gradient(135deg, var(--primary-900) 0%, var(--light-900) 100%);
    border-color: var(--primary-700);
}

.dark-mode .welcome-message-header {
    border-color: var(--primary-700);
}

.dark-mode .welcome-message-title {
    color: var(--primary-300);
}

.dark-mode .modal-info {
    background: var(--secondary-900);
    border-color: var(--secondary-700);
}

.dark-mode .modal-info-title {
    color: var(--secondary-300);
}

.dark-mode .modal-info-text,
.dark-mode .modal-info-list {
    color: var(--secondary-400);
}

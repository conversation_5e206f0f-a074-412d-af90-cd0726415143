<!--
  This is the canonical query configuration template (query_config_partial.html).
  All query configuration UI and settings should be managed and synced here.
  Do NOT use query_config.html; it is deprecated and has been removed.
-->
<!-- Query Configuration Sections -->
<div class="container-fluid">
    <!-- Core Configuration Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-primary text-white d-flex align-items-center core-config-header">
                    <i class="fas fa-star me-2 text-warning"></i>
                    <h5 class="mb-0 fw-bold">Core Configuration</h5>
                    <small class="ms-auto text-light opacity-90">Essential settings for query processing</small>
                </div>
                <div class="card-body">
                    <!-- Preamble Text -->
                    <div class="mb-4">
                        <label for="preamble" class="form-label fw-bold">
                            <i class="fas fa-file-text me-1 text-primary"></i>
                            Preamble Text
                            <span class="badge bg-primary ms-2">Required</span>
                        </label>
                        <p class="text-muted small mb-3">
                            This text is included at the beginning of each prompt to the LLM, providing context about how to use the retrieved information.
                            Use clear, specific instructions to guide the AI's behavior and response style.
                        </p>
                        <div class="position-relative">
                            <textarea id="preamble" name="preamble" rows="8"
                                class="form-control preamble-textarea"
                                placeholder="Example: You are an AI assistant specialized in analyzing documents from the ERDB Knowledge Products database. When responding to queries:

1. Base your answers strictly on the provided document content
2. Cite specific sources with page numbers when available
3. If information is insufficient, clearly state this limitation
4. Maintain a professional, informative tone
5. Provide actionable insights when possible

Always prioritize accuracy over completeness."
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Enter detailed instructions that will guide how the AI responds to all user queries">{{ preamble }}</textarea>
                            <div class="character-counter position-absolute bottom-0 end-0 me-2 mb-1">
                                <small class="text-muted">
                                    <span id="preamble-char-count">0</span> characters
                                </small>
                            </div>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Best practices:</strong> Include specific instructions about tone, citation requirements,
                            handling uncertainty, and response format. This text directly impacts AI response quality.
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="preamble-template-btn">
                                <i class="fas fa-magic me-1"></i>Load Template
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm ms-2" id="preamble-preview-btn">
                                <i class="fas fa-eye me-1"></i>Preview
                            </button>
                        </div>
                    </div>

                    <!-- Anti-Hallucination Modes -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-shield-alt me-1 text-warning"></i>
                            Anti-Hallucination Mode
                        </label>
                        <p class="text-muted small mb-3">Configure how the system handles potential hallucinations or fabricated information.</p>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" name="default_mode" value="strict" id="mode_strict" class="form-check-input"
                                            {% if anti_hallucination_modes.default_mode == 'strict' %}checked{% endif %}>
                                        <label class="form-check-label" for="mode_strict">
                                            <strong>Strict</strong>
                                            <small class="d-block text-muted">Maximum accuracy, minimal speculation</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="default_mode" value="balanced" id="mode_balanced" class="form-check-input"
                                            {% if anti_hallucination_modes.default_mode == 'balanced' %}checked{% endif %}>
                                        <label class="form-check-label" for="mode_balanced">
                                            <strong>Balanced</strong>
                                            <small class="d-block text-muted">Moderate accuracy with helpful context</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" name="default_mode" value="off" id="mode_off" class="form-check-input"
                                            {% if anti_hallucination_modes.default_mode == 'off' %}checked{% endif %}>
                                        <label class="form-check-label" for="mode_off">
                                            <strong>Off</strong>
                                            <small class="d-block text-muted">Standard AI responses</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="custom_instructions" class="form-label">Custom Instructions</label>
                                <textarea id="custom_instructions" name="custom_instructions" rows="3"
                                    class="form-control"
                                    placeholder="Enter custom instructions for handling uncertainty...">{{ anti_hallucination_modes.custom_instructions }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Processing Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white d-flex align-items-center">
                    <i class="fas fa-search me-2"></i>
                    <h5 class="mb-0">Document Processing</h5>
                    <small class="ms-auto text-light opacity-75">Control how documents are retrieved and displayed</small>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- Document Retrieval Settings -->
                        <div class="col-lg-6">
                            <h6 class="fw-bold text-success mb-3">
                                <i class="fas fa-database me-1"></i>
                                Document Retrieval Settings
                            </h6>
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <label for="retrieval_k" class="form-label">Documents to Retrieve (K)</label>
                                    <input type="number" id="retrieval_k" name="retrieval_k" min="1" max="50"
                                        value="{{ query_parameters.retrieval_k or 12 }}" class="form-control">
                                    <div class="form-text">Number of documents to retrieve from vector database</div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="threshold-slider-container">
                                        <label for="relevance_threshold" class="form-label d-flex align-items-center justify-content-between">
                                            <span class="fw-bold">
                                                <i class="fas fa-search me-1 text-primary"></i>
                                                Relevance Threshold
                                            </span>
                                            <span class="badge bg-primary threshold-badge" id="relevance_mode_badge">Standard</span>
                                        </label>
                                        <div class="slider-wrapper position-relative mb-3">
                                            <input type="range"
                                                id="relevance_threshold"
                                                name="relevance_threshold"
                                                min="0" max="1" step="0.05"
                                                value="{{ query_parameters.relevance_threshold or 0.15 }}"
                                                class="enhanced-range-slider relevance-slider"
                                                data-slider-type="relevance"
                                                aria-label="Document relevance threshold"
                                                aria-describedby="relevance_help_text">
                                            <div class="slider-track-fill"></div>
                                            <div class="slider-value-tooltip">
                                                <span id="relevance_threshold_value">{{ "%.2f"|format(query_parameters.relevance_threshold or 0.15) }}</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between small text-muted mb-2">
                                            <span class="range-label">0.0<br><small>All Results</small></span>
                                            <span class="range-label text-center">0.5<br><small>Moderate</small></span>
                                            <span class="range-label text-end">1.0<br><small>Exact Match</small></span>
                                        </div>
                                        <div class="threshold-indicator">
                                            <div class="indicator-bar">
                                                <div class="indicator-fill relevance-fill" id="relevance_indicator"></div>
                                            </div>
                                        </div>
                                        <small class="text-muted d-block mt-2" id="relevance_description">Standard relevance filtering for balanced results</small>
                                        <div class="form-text mt-2" id="relevance_help_text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Minimum relevance score to include document. Lower values include more documents, higher values are more selective.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <label for="min_documents" class="form-label">Minimum Documents</label>
                                    <input type="number" id="min_documents" name="min_documents" min="1" max="10"
                                        value="{{ query_parameters.min_documents or 3 }}" class="form-control">
                                    <div class="form-text">Minimum documents to use in response</div>
                                </div>
                                <div class="col-sm-6">
                                    <label for="max_documents" class="form-label">Maximum Documents</label>
                                    <input type="number" id="max_documents" name="max_documents" min="1" max="20"
                                        value="{{ query_parameters.max_documents or 8 }}" class="form-control">
                                    <div class="form-text">Maximum documents to use in response</div>
                                </div>
                            </div>
                        </div>

                        <!-- Response Display Limits -->
                        <div class="col-lg-6">
                            <h6 class="fw-bold text-info mb-3">
                                <i class="fas fa-eye me-1"></i>
                                Response Display Limits
                            </h6>
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <label for="max_pdf_images_display" class="form-label">Max PDF Images</label>
                                    <input type="number" id="max_pdf_images_display" name="max_pdf_images_display"
                                        min="1" max="20" value="{{ query_parameters.max_pdf_images_display or 5 }}"
                                        class="form-control">
                                    <div class="form-text">PDF-extracted images to show</div>
                                </div>
                                <div class="col-sm-6">
                                    <label for="max_url_images_display" class="form-label">Max URL Images</label>
                                    <input type="number" id="max_url_images_display" name="max_url_images_display"
                                        min="1" max="20" value="{{ query_parameters.max_url_images_display or 5 }}"
                                        class="form-control">
                                    <div class="form-text">URL-sourced images to show</div>
                                </div>
                                <div class="col-sm-6">
                                    <label for="max_tables_display" class="form-label">Max Tables</label>
                                    <input type="number" id="max_tables_display" name="max_tables_display"
                                        min="1" max="10" value="{{ query_parameters.max_tables_display or 3 }}"
                                        class="form-control">
                                    <div class="form-text">Tables to show in response</div>
                                </div>
                                <div class="col-sm-6">
                                    <label for="max_pdf_links_display" class="form-label">Max PDF Links</label>
                                    <input type="number" id="max_pdf_links_display" name="max_pdf_links_display"
                                        min="1" max="50" value="{{ query_parameters.max_pdf_links_display or 10 }}"
                                        class="form-control">
                                    <div class="form-text">PDF download links to show</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Settings Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark d-flex align-items-center">
                    <i class="fas fa-cogs me-2"></i>
                    <h5 class="mb-0">Advanced Settings</h5>
                    <small class="ms-auto opacity-75">Fine-tune detection and processing parameters</small>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- Hallucination Detection Settings -->
                        <div class="col-lg-8">
                            <h6 class="fw-bold text-warning mb-4">
                                <i class="fas fa-shield-alt me-1"></i>
                                Hallucination Detection Thresholds
                                <small class="text-muted fw-normal ms-2">(Configure sensitivity levels)</small>
                            </h6>
                            <div class="row g-4">
                                <!-- Strict Mode Slider -->
                                <div class="col-md-4">
                                    <div class="threshold-slider-container">
                                        <label for="hallucination_threshold_strict" class="form-label d-flex align-items-center justify-content-between">
                                            <span class="fw-bold">
                                                <i class="fas fa-exclamation-triangle me-1 text-danger"></i>
                                                Strict Mode
                                            </span>
                                            <span class="badge bg-danger threshold-badge" id="strict_mode_badge">High Security</span>
                                        </label>
                                        <div class="slider-wrapper position-relative mb-3">
                                            <input type="range"
                                                id="hallucination_threshold_strict"
                                                name="hallucination_threshold_strict"
                                                min="0.3" max="1.0" step="0.05"
                                                value="{{ hallucination_detection.threshold_strict }}"
                                                class="enhanced-range-slider strict-slider"
                                                data-slider-type="strict"
                                                aria-label="Strict mode hallucination detection threshold"
                                                aria-describedby="strict_help_text">
                                            <div class="slider-track-fill strict-track"></div>
                                            <div class="slider-value-tooltip" id="strict_tooltip">
                                                <span id="hallucination_threshold_strict_value">{{ hallucination_detection.threshold_strict }}</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between small text-muted mb-2">
                                            <span class="range-label">0.3<br><small>Lenient</small></span>
                                            <span class="range-label text-center">0.65<br><small>Moderate</small></span>
                                            <span class="range-label text-end">1.0<br><small>Maximum</small></span>
                                        </div>
                                        <div class="form-text" id="strict_help_text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>Higher values</strong> = stricter detection, fewer false positives
                                        </div>
                                        <div class="threshold-indicator mt-2">
                                            <div class="indicator-bar">
                                                <div class="indicator-fill strict-fill" id="strict_indicator"></div>
                                            </div>
                                            <small class="text-muted d-block mt-1" id="strict_description">Maximum accuracy, minimal speculation</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Balanced Mode Slider -->
                                <div class="col-md-4">
                                    <div class="threshold-slider-container">
                                        <label for="hallucination_threshold_balanced" class="form-label d-flex align-items-center justify-content-between">
                                            <span class="fw-bold">
                                                <i class="fas fa-balance-scale me-1 text-warning"></i>
                                                Balanced Mode
                                            </span>
                                            <span class="badge bg-warning text-dark threshold-badge" id="balanced_mode_badge">Moderate</span>
                                        </label>
                                        <div class="slider-wrapper position-relative mb-3">
                                            <input type="range"
                                                id="hallucination_threshold_balanced"
                                                name="hallucination_threshold_balanced"
                                                min="0.2" max="0.8" step="0.05"
                                                value="{{ hallucination_detection.threshold_balanced }}"
                                                class="enhanced-range-slider balanced-slider"
                                                data-slider-type="balanced"
                                                aria-label="Balanced mode hallucination detection threshold"
                                                aria-describedby="balanced_help_text">
                                            <div class="slider-track-fill balanced-track"></div>
                                            <div class="slider-value-tooltip" id="balanced_tooltip">
                                                <span id="hallucination_threshold_balanced_value">{{ hallucination_detection.threshold_balanced }}</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between small text-muted mb-2">
                                            <span class="range-label">0.2<br><small>Permissive</small></span>
                                            <span class="range-label text-center">0.5<br><small>Balanced</small></span>
                                            <span class="range-label text-end">0.8<br><small>Strict</small></span>
                                        </div>
                                        <div class="form-text" id="balanced_help_text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>Moderate sensitivity</strong> = balanced accuracy and helpfulness
                                        </div>
                                        <div class="threshold-indicator mt-2">
                                            <div class="indicator-bar">
                                                <div class="indicator-fill balanced-fill" id="balanced_indicator"></div>
                                            </div>
                                            <small class="text-muted d-block mt-1" id="balanced_description">Moderate accuracy with helpful context</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Default Mode Slider -->
                                <div class="col-md-4">
                                    <div class="threshold-slider-container">
                                        <label for="hallucination_threshold_default" class="form-label d-flex align-items-center justify-content-between">
                                            <span class="fw-bold">
                                                <i class="fas fa-cog me-1 text-success"></i>
                                                Default Mode
                                            </span>
                                            <span class="badge bg-success threshold-badge" id="default_mode_badge">Standard</span>
                                        </label>
                                        <div class="slider-wrapper position-relative mb-3">
                                            <input type="range"
                                                id="hallucination_threshold_default"
                                                name="hallucination_threshold_default"
                                                min="0.3" max="0.8" step="0.05"
                                                value="{{ hallucination_detection.threshold_default }}"
                                                class="enhanced-range-slider default-slider"
                                                data-slider-type="default"
                                                aria-label="Default hallucination detection threshold"
                                                aria-describedby="default_help_text">
                                            <div class="slider-track-fill default-track"></div>
                                            <div class="slider-value-tooltip" id="default_tooltip">
                                                <span id="hallucination_threshold_default_value">{{ hallucination_detection.threshold_default }}</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between small text-muted mb-2">
                                            <span class="range-label">0.3<br><small>Lenient</small></span>
                                            <span class="range-label text-center">0.55<br><small>Standard</small></span>
                                            <span class="range-label text-end">0.8<br><small>Strict</small></span>
                                        </div>
                                        <div class="form-text" id="default_help_text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>Fallback threshold</strong> = used when mode not specified
                                        </div>
                                        <div class="threshold-indicator mt-2">
                                            <div class="indicator-bar">
                                                <div class="indicator-fill default-fill" id="default_indicator"></div>
                                            </div>
                                            <small class="text-muted d-block mt-1" id="default_description">Standard detection for general use</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="min_statement_length" class="form-label">Minimum Statement Length</label>
                                <input type="number" id="min_statement_length" name="min_statement_length"
                                    min="10" max="100" value="{{ hallucination_detection.min_statement_length or 20 }}"
                                    class="form-control" style="max-width: 200px;">
                                <div class="form-text">Statements shorter than this won't be analyzed for hallucinations</div>
                            </div>
                        </div>

                        <!-- Context Processing Settings -->
                        <div class="col-lg-4">
                            <h6 class="fw-bold text-secondary mb-3">
                                <i class="fas fa-brain me-1"></i>
                                Context Processing
                            </h6>
                            <div class="mb-3">
                                <label for="max_vision_context_length" class="form-label">Max Context Length</label>
                                <input type="number" id="max_vision_context_length" name="max_vision_context_length"
                                    min="500" max="5000" value="{{ query_parameters.max_vision_context_length or 2000 }}"
                                    class="form-control">
                                <div class="form-text">Characters for vision analysis</div>
                            </div>
                            <div class="mb-3">
                                <label for="context_truncation_strategy" class="form-label">Truncation Strategy</label>
                                <select id="context_truncation_strategy" name="context_truncation_strategy" class="form-select">
                                    <option value="end" {% if query_parameters.context_truncation_strategy == 'end' %}selected{% endif %}>Truncate at end</option>
                                    <option value="middle" {% if query_parameters.context_truncation_strategy == 'middle' %}selected{% endif %}>Truncate in middle</option>
                                    <option value="smart" {% if query_parameters.context_truncation_strategy == 'smart' %}selected{% endif %}>Smart truncation</option>
                                </select>
                                <div class="form-text">How to handle long context</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Templates & Customization Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white d-flex align-items-center">
                    <i class="fas fa-edit me-2"></i>
                    <h5 class="mb-0">Templates & Customization</h5>
                    <small class="ms-auto text-light opacity-75">Customize prompts and response templates</small>
                </div>
                <div class="card-body">
                    <!-- Prompt Templates -->
                    <div class="mb-4">
                        <h6 class="fw-bold text-info mb-3">
                            <i class="fas fa-code me-1"></i>
                            Prompt Templates
                        </h6>
                        <p class="text-muted small mb-3">Customize the prompt templates for different query types and anti-hallucination modes.</p>

                        <!-- Template Tabs -->
                        <ul class="nav nav-tabs" id="templateTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="templateTabStrict" data-bs-toggle="tab"
                                    data-bs-target="#templateContentStrict" type="button" role="tab">
                                    <i class="fas fa-shield-alt me-1"></i>Strict
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="templateTabBalanced" data-bs-toggle="tab"
                                    data-bs-target="#templateContentBalanced" type="button" role="tab">
                                    <i class="fas fa-balance-scale me-1"></i>Balanced
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="templateTabOff" data-bs-toggle="tab"
                                    data-bs-target="#templateContentOff" type="button" role="tab">
                                    <i class="fas fa-toggle-off me-1"></i>Off
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="templateTabGeneral" data-bs-toggle="tab"
                                    data-bs-target="#templateContentGeneral" type="button" role="tab">
                                    <i class="fas fa-globe me-1"></i>General
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="templateTabDocSpecific" data-bs-toggle="tab"
                                    data-bs-target="#templateContentDocSpecific" type="button" role="tab">
                                    <i class="fas fa-file-alt me-1"></i>Doc-Specific
                                </button>
                            </li>
                        </ul>

                        <!-- Template Content -->
                        <div class="tab-content border border-top-0 p-3" id="templateTabContent">
                            <div class="tab-pane fade show active template-content" id="templateContentStrict" role="tabpanel">
                                <textarea id="template_strict" name="template_strict" rows="8" class="form-control"
                                    placeholder="Enter strict mode template...">{{ prompt_templates.strict }}</textarea>
                                <div class="form-text">Template used when strict anti-hallucination mode is active</div>
                            </div>
                            <div class="tab-pane fade template-content" id="templateContentBalanced" role="tabpanel">
                                <textarea id="template_balanced" name="template_balanced" rows="8" class="form-control"
                                    placeholder="Enter balanced mode template...">{{ prompt_templates.balanced }}</textarea>
                                <div class="form-text">Template used when balanced anti-hallucination mode is active</div>
                            </div>
                            <div class="tab-pane fade template-content" id="templateContentOff" role="tabpanel">
                                <textarea id="template_off" name="template_off" rows="8" class="form-control"
                                    placeholder="Enter off mode template...">{{ prompt_templates.off }}</textarea>
                                <div class="form-text">Template used when anti-hallucination mode is disabled</div>
                            </div>
                            <div class="tab-pane fade template-content" id="templateContentGeneral" role="tabpanel">
                                <textarea id="template_general" name="template_general" rows="8" class="form-control"
                                    placeholder="Enter general query template...">{{ prompt_templates.general }}</textarea>
                                <div class="form-text">Template for general queries across all document types</div>
                            </div>
                            <div class="tab-pane fade template-content" id="templateContentDocSpecific" role="tabpanel">
                                <textarea id="template_doc_specific" name="template_doc_specific" rows="8" class="form-control"
                                    placeholder="Enter document-specific template...">{{ prompt_templates.document_specific }}</textarea>
                                <div class="form-text">Template for queries targeting specific documents</div>
                            </div>
                        </div>
                    </div>

                    <!-- Follow-up Question Templates -->
                    <div class="mb-4">
                        <h6 class="fw-bold text-secondary mb-3">
                            <i class="fas fa-question-circle me-1"></i>
                            Follow-up Question Templates
                        </h6>
                        <p class="text-muted small mb-3">Configure templates for generating follow-up questions when responses are incomplete.</p>

                        <!-- Follow-up Tabs -->
                        <ul class="nav nav-tabs" id="followupTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="followupTabDefault" data-bs-toggle="tab"
                                    data-bs-target="#followupContentDefault" type="button" role="tab">
                                    <i class="fas fa-comments me-1"></i>Default
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="followupTabInsufficient" data-bs-toggle="tab"
                                    data-bs-target="#followupContentInsufficient" type="button" role="tab">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Insufficient Info
                                </button>
                            </li>
                        </ul>

                        <!-- Follow-up Content -->
                        <div class="tab-content border border-top-0 p-3" id="followupTabContent">
                            <div class="tab-pane fade show active followup-content" id="followupContentDefault" role="tabpanel">
                                <textarea id="followup_default" name="followup_default" rows="6" class="form-control"
                                    placeholder="Enter default follow-up template...">{{ followup_question_templates.default }}</textarea>
                                <div class="form-text">Template for generating standard follow-up questions</div>
                            </div>
                            <div class="tab-pane fade followup-content" id="followupContentInsufficient" role="tabpanel">
                                <textarea id="followup_insufficient" name="followup_insufficient" rows="6" class="form-control"
                                    placeholder="Enter insufficient info follow-up template...">{{ followup_question_templates.insufficient_info }}</textarea>
                                <div class="form-text">Template for follow-ups when information is insufficient</div>
                            </div>
                        </div>
                    </div>

                    <!-- Insufficient Information Phrases -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-danger mb-3">
                            <i class="fas fa-search-minus me-1"></i>
                            Insufficient Information Detection
                        </h6>
                        <p class="text-muted small mb-3">Configure phrases used to detect when the system lacks adequate information.</p>

                        <div id="insufficientPhrases" class="mb-3">
                            {% for phrase in insufficient_info_phrases %}
                            <div class="input-group mb-2">
                                <input type="text" name="insufficient_phrase[]" value="{{ phrase }}"
                                    class="form-control" placeholder="Enter a detection phrase...">
                                <button type="button" class="btn btn-outline-danger remove-phrase">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% endfor %}
                        </div>

                        <button type="button" id="addPhrase" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Phrase
                        </button>
                        <div class="form-text">These phrases help identify when the AI should generate follow-up questions</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Integration Settings Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white d-flex align-items-center">
                    <i class="fas fa-plug me-2"></i>
                    <h5 class="mb-0">Integration Settings</h5>
                    <small class="ms-auto text-light opacity-75">Configure external model integrations</small>
                </div>
                <div class="card-body">
                    <!-- Vision Settings for Chat -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-secondary mb-3">
                            <i class="fas fa-eye me-1"></i>
                            Vision Model Integration
                        </h6>
                        <p class="text-muted small mb-3">
                            Configure how the vision model is used when analyzing images during chat.
                            Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported.
                        </p>

                        <div class="alert alert-info d-flex align-items-start">
                            <div class="form-check me-3">
                                <input id="use_vision_chat" name="use_vision" type="checkbox" class="form-check-input"
                                    {% if use_vision %}checked{% endif %}>
                                <label for="use_vision_chat" class="form-check-label fw-bold">
                                    Enable Vision Model for Chat
                                </label>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-2 small">Use vision model to analyze images when responding to user queries.</p>
                                <div class="text-primary small">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    <strong>Dependency:</strong> Requires a vision model to be selected in AI Models tab
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning small mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> Document processing vision settings are configured in the Embedding Configuration tab.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> <!-- End container-fluid -->

<div class="alert alert-info mt-3" style="background:#e3f2fd; color:#1976d2; border:1px solid #90caf9; border-radius:6px; padding:0.75rem 1rem; font-size:0.95rem; font-weight:500;">
    <i class="fa fa-info-circle" aria-hidden="true" style="margin-right:0.5em;"></i>
    <span>After saving, please <strong>refresh the page</strong> to see the updated threshold values reflected in the sliders.</span>
</div>

<!-- Add Save button at the very end of the form -->
<div class="d-flex justify-content-end mt-4 mb-2">
    <button type="button" id="saveQueryConfigBtn" class="btn btn-primary btn-lg px-5">
        <span id="saveBtnText">Save</span>
        <span id="saveBtnSpinner" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
    </button>
</div>

<!-- Custom CSS for Query Configuration -->
<style>
/* Enhanced Core Configuration Header Styling */
.core-config-header {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
    border-bottom: 3px solid #ffc107;
    position: relative;
    overflow: hidden;
}

.core-config-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 75%, transparent);
    background-size: 20px 20px;
    animation: shimmer 3s linear infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-20px); }
    100% { transform: translateX(20px); }
}

.core-config-header h5 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.core-config-header small {
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.core-config-header .fas.fa-star {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    position: relative;
    z-index: 1;
}

/* Enhanced gradient background for Bootstrap classes */
.bg-gradient-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
}

/* Enhanced Preamble Textarea Styling */
.preamble-textarea {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    resize: vertical;
    min-height: 200px;
    max-height: 400px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    transition: all 0.3s ease;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.preamble-textarea:focus {
    background-color: #ffffff;
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    outline: none;
}

.preamble-textarea::placeholder {
    color: #6c757d;
    font-style: italic;
    opacity: 0.8;
}

/* Character counter styling */
.character-counter {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Bootstrap 5 Dark Mode Support */
[data-bs-theme="dark"] .card,
.dark-mode .card,
.dark .card {
    background-color: var(--bs-dark, #212529);
    border-color: var(--bs-gray-700, #495057);
    color: var(--bs-light, #f8f9fa);
}

[data-bs-theme="dark"] .card-header,
.dark-mode .card-header,
.dark .card-header {
    border-color: var(--bs-gray-700, #495057);
}

[data-bs-theme="dark"] .alert-info,
.dark-mode .alert-info,
.dark .alert-info {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: rgba(13, 110, 253, 0.2);
    color: var(--bs-info, #0dcaf0);
}

[data-bs-theme="dark"] .alert-warning,
.dark-mode .alert-warning,
.dark .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
    color: var(--bs-warning, #ffc107);
}

/* Dark mode textarea styling */
[data-bs-theme="dark"] .preamble-textarea,
.dark-mode .preamble-textarea,
.dark .preamble-textarea {
    background-color: #2b3035;
    border-color: #495057;
    color: #f8f9fa;
}

[data-bs-theme="dark"] .preamble-textarea:focus,
.dark-mode .preamble-textarea:focus,
.dark .preamble-textarea:focus {
    background-color: #343a40;
    border-color: #0d6efd;
    color: #f8f9fa;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-bs-theme="dark"] .preamble-textarea::placeholder,
.dark-mode .preamble-textarea::placeholder,
.dark .preamble-textarea::placeholder {
    color: #adb5bd;
}

[data-bs-theme="dark"] .character-counter,
.dark-mode .character-counter,
.dark .character-counter {
    background: rgba(33, 37, 41, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #adb5bd;
}

/* Form control consistency */
[data-bs-theme="dark"] .form-control,
.dark-mode .form-control,
.dark .form-control {
    background-color: #2b3035;
    border-color: #495057;
    color: #f8f9fa;
}

[data-bs-theme="dark"] .form-control:focus,
.dark-mode .form-control:focus,
.dark .form-control:focus {
    background-color: #343a40;
    border-color: #0d6efd;
    color: #f8f9fa;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-bs-theme="dark"] .form-control::placeholder,
.dark-mode .form-control::placeholder,
.dark .form-control::placeholder {
    color: #adb5bd;
    opacity: 1;
}

/* Dark mode Core Configuration header styling */
[data-bs-theme="dark"] .core-config-header,
.dark-mode .core-config-header,
.dark .core-config-header {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%) !important;
    border-bottom-color: #fbbf24;
}

[data-bs-theme="dark"] .core-config-header h5,
.dark-mode .core-config-header h5,
.dark .core-config-header h5 {
    color: #f8fafc;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

[data-bs-theme="dark"] .core-config-header small,
.dark-mode .core-config-header small,
.dark .core-config-header small {
    color: #e2e8f0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

[data-bs-theme="dark"] .bg-gradient-primary,
.dark-mode .bg-gradient-primary,
.dark .bg-gradient-primary {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%) !important;
}

/* Dark mode threshold slider styling */
[data-bs-theme="dark"] .threshold-slider-container,
.dark-mode .threshold-slider-container,
.dark .threshold-slider-container {
    background: rgba(255, 255, 255, 0.02);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .threshold-slider-container:hover,
.dark-mode .threshold-slider-container:hover,
.dark .threshold-slider-container:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .threshold-slider-container::before,
.dark-mode .threshold-slider-container::before,
.dark .threshold-slider-container::before {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

[data-bs-theme="dark"] .slider-value-tooltip,
.dark-mode .slider-value-tooltip,
.dark .slider-value-tooltip {
    background: rgba(255, 255, 255, 0.9);
    color: #1a202c;
}

[data-bs-theme="dark"] .slider-value-tooltip::after,
.dark-mode .slider-value-tooltip::after,
.dark .slider-value-tooltip::after {
    border-top-color: rgba(255, 255, 255, 0.9);
}

[data-bs-theme="dark"] .indicator-bar,
.dark-mode .indicator-bar,
.dark .indicator-bar {
    background: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .range-label,
.dark-mode .range-label,
.dark .range-label {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .range-label small,
.dark-mode .range-label small,
.dark .range-label small {
    color: #a0aec0;
}

/* Enhanced Threshold Slider Styling */
.threshold-slider-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    padding: 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.threshold-slider-container:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.threshold-slider-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer-top 3s ease-in-out infinite;
}

@keyframes shimmer-top {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Enhanced Range Slider Base Styling */
.enhanced-range-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: transparent;
    outline: none;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.enhanced-range-slider:focus {
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
    outline: 2px solid transparent;
}

/* Slider Track Fill */
.slider-track-fill {
    position: absolute;
    top: 50%;
    left: 0;
    height: 8px;
    border-radius: 4px;
    transform: translateY(-50%);
    transition: width 0.1s ease-out;
    z-index: 1;
    pointer-events: none;
    will-change: width;
}

/* Strict Mode Slider (Red Theme) */
.strict-slider {
    background: linear-gradient(to right, #fecaca 0%, #dc2626 50%, #991b1b 100%);
}

.strict-track {
    background: linear-gradient(to right, #dc2626, #991b1b);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.strict-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    transition: all 0.2s ease;
    position: relative;
    z-index: 3;
}

.strict-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.6);
}

.strict-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
    transition: all 0.2s ease;
}

/* Balanced Mode Slider (Yellow Theme) */
.balanced-slider {
    background: linear-gradient(to right, #fef3c7 0%, #f59e0b 50%, #d97706 100%);
}

.balanced-track {
    background: linear-gradient(to right, #f59e0b, #d97706);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.balanced-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
    transition: all 0.2s ease;
    position: relative;
    z-index: 3;
}

.balanced-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.6);
}

.balanced-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
    transition: all 0.2s ease;
}

/* Default Mode Slider (Green Theme) */
.default-slider {
    background: linear-gradient(to right, #d1fae5 0%, #10b981 50%, #059669 100%);
}

.default-track {
    background: linear-gradient(to right, #10b981, #059669);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.default-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981, #059669);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
    transition: all 0.2s ease;
    position: relative;
    z-index: 3;
}

.default-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.6);
}

.default-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981, #059669);
    border: 3px solid #ffffff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
    transition: all 0.2s ease;
}

/* Enhanced Value Tooltip */
.slider-value-tooltip {
    position: absolute;
    top: -45px;
    left: var(--slider-percentage, 50%);
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: bold;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
    z-index: 1000;
    min-width: 50px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
}

.slider-value-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

/* Show tooltip on hover, focus, or during interaction */
.slider-wrapper:hover .slider-value-tooltip,
.enhanced-range-slider:focus + .slider-track-fill + .slider-value-tooltip,
.enhanced-range-slider:active + .slider-track-fill + .slider-value-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Always show tooltip during drag */
.enhanced-range-slider[data-dragging="true"] + .slider-track-fill + .slider-value-tooltip {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(-50%) translateY(-5px) !important;
}

/* Enhanced tooltip animations for real-time updates */
.slider-value-tooltip span {
    display: inline-block;
    transition: transform 0.1s ease;
}

.slider-value-tooltip.updating span {
    transform: scale(1.1);
}

/* Threshold Indicator Bar */
.threshold-indicator {
    margin-top: 0.75rem;
}

.indicator-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.indicator-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.2s ease-out;
    position: relative;
    will-change: width;
}

.strict-fill {
    background: linear-gradient(90deg, #dc2626, #991b1b);
}

.balanced-fill {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.default-fill {
    background: linear-gradient(90deg, #10b981, #059669);
}

.relevance-fill {
    background: linear-gradient(90deg, #0d6efd, #0b5ed7);
}

/* Enhanced performance optimizations for real-time updates */
.enhanced-range-slider[data-dragging="true"] ~ .threshold-indicator .indicator-fill {
    transition: none;
}

.enhanced-range-slider[data-dragging="true"] + .slider-track-fill {
    transition: none;
}

/* Threshold Badge */
.threshold-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: all 0.2s ease;
    will-change: transform, background-color;
}

/* Disable badge animation during drag for better performance */
.enhanced-range-slider[data-dragging="true"] ~ * .threshold-badge {
    animation: none;
}

@keyframes pulse-badge {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Range Labels */
.range-label {
    font-size: 0.75rem;
    line-height: 1.2;
    text-align: center;
}

.range-label small {
    font-size: 0.65rem;
    opacity: 0.7;
}

/* Range slider styling (fallback) */
.form-range::-webkit-slider-thumb {
    background-color: var(--bs-primary);
}

.form-range::-moz-range-thumb {
    background-color: var(--bs-primary);
    border: none;
}

/* Tab content transitions */
.tab-content .tab-pane {
    transition: opacity 0.3s ease-in-out;
}

/* Card hover effects */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Core Configuration card styling */
.core-config-header + .card-body {
    border-top: 2px solid #ffc107;
    background: linear-gradient(to bottom, rgba(13, 110, 253, 0.02) 0%, transparent 100%);
}

[data-bs-theme="dark"] .core-config-header + .card-body,
.dark-mode .core-config-header + .card-body,
.dark .core-config-header + .card-body {
    background: linear-gradient(to bottom, rgba(30, 64, 175, 0.05) 0%, transparent 100%);
    border-top-color: #fbbf24;
}

/* Form validation styling */
.is-invalid {
    border-color: var(--bs-danger);
}

.invalid-feedback {
    display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header small {
        display: none;
    }

    .col-sm-6 {
        margin-bottom: 1rem;
    }

    /* Mobile threshold slider adjustments */
    .threshold-slider-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .threshold-slider-container .form-label {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }

    .threshold-badge {
        align-self: flex-start;
        margin-top: 0.25rem;
    }

    .enhanced-range-slider::-webkit-slider-thumb {
        width: 28px;
        height: 28px;
    }

    .enhanced-range-slider::-moz-range-thumb {
        width: 28px;
        height: 28px;
    }

    .slider-value-tooltip {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        top: -40px;
    }

    .range-label {
        font-size: 0.7rem;
    }

    .range-label small {
        font-size: 0.6rem;
    }
}

@media (max-width: 576px) {
    .threshold-slider-container {
        padding: 0.75rem;
    }

    .enhanced-range-slider {
        height: 10px;
    }

    .slider-track-fill {
        height: 10px;
    }

    .enhanced-range-slider::-webkit-slider-thumb {
        width: 32px;
        height: 32px;
    }

    .enhanced-range-slider::-moz-range-thumb {
        width: 32px;
        height: 32px;
    }

    .slider-value-tooltip {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
    }
}
</style>

<script>
// Enhanced JavaScript for Query Configuration with Bootstrap 5 support
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips if available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Enhanced Range slider value updates with animation
    function setupRangeSlider(sliderId, displayId) {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(displayId);

        if (slider && display) {
            // Set initial value
            display.textContent = slider.value;

            slider.addEventListener('input', function() {
                display.textContent = this.value;
                display.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    display.style.transform = 'scale(1)';
                }, 150);
            });
        }
    }

    // Note: Enhanced threshold slider setup is now handled by unified_config.js
    // This prevents duplicate event listeners and conflicts

    // All threshold slider functions moved to unified_config.js to prevent conflicts

    // All range sliders now handled by unified_config.js enhanced setup

    // Enhanced form validation
    function validateDocumentLimits() {
        const minDocsInput = document.getElementById('min_documents');
        const maxDocsInput = document.getElementById('max_documents');

        if (minDocsInput && maxDocsInput) {
            const minVal = parseInt(minDocsInput.value);
            const maxVal = parseInt(maxDocsInput.value);

            // Remove existing validation classes
            minDocsInput.classList.remove('is-invalid');
            maxDocsInput.classList.remove('is-invalid');

            // Remove existing feedback
            const existingFeedback = maxDocsInput.parentNode.querySelector('.invalid-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }

            if (minVal > maxVal) {
                maxDocsInput.classList.add('is-invalid');
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Maximum documents must be greater than or equal to minimum documents';
                maxDocsInput.parentNode.appendChild(feedback);
                return false;
            }
        }
        return true;
    }

    // Add validation event listeners
    const minDocsInput = document.getElementById('min_documents');
    const maxDocsInput = document.getElementById('max_documents');

    if (minDocsInput) {
        minDocsInput.addEventListener('change', validateDocumentLimits);
        minDocsInput.addEventListener('input', validateDocumentLimits);
    }
    if (maxDocsInput) {
        maxDocsInput.addEventListener('change', validateDocumentLimits);
        maxDocsInput.addEventListener('input', validateDocumentLimits);
    }

    // Enhanced phrase management for insufficient information detection
    function setupPhraseManagement() {
        const addPhraseBtn = document.getElementById('addPhrase');
        const phrasesContainer = document.getElementById('insufficientPhrases');

        if (addPhraseBtn && phrasesContainer) {
            addPhraseBtn.addEventListener('click', function() {
                const newPhraseGroup = document.createElement('div');
                newPhraseGroup.className = 'input-group mb-2';
                newPhraseGroup.innerHTML = `
                    <input type="text" name="insufficient_phrase[]" class="form-control"
                        placeholder="Enter a detection phrase...">
                    <button type="button" class="btn btn-outline-danger remove-phrase">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                // Add animation
                newPhraseGroup.style.opacity = '0';
                newPhraseGroup.style.transform = 'translateY(-10px)';
                phrasesContainer.appendChild(newPhraseGroup);

                // Animate in
                setTimeout(() => {
                    newPhraseGroup.style.transition = 'all 0.3s ease';
                    newPhraseGroup.style.opacity = '1';
                    newPhraseGroup.style.transform = 'translateY(0)';
                }, 10);

                // Add remove functionality
                const removeBtn = newPhraseGroup.querySelector('.remove-phrase');
                removeBtn.addEventListener('click', function() {
                    newPhraseGroup.style.transition = 'all 0.3s ease';
                    newPhraseGroup.style.opacity = '0';
                    newPhraseGroup.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        phrasesContainer.removeChild(newPhraseGroup);
                    }, 300);
                });
            });

            // Add remove functionality to existing phrases
            phrasesContainer.addEventListener('click', function(e) {
                if (e.target.closest('.remove-phrase')) {
                    const phraseGroup = e.target.closest('.input-group');
                    phraseGroup.style.transition = 'all 0.3s ease';
                    phraseGroup.style.opacity = '0';
                    phraseGroup.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        if (phraseGroup.parentNode) {
                            phraseGroup.parentNode.removeChild(phraseGroup);
                        }
                    }, 300);
                }
            });
        }
    }

    setupPhraseManagement();

    // Enhanced Preamble Textarea functionality
    setupPreambleTextarea();

    // Form submission validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateDocumentLimits() || !validatePreamble()) {
                e.preventDefault();
                // Scroll to the invalid field
                const invalidField = document.querySelector('.is-invalid');
                if (invalidField) {
                    invalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    }

    // Auto-save indication (if needed)
    function showSaveIndicator() {
        // This can be implemented to show save status
        console.log('Configuration updated');
    }

    // Add change listeners to all form inputs for auto-save indication
    const formInputs = document.querySelectorAll('input, textarea, select');
    formInputs.forEach(input => {
        input.addEventListener('change', showSaveIndicator);
    });

    // Enhanced Preamble Textarea Setup
    function setupPreambleTextarea() {
        const preambleTextarea = document.getElementById('preamble');
        const charCountElement = document.getElementById('preamble-char-count');
        const templateBtn = document.getElementById('preamble-template-btn');
        const previewBtn = document.getElementById('preamble-preview-btn');

        if (preambleTextarea && charCountElement) {
            // Character counter functionality
            function updateCharCount() {
                const count = preambleTextarea.value.length;
                charCountElement.textContent = count;

                // Color coding based on length
                if (count < 100) {
                    charCountElement.className = 'text-warning';
                } else if (count > 2000) {
                    charCountElement.className = 'text-danger';
                } else {
                    charCountElement.className = 'text-success';
                }
            }

            // Initial count
            updateCharCount();

            // Update on input
            preambleTextarea.addEventListener('input', updateCharCount);
            preambleTextarea.addEventListener('paste', function() {
                setTimeout(updateCharCount, 10);
            });

            // Auto-resize functionality
            preambleTextarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 400) + 'px';
            });
        }

        // Template loading functionality
        if (templateBtn) {
            templateBtn.addEventListener('click', function() {
                const templates = {
                    'academic': `You are an AI assistant specialized in analyzing academic and research documents from the ERDB Knowledge Products database. When responding to queries:

1. Base your answers strictly on the provided document content
2. Cite specific sources with page numbers when available
3. If information is insufficient, clearly state this limitation
4. Maintain a professional, academic tone
5. Provide evidence-based insights and recommendations
6. Highlight research methodologies and findings
7. Note any limitations or gaps in the research

Always prioritize accuracy and scholarly rigor over completeness.`,

                    'business': `You are an AI assistant specialized in analyzing business and policy documents from the ERDB Knowledge Products database. When responding to queries:

1. Focus on actionable business insights and policy implications
2. Cite specific sources with page numbers when available
3. If information is insufficient, clearly state this limitation
4. Maintain a professional, consultative tone
5. Provide strategic recommendations based on the evidence
6. Highlight key performance indicators and metrics
7. Consider economic and market implications

Always prioritize practical applicability and strategic value.`,

                    'technical': `You are an AI assistant specialized in analyzing technical and scientific documents from the ERDB Knowledge Products database. When responding to queries:

1. Provide precise, technically accurate information
2. Cite specific sources with page numbers when available
3. If information is insufficient, clearly state this limitation
4. Maintain a clear, technical tone appropriate for experts
5. Include relevant technical specifications and procedures
6. Highlight methodologies and technical approaches
7. Note any technical limitations or requirements

Always prioritize technical accuracy and precision.`
                };

                // Create modal for template selection
                const modalHtml = `
                    <div class="modal fade" id="templateModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Preamble Template</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="list-group">
                                        <button type="button" class="list-group-item list-group-item-action" data-template="academic">
                                            <strong>Academic/Research</strong><br>
                                            <small class="text-muted">For scholarly documents and research papers</small>
                                        </button>
                                        <button type="button" class="list-group-item list-group-item-action" data-template="business">
                                            <strong>Business/Policy</strong><br>
                                            <small class="text-muted">For business documents and policy papers</small>
                                        </button>
                                        <button type="button" class="list-group-item list-group-item-action" data-template="technical">
                                            <strong>Technical/Scientific</strong><br>
                                            <small class="text-muted">For technical manuals and scientific documents</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                const existingModal = document.getElementById('templateModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Add modal to body
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('templateModal'));
                modal.show();

                // Handle template selection
                document.querySelectorAll('[data-template]').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const templateType = this.dataset.template;
                        preambleTextarea.value = templates[templateType];
                        updateCharCount();
                        modal.hide();

                        // Show success message
                        showToast('Template loaded successfully!', 'success');
                    });
                });
            });
        }

        // Preview functionality
        if (previewBtn) {
            previewBtn.addEventListener('click', function() {
                const content = preambleTextarea.value;
                if (!content.trim()) {
                    showToast('Please enter some preamble text to preview.', 'warning');
                    return;
                }

                // Create preview modal
                const previewModalHtml = `
                    <div class="modal fade" id="previewModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Preamble Preview</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        This is how your preamble text will appear to the AI model:
                                    </div>
                                    <div class="bg-light p-3 rounded border" style="white-space: pre-wrap; font-family: monospace;">${content}</div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>Character count:</strong> ${content.length} characters<br>
                                            <strong>Word count:</strong> ${content.split(/\s+/).filter(word => word.length > 0).length} words<br>
                                            <strong>Lines:</strong> ${content.split('\n').length} lines
                                        </small>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                const existingModal = document.getElementById('previewModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Add modal to body
                document.body.insertAdjacentHTML('beforeend', previewModalHtml);

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('previewModal'));
                modal.show();
            });
        }
    }

    // Preamble validation
    function validatePreamble() {
        const preambleTextarea = document.getElementById('preamble');
        if (preambleTextarea) {
            const content = preambleTextarea.value.trim();

            // Remove existing validation classes
            preambleTextarea.classList.remove('is-invalid');
            const existingFeedback = preambleTextarea.parentNode.querySelector('.invalid-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }

            if (content.length < 50) {
                preambleTextarea.classList.add('is-invalid');
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Preamble text should be at least 50 characters long for effective AI guidance.';
                preambleTextarea.parentNode.appendChild(feedback);
                return false;
            }

            if (content.length > 3000) {
                preambleTextarea.classList.add('is-invalid');
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Preamble text should not exceed 3000 characters to avoid token limits.';
                preambleTextarea.parentNode.appendChild(feedback);
                return false;
            }
        }
        return true;
    }

    // Toast notification helper
    function showToast(message, type = 'info') {
        // Simple toast implementation - can be enhanced with proper toast library
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
        `;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Declare form variable once at the top
    // const form = document.querySelector('form') || document.body; // fallback if not in a <form>
    // Use the already declared 'form' variable above

    async function fetchAndUpdateConfig() {
        try {
            const response = await fetch('/api/settings/query_config', { method: 'GET' });
            if (!response.ok) throw new Error('Failed to fetch updated config');
            const data = await response.json();
            // Update all relevant UI fields with new values
            // Example for sliders/inputs:
            if (data.hallucination_detection) {
                document.getElementById('hallucination_threshold_strict').value = data.hallucination_detection.threshold_strict;
                document.getElementById('hallucination_threshold_balanced').value = data.hallucination_detection.threshold_balanced;
                document.getElementById('hallucination_threshold_default').value = data.hallucination_detection.threshold_default;
                document.getElementById('min_statement_length').value = data.hallucination_detection.min_statement_length;
            }
            // Add more fields as needed (preamble, retrieval_k, etc.)
            if (data.query_parameters) {
                document.getElementById('retrieval_k').value = data.query_parameters.retrieval_k;
                document.getElementById('relevance_threshold').value = data.query_parameters.relevance_threshold;
                document.getElementById('min_documents').value = data.query_parameters.min_documents;
                document.getElementById('max_documents').value = data.query_parameters.max_documents;
                document.getElementById('max_pdf_images_display').value = data.query_parameters.max_pdf_images_display;
                document.getElementById('max_url_images_display').value = data.query_parameters.max_url_images_display;
                document.getElementById('max_tables_display').value = data.query_parameters.max_tables_display;
                document.getElementById('max_pdf_links_display').value = data.query_parameters.max_pdf_links_display;
                document.getElementById('max_vision_context_length').value = data.query_parameters.max_vision_context_length;
                document.getElementById('context_truncation_strategy').value = data.query_parameters.context_truncation_strategy;
            }
            if (data.preamble !== undefined) {
                document.getElementById('preamble').value = data.preamble;
            }
            // Add more as needed for full sync
        } catch (err) {
            showToast('Failed to refresh settings: ' + err.message, 'danger');
        }
    }

    // Define saveBtn and related elements before using them
    const saveBtn = document.getElementById('saveQueryConfigBtn');
    const saveBtnText = document.getElementById('saveBtnText');
    const saveBtnSpinner = document.getElementById('saveBtnSpinner');

    if (saveBtn) {
        saveBtn.addEventListener('click', async function() {
            // Disable button and show spinner
            saveBtn.disabled = true;
            saveBtnSpinner.classList.remove('d-none');
            saveBtnText.textContent = 'Saving...';
            try {
                // Serialize all form data
                const formData = new FormData(form);
                const data = {};
                formData.forEach((value, key) => {
                    // Handle multiple values (e.g., arrays)
                    if (data[key]) {
                        if (!Array.isArray(data[key])) data[key] = [data[key]];
                        data[key].push(value);
                    } else {
                        data[key] = value;
                    }
                });
                // POST to backend
                const response = await fetch('/api/settings/query_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                if (!response.ok) throw new Error('Failed to save settings');
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                // Fetch and update UI with latest config
                await fetchAndUpdateConfig();
                showToast('Settings updated!', 'success');
            } catch (err) {
                showToast('Save failed: ' + err.message, 'danger');
            } finally {
                saveBtn.disabled = false;
                saveBtnSpinner.classList.add('d-none');
                saveBtnText.textContent = 'Save';
            }
        });
    }
});
</script>
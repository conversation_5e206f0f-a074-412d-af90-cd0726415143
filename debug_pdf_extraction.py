#!/usr/bin/env python3
"""
Debug script for PDF title/author extraction
Analyzes the problematic canopy_vol45n1.pdf with detailed debug output
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.pdf_processor import (
    extract_titles_and_authors_from_ocr_pdf,
    extract_titles_and_authors_from_pdf,
    extract_text_with_font_sizes_ocr
)

def debug_pdf_extraction():
    """Debug the PDF extraction for canopy_vol45n1.pdf"""
    
    # Path to the problematic PDF
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"
    
    print("=" * 80)
    print("🔍 DEBUGGING PDF EXTRACTION")
    print("=" * 80)
    print(f"PDF Path: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    print()
    
    if not os.path.exists(pdf_path):
        print("❌ PDF file not found!")
        return
    
    # Test 1: OCR-based extraction with debug
    print("=" * 80)
    print("📄 TEST 1: OCR-BASED EXTRACTION")
    print("=" * 80)
    
    try:
        ocr_articles = extract_titles_and_authors_from_ocr_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,  # Don't skip first page for debugging
            min_title_font=8,  # Very permissive
            min_title_length=2,  # Very permissive
            max_title_gap=100,  # Very permissive
            max_author_gap=120,  # Very permissive
            min_upper_ratio=0.3,  # Very permissive
            min_author_font=8,  # Very permissive
            max_author_font=20,  # Very permissive
            title_font_ratio=0.6  # Very permissive
        )
        
        print("\n" + "=" * 80)
        print("📊 OCR EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(ocr_articles)} articles")
        
        for i, article in enumerate(ocr_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ OCR extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("📄 TEST 2: NATIVE PDF EXTRACTION")
    print("=" * 80)
    
    try:
        native_articles = extract_titles_and_authors_from_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,  # Don't skip first page for debugging
            min_title_font=8,  # Very permissive
            min_title_length=2,  # Very permissive
            max_title_gap=100,  # Very permissive
            max_author_gap=120,  # Very permissive
            min_upper_ratio=0.3,  # Very permissive
            min_author_font=8,  # Very permissive
            max_author_font=20,  # Very permissive
            title_font_ratio=0.6  # Very permissive
        )
        
        print("\n" + "=" * 80)
        print("📊 NATIVE PDF EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(native_articles)} articles")
        
        for i, article in enumerate(native_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ Native PDF extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("📄 TEST 3: RAW OCR TEXT BLOCKS (First 2 pages)")
    print("=" * 80)
    
    try:
        # Get raw OCR text blocks for first 2 pages
        ocr_text_data = extract_text_with_font_sizes_ocr(pdf_path, debug=True)
        
        print(f"\nFound {len(ocr_text_data)} pages with OCR data")
        
        for page_data in ocr_text_data[:2]:  # First 2 pages only
            page_num = page_data["page"]
            font_analysis = page_data.get("font_size_analysis", {})
            text_blocks = font_analysis.get("text_blocks", [])
            
            print(f"\n📄 Page {page_num}: {len(text_blocks)} text blocks")
            print("-" * 60)
            
            # Show first 20 text blocks
            for i, block in enumerate(text_blocks[:20]):
                text = block.get('text', '')
                font_size = block.get('font_size', 0)
                bbox = block.get('bbox', [0,0,0,0])
                confidence = block.get('confidence', 100)
                
                print(f"{i+1:2d}. Font: {font_size:2.0f}pt | Pos: ({bbox[0]:4.0f}, {bbox[1]:4.0f}) | Conf: {confidence:3.0f}% | '{text}'")
            
            if len(text_blocks) > 20:
                print(f"... and {len(text_blocks) - 20} more blocks")
                
    except Exception as e:
        print(f"❌ Raw OCR extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("✅ DEBUG COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    debug_pdf_extraction() 
<!-- models_config_partial.html: Modular AI Models Configuration Section -->
<div class="config-section">
    <h2 class="config-section-header">LLM Models</h2>
    <p class="config-section-description">Select the language model used for generating responses to user queries.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for model in models %}
            <div class="relative">
                <input type="radio" id="llm_{{ loop.index }}" name="llm_model" value="{{ model.name }}"
                       class="hidden peer" {% if model.name == selected_model %}checked{% endif %}>
                <label for="llm_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if model.name == selected_model %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if model.name == default_llm %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ model.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ model.size | filesizeformat }}
                    </div>
                    {% if model.name == default_llm %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Embedding Models</h2>
    <p class="config-section-description">Select the model used for generating vector embeddings of documents.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for embedding in embeddings %}
            <div class="relative">
                <input type="radio" id="embed_{{ loop.index }}" name="embedding_model" value="{{ embedding.name }}"
                       class="hidden peer" {% if embedding.name == selected_embedding %}checked{% endif %}>
                <label for="embed_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if embedding.name == selected_embedding %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if embedding.name == default_embedding %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ embedding.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ embedding.size | filesizeformat }}
                    </div>
                    {% if embedding.name == default_embedding %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Vision Models</h2>
    <p class="config-section-description">Select the model used for analyzing images in documents.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for vision in vision_models %}
            <div class="relative">
                <input type="radio" id="vision_{{ loop.index }}" name="vision_model" value="{{ vision.name }}"
                       class="hidden peer" {% if vision.name == selected_vision %}checked{% endif %}>
                <label for="vision_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if vision.name == selected_vision %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if vision.name == default_vision %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ vision.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ vision.size | filesizeformat }}
                    </div>
                    {% if vision.name == default_vision %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Vision Model</h2>
    <p class="config-section-description">Select the vision model used for analyzing images in chat responses. Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported.</p>
    <div class="flex flex-col space-y-4">
        <label class="inline-flex items-center">
            <input type="checkbox" name="use_vision" class="form-checkbox h-5 w-5 text-blue-600"
                   {% if use_vision %}checked{% endif %}>
            <span class="ml-2 text-gray-700 dark:text-gray-300">Enable Vision Model for Chat</span>
        </label>
        <div class="dependency-indicator">
            <span class="text-blue-600 dark:text-blue-400">↑</span> Controls whether images are analyzed during chat
        </div>
    </div>
</div>

<!-- LlamaIndex Integration Section -->
<div class="config-section">
    <h2 class="config-section-header">LlamaIndex Integration</h2>
    <p class="config-section-description">Configure LlamaIndex + LangChain hybrid processing for enhanced document analysis and query capabilities.</p>
    
    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg border border-blue-200 dark:border-blue-600 mb-4">
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="llamaindex_integration_enabled" name="llamaindex_integration_enabled" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if llamaindex_config.enabled %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="llamaindex_integration_enabled" class="font-medium text-gray-700 dark:text-gray-300">Enable LlamaIndex Hybrid Processing</label>
                <p class="text-gray-600 dark:text-gray-400">Enable enhanced document processing using LlamaIndex + LangChain integration for improved query accuracy and context understanding.</p>
                <div class="mt-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        {% if llamaindex_config.enabled %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200{% else %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200{% endif %}">
                        {% if llamaindex_config.enabled %}LlamaIndex Enabled{% else %}LlamaIndex Disabled{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- LlamaIndex Model Compatibility -->
    <div class="mb-4">
        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Model Compatibility</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">LLM Models</h4>
                <div class="space-y-1">
                    {% for model in models %}
                    <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full {% if 'llama' in model.name.lower() %}bg-green-500{% else %}bg-gray-400{% endif %} mr-2"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-400">{{ model.name }}</span>
                        {% if 'llama' in model.name.lower() %}
                        <span class="ml-2 text-xs text-green-600 dark:text-green-400">✓ LlamaIndex Compatible</span>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Embedding Models</h4>
                <div class="space-y-1">
                    {% for embedding in embeddings %}
                    <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full {% if 'llama' in embedding.name.lower() %}bg-green-500{% else %}bg-gray-400{% endif %} mr-2"></span>
                        <span class="text-sm text-gray-600 dark:text-gray-400">{{ embedding.name }}</span>
                        {% if 'llama' in embedding.name.lower() %}
                        <span class="ml-2 text-xs text-green-600 dark:text-green-400">✓ LlamaIndex Compatible</span>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- LlamaIndex Performance Indicator -->
    <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg border border-yellow-200 dark:border-yellow-600">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">LlamaIndex Processing</h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>LlamaIndex processing may increase response times but provides enhanced accuracy and context understanding. For detailed LlamaIndex configuration, use the dedicated LlamaIndex tab.</p>
                </div>
            </div>
        </div>
    </div>
</div>
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Model Parameters</h2>
    <p class="config-section-description">Configure parameters for the LLM model to adjust its behavior for each anti-hallucination mode.</p>
    <div>
        <ul class="flex border-b mb-4" id="modelParamTabs" role="tablist">
            <li class="mr-2"><button class="tab-active px-4 py-2 rounded-t-lg" id="strict-tab" data-mode="strict" type="button">Strict</button></li>
            <li class="mr-2"><button class="tab-inactive px-4 py-2 rounded-t-lg" id="balanced-tab" data-mode="balanced" type="button">Balanced</button></li>
            <li class="mr-2"><button class="tab-inactive px-4 py-2 rounded-t-lg" id="off-tab" data-mode="off" type="button">Off</button></li>
        </ul>
        <div id="modelParamTabContents">
            {% for mode in ['strict', 'balanced', 'off'] %}
            <div class="mode-params-tab {% if mode != 'strict' %}hidden{% endif %}" id="params-{{ mode }}">
                <h3 class="text-lg font-semibold mb-2 capitalize">{{ mode }} Mode Parameters</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                    <!-- Temperature -->
                    <div>
                        <label for="{{ mode }}_temperature" class="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
                        <input type="range" id="{{ mode }}_temperature" name="{{ mode }}_temperature" min="0" max="1" step="0.05"
                               value="{{ model_parameters[mode].temperature|default(0.7) }}" class="form-range mr-3">
                        <span class="badge bg-secondary">{{ model_parameters[mode].temperature|default(0.7) }}</span>
                        <small class="text-muted">Controls randomness (0 = deterministic, 1 = creative)</small>
                    </div>
                    <!-- Top P -->
                    <div>
                        <label for="{{ mode }}_top_p" class="block text-sm font-medium text-gray-700 mb-1">Top P</label>
                        <input type="number" id="{{ mode }}_top_p" name="{{ mode }}_top_p" min="0" max="1" step="0.01" value="{{ model_parameters[mode].top_p|default(0.9) }}"
                               class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                        <small class="text-muted">Limits nucleus sampling to the smallest set whose cumulative probability exceeds this value</small>
                    </div>
                    <!-- Top K -->
                    <div>
                        <label for="{{ mode }}_top_k" class="block text-sm font-medium text-gray-700 mb-1">Top K</label>
                        <input type="number" id="{{ mode }}_top_k" name="{{ mode }}_top_k" min="1" max="100" step="1" value="{{ model_parameters[mode].top_k|default(40) }}"
                               class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                        <small class="text-muted">Limits sampling to the top K most likely tokens</small>
                    </div>
                    <!-- Repeat Penalty -->
                    <div>
                        <label for="{{ mode }}_repeat_penalty" class="block text-sm font-medium text-gray-700 mb-1">Repeat Penalty</label>
                        <input type="number" id="{{ mode }}_repeat_penalty" name="{{ mode }}_repeat_penalty" min="1" max="2" step="0.01" value="{{ model_parameters[mode].repeat_penalty|default(1.1) }}"
                               class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                        <small class="text-muted">Penalizes repeated tokens (higher = less repetition)</small>
                    </div>
                    <!-- Context Window -->
                    <div>
                        <label for="{{ mode }}_num_ctx" class="block text-sm font-medium text-gray-700 mb-1">Context Window (Tokens)</label>
                        <input type="number" id="{{ mode }}_num_ctx" name="{{ mode }}_num_ctx" min="512" max="32768" step="1" value="{{ model_parameters[mode].num_ctx|default(4096) }}"
                               class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                        <small class="text-muted">Maximum number of tokens the model can consider at once</small>
                    </div>
                    <!-- Max Tokens to Predict -->
                    <div>
                        <label for="{{ mode }}_num_predict" class="block text-sm font-medium text-gray-700 mb-1">Max Tokens to Predict</label>
                        <input type="number" id="{{ mode }}_num_predict" name="{{ mode }}_num_predict" min="64" max="4096" step="1" value="{{ model_parameters[mode].num_predict|default(256) }}"
                               class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                        <small class="text-muted">Maximum number of tokens the model will generate in a response</small>
                    </div>
                    <!-- System Prompt -->
                    <div class="col-span-1 md:col-span-2">
                        <label for="{{ mode }}_system_prompt" class="block text-sm font-medium text-gray-700 mb-1">System Prompt</label>
                        <textarea id="{{ mode }}_system_prompt" name="{{ mode }}_system_prompt" rows="3"
                                  class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">{{ model_parameters[mode].system_prompt|default('You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.') }}</textarea>
                        <small class="text-muted">Instructions for the model's behavior and persona</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
<script>
// Tab switching for model parameter modes
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('#modelParamTabs button');
        const tabContents = document.querySelectorAll('.mode-params-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                tabs.forEach(t => t.classList.remove('tab-active'));
                this.classList.add('tab-active');
                const mode = this.getAttribute('data-mode');
                tabContents.forEach(content => {
                    if (content.id === `params-${mode}`) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });
    });
}
</script>

<div class="flex justify-end mt-6">
    <button type="button" id="saveModelsBtn"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
        Save Models Settings
    </button>
</div> 
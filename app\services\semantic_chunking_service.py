"""
Semantic Chunking Service for ERDB AI Cursor

This service implements a hybrid semantic chunking pipeline that combines:
1. Structural pre-splitting (headers, paragraphs, sections)
2. Semantic similarity analysis
3. RAG-optimized final chunking
4. Rich metadata enrichment

The pipeline is designed to create more meaningful, contextually coherent chunks
that preserve document structure while optimizing for retrieval relevance.
"""

import os
import logging
import re
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import defaultdict

# LangChain imports
from langchain.text_splitter import (
    RecursiveCharacterTextSplitter,
    MarkdownHeaderTextSplitter,
    HTMLHeaderTextSplitter
)
from langchain.schema import Document
from langchain.text_splitter import Language

# spaCy for NLP processing
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

# Sentence transformers for semantic similarity
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None

# Performance monitoring
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """Available chunking strategies."""
    RECURSIVE = "recursive"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"
    STRUCTURAL = "structural"


class ContentType(Enum):
    """Content type classification."""
    SCIENTIFIC_PAPER = "scientific_paper"
    GENERAL_DOCUMENT = "general_document"
    WEB_CONTENT = "web_content"
    TECHNICAL_DOCUMENT = "technical_document"
    UNKNOWN = "unknown"


@dataclass
class ChunkingConfig:
    """Configuration for semantic chunking."""
    strategy: ChunkingStrategy = ChunkingStrategy.HYBRID
    chunk_size: int = 800
    chunk_overlap: int = 160
    similarity_threshold: float = 0.7
    max_chunk_size: int = 1200
    min_chunk_size: int = 200
    structural_split: bool = True
    semantic_refinement: bool = True
    metadata_enrichment: bool = True
    preserve_hierarchy: bool = True
    extract_keywords: bool = True
    content_type_detection: bool = True
    
    # Enhanced metadata extraction options
    extract_article_metadata: bool = True
    extract_publication_info: bool = True
    validate_metadata: bool = True
    confidence_threshold: float = 0.7
    enable_cross_reference: bool = True


@dataclass
class ChunkMetadata:
    """Enhanced metadata for chunks."""
    chunk_id: str
    parent_section: Optional[str] = None
    section_level: int = 0
    content_type: ContentType = ContentType.UNKNOWN
    semantic_keywords: List[str] = None
    structural_tags: List[str] = None
    chunk_quality_score: float = 0.0
    word_count: int = 0
    sentence_count: int = 0
    has_tables: bool = False
    has_images: bool = False
    has_links: bool = False


@dataclass
class ArticleMetadata:
    """Enhanced article metadata extracted using semantic analysis."""
    title: Optional[str] = None
    authors: List[str] = None
    affiliations: List[str] = None
    abstract: Optional[str] = None
    keywords: List[str] = None
    publication_info: Dict[str, Any] = None
    confidence_scores: Dict[str, float] = None
    extraction_method: str = "semantic"
    
    def __post_init__(self):
        if self.authors is None:
            self.authors = []
        if self.affiliations is None:
            self.affiliations = []
        if self.keywords is None:
            self.keywords = []
        if self.publication_info is None:
            self.publication_info = {}
        if self.confidence_scores is None:
            self.confidence_scores = {}


@dataclass
class DocumentStructure:
    """Document structure information."""
    sections: List[Dict[str, Any]] = None
    hierarchy: Dict[str, Any] = None
    section_types: Dict[str, str] = None
    title_candidates: List[str] = None
    author_candidates: List[str] = None
    
    def __post_init__(self):
        if self.sections is None:
            self.sections = []
        if self.hierarchy is None:
            self.hierarchy = {}
        if self.section_types is None:
            self.section_types = {}
        if self.title_candidates is None:
            self.title_candidates = []
        if self.author_candidates is None:
            self.author_candidates = []


class SemanticChunkingService:
    """
    Hybrid semantic chunking service that combines structural and semantic analysis.
    """
    
    def __init__(self, config: Optional[ChunkingConfig] = None):
        """Initialize the semantic chunking service."""
        self.config = config or ChunkingConfig()
        self.nlp = None
        self.sentence_model = None
        
        # Initialize NLP components
        self._initialize_nlp()
        
        # Initialize splitters
        self._initialize_splitters()
        
        logger.info(f"SemanticChunkingService initialized with strategy: {self.config.strategy}")
    
    def _initialize_nlp(self):
        """Initialize NLP components if available."""
        if SPACY_AVAILABLE:
            try:
                # Try to load English model, fallback to basic if not available
                self.nlp = spacy.load("en_core_web_sm")
                logger.info("spaCy English model loaded successfully")
            except OSError:
                logger.warning("spaCy English model not found. Installing basic model...")
                try:
                    import subprocess
                    subprocess.run(["python", "-m", "spacy", "download", "en_core_web_sm"], 
                                 check=True, capture_output=True)
                    self.nlp = spacy.load("en_core_web_sm")
                    logger.info("spaCy English model installed and loaded")
                except Exception as e:
                    logger.warning(f"Failed to install spaCy model: {e}. Continuing without NLP features.")
                    self.nlp = None
        else:
            logger.warning("spaCy not available. NLP features will be disabled.")
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                # Use a lightweight model for semantic similarity
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("Sentence transformer model loaded successfully")
            except Exception as e:
                logger.warning(f"Failed to load sentence transformer: {e}")
                self.sentence_model = None
        else:
            logger.warning("Sentence transformers not available. Semantic similarity will be disabled.")
    
    def _initialize_splitters(self):
        """Initialize text splitters."""
        # Recursive character splitter for size control
        self.recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ". ", "! ", "? ", " ", ""]
        )
        
        # Markdown header splitter for structural analysis
        self.markdown_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("#", "Header 1"),
                ("##", "Header 2"),
                ("###", "Header 3"),
                ("####", "Header 4"),
            ]
        )
        
        # HTML header splitter for web content
        self.html_splitter = HTMLHeaderTextSplitter(
            headers_to_split_on=[
                ("h1", "Header 1"),
                ("h2", "Header 2"),
                ("h3", "Header 3"),
                ("h4", "Header 4"),
            ]
        )
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def chunk_documents(self, documents: List[Document], content_type: Optional[ContentType] = None) -> List[Document]:
        """
        Main method to chunk documents using the configured strategy.
        
        Args:
            documents: List of LangChain Document objects
            content_type: Optional content type classification
            
        Returns:
            List of chunked Document objects with enhanced metadata
        """
        logger.info(f"Chunking {len(documents)} documents with strategy: {self.config.strategy}")
        
        if self.config.strategy == ChunkingStrategy.RECURSIVE:
            return self._recursive_chunking(documents)
        elif self.config.strategy == ChunkingStrategy.SEMANTIC:
            return self._semantic_chunking(documents, content_type)
        elif self.config.strategy == ChunkingStrategy.STRUCTURAL:
            return self._structural_chunking(documents, content_type)
        elif self.config.strategy == ChunkingStrategy.HYBRID:
            return self._hybrid_chunking(documents, content_type)
        else:
            raise ValueError(f"Unknown chunking strategy: {self.config.strategy}")
    
    def _recursive_chunking(self, documents: List[Document]) -> List[Document]:
        """Standard recursive character chunking."""
        logger.info("Using recursive character chunking")
        chunks = []
        
        for doc in documents:
            doc_chunks = self.recursive_splitter.split_documents([doc])
            # Add basic metadata
            for i, chunk in enumerate(doc_chunks):
                chunk.metadata.update({
                    "chunking_strategy": "recursive",
                    "chunk_id": f"{doc.metadata.get('source', 'unknown')}_chunk_{i}",
                    "chunk_index": i,
                    "total_chunks": len(doc_chunks)
                })
            chunks.extend(doc_chunks)
        
        return chunks
    
    def _structural_chunking(self, documents: List[Document], content_type: Optional[ContentType] = None) -> List[Document]:
        """Structural chunking based on document headers and sections."""
        logger.info("Using structural chunking")
        chunks = []
        
        for doc in documents:
            # Detect content type if not provided
            if content_type is None:
                content_type = self._detect_content_type(doc.page_content)
            
            # Choose appropriate splitter based on content type
            if self._is_markdown_content(doc.page_content):
                doc_chunks = self.markdown_splitter.split_text(doc.page_content)
                splitter_type = "markdown"
            elif self._is_html_content(doc.page_content):
                doc_chunks = self.html_splitter.split_text(doc.page_content)
                splitter_type = "html"
            else:
                # Fallback to recursive splitting
                doc_chunks = self.recursive_splitter.split_documents([doc])
                splitter_type = "recursive"
            
            # Convert to Document objects and add metadata
            for i, chunk in enumerate(doc_chunks):
                if isinstance(chunk, str):
                    chunk_doc = Document(
                        page_content=chunk,
                        metadata=doc.metadata.copy()
                    )
                else:
                    chunk_doc = chunk
                
                chunk_doc.metadata.update({
                    "chunking_strategy": "structural",
                    "splitter_type": splitter_type,
                    "chunk_id": f"{doc.metadata.get('source', 'unknown')}_structural_{i}",
                    "chunk_index": i,
                    "content_type": content_type.value,
                    "total_chunks": len(doc_chunks)
                })
                
                # Add structural metadata if available
                if hasattr(chunk_doc, 'metadata') and 'Header' in chunk_doc.metadata:
                    chunk_doc.metadata['section_header'] = chunk_doc.metadata['Header']
                
                chunks.append(chunk_doc)
        
        return chunks
    
    def _semantic_chunking(self, documents: List[Document], content_type: Optional[ContentType] = None) -> List[Document]:
        """Semantic chunking based on meaning and similarity."""
        logger.info("Using semantic chunking")
        
        if not self.sentence_model:
            logger.warning("Sentence transformer not available, falling back to recursive chunking")
            return self._recursive_chunking(documents)
        
        chunks = []
        
        for doc in documents:
            # Split into sentences first
            sentences = self._split_into_sentences(doc.page_content)
            
            if len(sentences) <= 1:
                # Single sentence or very short content, use recursive chunking
                doc_chunks = self.recursive_splitter.split_documents([doc])
                for i, chunk in enumerate(doc_chunks):
                    chunk.metadata.update({
                        "chunking_strategy": "semantic_fallback",
                        "chunk_id": f"{doc.metadata.get('source', 'unknown')}_semantic_{i}",
                        "chunk_index": i,
                        "total_chunks": len(doc_chunks)
                    })
                chunks.extend(doc_chunks)
                continue
            
            # Group sentences by semantic similarity
            semantic_chunks = self._group_sentences_by_similarity(sentences)
            
            # Convert to Document objects
            for i, chunk_sentences in enumerate(semantic_chunks):
                chunk_text = " ".join(chunk_sentences)
                
                # Ensure chunk size is within limits
                if len(chunk_text) > self.config.max_chunk_size:
                    # Split large chunks further
                    sub_chunks = self.recursive_splitter.split_text(chunk_text)
                    for j, sub_chunk in enumerate(sub_chunks):
                        chunk_doc = Document(
                            page_content=sub_chunk,
                            metadata=doc.metadata.copy()
                        )
                        chunk_doc.metadata.update({
                            "chunking_strategy": "semantic",
                            "chunk_id": f"{doc.metadata.get('source', 'unknown')}_semantic_{i}_{j}",
                            "chunk_index": f"{i}_{j}",
                            "semantic_group": i,
                            "content_type": content_type.value if content_type else "unknown"
                        })
                        chunks.append(chunk_doc)
                else:
                    chunk_doc = Document(
                        page_content=chunk_text,
                        metadata=doc.metadata.copy()
                    )
                    chunk_doc.metadata.update({
                        "chunking_strategy": "semantic",
                        "chunk_id": f"{doc.metadata.get('source', 'unknown')}_semantic_{i}",
                        "chunk_index": i,
                        "semantic_group": i,
                        "content_type": content_type.value if content_type else "unknown"
                    })
                    chunks.append(chunk_doc)
        
        return chunks
    
    def _hybrid_chunking(self, documents: List[Document], content_type: Optional[ContentType] = None) -> List[Document]:
        """
        Hybrid chunking: structural pre-splitting + semantic refinement.
        This is the most sophisticated approach combining the best of both worlds.
        """
        logger.info("Using hybrid semantic chunking")
        chunks = []
        
        for doc in documents:
            # Step 1: Structural pre-splitting
            structural_chunks = self._structural_pre_split(doc, content_type)
            
            # Step 2: Semantic refinement within structural units
            refined_chunks = self._semantic_refinement(structural_chunks)
            
            # Step 3: Final size optimization and metadata enrichment
            final_chunks = self._final_optimization(refined_chunks)
            
            chunks.extend(final_chunks)
        
        return chunks
    
    def _structural_pre_split(self, doc: Document, content_type: Optional[ContentType] = None) -> List[Document]:
        """Step 1: Split document into structural units."""
        if not self.config.structural_split:
            return [doc]
        
        # Detect content type
        if content_type is None:
            content_type = self._detect_content_type(doc.page_content)
        
        # Choose structural splitter
        if self._is_markdown_content(doc.page_content):
            structural_texts = self.markdown_splitter.split_text(doc.page_content)
            splitter_type = "markdown"
        elif self._is_html_content(doc.page_content):
            structural_texts = self.html_splitter.split_text(doc.page_content)
            splitter_type = "html"
        else:
            # For plain text, try to identify sections by headers
            structural_texts = self._identify_text_sections(doc.page_content)
            splitter_type = "text_sections"
        
        # Convert to Document objects
        structural_chunks = []
        for i, text in enumerate(structural_texts):
            chunk_doc = Document(
                page_content=text,
                metadata=doc.metadata.copy()
            )
            chunk_doc.metadata.update({
                "structural_split": True,
                "splitter_type": splitter_type,
                "structural_index": i,
                "content_type": content_type.value,
                "section_level": self._determine_section_level(text, splitter_type)
            })
            structural_chunks.append(chunk_doc)
        
        return structural_chunks
    
    def _semantic_refinement(self, structural_chunks: List[Document]) -> List[Document]:
        """Step 2: Apply semantic refinement within structural units."""
        if not self.config.semantic_refinement or not self.sentence_model:
            return structural_chunks
        
        refined_chunks = []
        
        for structural_chunk in structural_chunks:
            # Split into sentences
            sentences = self._split_into_sentences(structural_chunk.page_content)
            
            if len(sentences) <= 1:
                # Single sentence, keep as is
                refined_chunks.append(structural_chunk)
                continue
            
            # Group sentences by semantic similarity
            semantic_groups = self._group_sentences_by_similarity(sentences)
            
            # Create refined chunks
            for i, group in enumerate(semantic_groups):
                chunk_text = " ".join(group)
                chunk_doc = Document(
                    page_content=chunk_text,
                    metadata=structural_chunk.metadata.copy()
                )
                chunk_doc.metadata.update({
                    "semantic_refinement": True,
                    "semantic_group": i,
                    "sentences_in_group": len(group),
                    "semantic_similarity_score": self._calculate_group_similarity(group)
                })
                refined_chunks.append(chunk_doc)
        
        return refined_chunks
    
    def _final_optimization(self, chunks: List[Document]) -> List[Document]:
        """Step 3: Final size optimization and metadata enrichment."""
        optimized_chunks = []
        
        for chunk in chunks:
            # Check if chunk needs size optimization
            if len(chunk.page_content) > self.config.max_chunk_size:
                # Split large chunks
                sub_chunks = self.recursive_splitter.split_text(chunk.page_content)
                for i, sub_text in enumerate(sub_chunks):
                    sub_chunk = Document(
                        page_content=sub_text,
                        metadata=chunk.metadata.copy()
                    )
                    sub_chunk.metadata.update({
                        "size_optimized": True,
                        "sub_chunk_index": i,
                        "total_sub_chunks": len(sub_chunks)
                    })
                    optimized_chunks.append(sub_chunk)
            elif len(chunk.page_content) < self.config.min_chunk_size:
                # Skip very small chunks unless they're the only content
                if len(optimized_chunks) == 0:
                    optimized_chunks.append(chunk)
            else:
                optimized_chunks.append(chunk)
        
        # Enrich metadata
        if self.config.metadata_enrichment:
            for chunk in optimized_chunks:
                self._enrich_chunk_metadata(chunk)
        
        return optimized_chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using spaCy or regex fallback."""
        if self.nlp:
            doc = self.nlp(text)
            sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]
        else:
            # Regex fallback for sentence splitting
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]
        
        return sentences
    
    def _group_sentences_by_similarity(self, sentences: List[str]) -> List[List[str]]:
        """Group sentences by semantic similarity."""
        if not self.sentence_model or len(sentences) <= 1:
            return [sentences]
        
        # Get sentence embeddings
        embeddings = self.sentence_model.encode(sentences)
        
        # Group sentences by similarity
        groups = []
        used_indices = set()
        
        for i in range(len(sentences)):
            if i in used_indices:
                continue
            
            # Start a new group
            group = [sentences[i]]
            used_indices.add(i)
            
            # Find similar sentences
            for j in range(i + 1, len(sentences)):
                if j in used_indices:
                    continue
                
                # Calculate cosine similarity
                similarity = self._cosine_similarity(embeddings[i], embeddings[j])
                
                if similarity >= self.config.similarity_threshold:
                    group.append(sentences[j])
                    used_indices.add(j)
            
            groups.append(group)
        
        return groups
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    
    def _calculate_group_similarity(self, sentences: List[str]) -> float:
        """Calculate average similarity within a sentence group."""
        if len(sentences) <= 1:
            return 1.0
        
        if not self.sentence_model:
            return 0.5  # Default score
        
        embeddings = self.sentence_model.encode(sentences)
        similarities = []
        
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity = self._cosine_similarity(embeddings[i], embeddings[j])
                similarities.append(similarity)
        
        return np.mean(similarities) if similarities else 0.5
    
    def _detect_content_type(self, text: str) -> ContentType:
        """Detect the type of content based on text analysis."""
        # Simple heuristics for content type detection
        text_lower = text.lower()
        
        # Scientific paper indicators
        scientific_indicators = [
            'abstract', 'introduction', 'methodology', 'results', 'discussion', 'conclusion',
            'references', 'bibliography', 'doi:', 'issn:', 'volume', 'issue', 'journal',
            'university', 'department', 'research', 'study', 'experiment'
        ]
        
        scientific_score = sum(1 for indicator in scientific_indicators if indicator in text_lower)
        
        # Technical document indicators
        technical_indicators = [
            'api', 'function', 'class', 'method', 'parameter', 'configuration',
            'installation', 'setup', 'tutorial', 'guide', 'manual', 'documentation'
        ]
        
        technical_score = sum(1 for indicator in technical_indicators if indicator in text_lower)
        
        # Web content indicators
        web_indicators = [
            'html', 'http', 'www', 'website', 'webpage', 'link', 'click', 'browse'
        ]
        
        web_score = sum(1 for indicator in web_indicators if indicator in text_lower)
        
        # Determine content type
        if scientific_score >= 3:
            return ContentType.SCIENTIFIC_PAPER
        elif technical_score >= 3:
            return ContentType.TECHNICAL_DOCUMENT
        elif web_score >= 2:
            return ContentType.WEB_CONTENT
        else:
            return ContentType.GENERAL_DOCUMENT
    
    def _is_markdown_content(self, text: str) -> bool:
        """Check if text contains markdown formatting."""
        markdown_patterns = [
            r'^#+\s+',  # Headers
            r'\*\*.*?\*\*',  # Bold
            r'\*.*?\*',  # Italic
            r'\[.*?\]\(.*?\)',  # Links
            r'```',  # Code blocks
        ]
        
        return any(re.search(pattern, text, re.MULTILINE) for pattern in markdown_patterns)
    
    def _is_html_content(self, text: str) -> bool:
        """Check if text contains HTML tags."""
        html_patterns = [
            r'<[^>]+>',  # HTML tags
            r'&[a-zA-Z]+;',  # HTML entities
        ]
        
        return any(re.search(pattern, text) for pattern in html_patterns)
    
    def _identify_text_sections(self, text: str) -> List[str]:
        """Identify sections in plain text based on headers and formatting."""
        # Split by potential section headers
        lines = text.split('\n')
        sections = []
        current_section = []
        
        for line in lines:
            # Check if line looks like a header
            if self._is_header_line(line):
                if current_section:
                    sections.append('\n'.join(current_section))
                    current_section = []
            current_section.append(line)
        
        # Add the last section
        if current_section:
            sections.append('\n'.join(current_section))
        
        return sections if sections else [text]
    
    def _is_header_line(self, line: str) -> bool:
        """Check if a line looks like a header."""
        line = line.strip()
        
        # All caps line
        if line.isupper() and len(line) > 3 and len(line) < 100:
            return True
        
        # Numbered section
        if re.match(r'^\d+\.\s+[A-Z]', line):
            return True
        
        # Short line with capital letters
        if len(line) < 50 and line[0].isupper() and line.isupper():
            return True
        
        return False
    
    def _determine_section_level(self, text: str, splitter_type: str) -> int:
        """Determine the hierarchical level of a section."""
        if splitter_type == "markdown":
            # Count leading # symbols
            lines = text.split('\n')
            for line in lines:
                if line.strip().startswith('#'):
                    return line.count('#', 0, line.find(' '))
        elif splitter_type == "html":
            # Look for h1, h2, h3, etc. tags
            for i in range(1, 7):
                if f'<h{i}' in text:
                    return i
        
        return 0
    
    def _enrich_chunk_metadata(self, chunk: Document):
        """Enrich chunk with additional metadata."""
        text = chunk.page_content
        
        # Basic statistics
        chunk.metadata.update({
            "word_count": len(text.split()),
            "character_count": len(text),
            "sentence_count": len(self._split_into_sentences(text)),
            "chunk_quality_score": self._calculate_chunk_quality(text)
        })
        
        # Content type detection
        if self.config.content_type_detection:
            chunk.metadata["content_type"] = self._detect_content_type(text).value
        
        # Keyword extraction
        if self.config.extract_keywords and self.nlp:
            keywords = self._extract_keywords(text)
            chunk.metadata["semantic_keywords"] = keywords
        
        # Structural tags
        structural_tags = []
        if self._contains_tables(text):
            structural_tags.append("table")
        if self._contains_images(text):
            structural_tags.append("image")
        if self._contains_links(text):
            structural_tags.append("link")
        
        chunk.metadata["structural_tags"] = structural_tags
    
    def _calculate_chunk_quality(self, text: str) -> float:
        """Calculate a quality score for the chunk."""
        if not text.strip():
            return 0.0
        
        # Simple quality metrics
        word_count = len(text.split())
        sentence_count = len(self._split_into_sentences(text))
        
        # Penalize very short or very long chunks
        if word_count < 10:
            return 0.3
        elif word_count > 500:
            return 0.7
        
        # Reward chunks with multiple sentences
        if sentence_count > 1:
            return min(1.0, 0.5 + (sentence_count * 0.1))
        
        return 0.5
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text using spaCy."""
        if not self.nlp:
            return []
        
        doc = self.nlp(text)
        keywords = []
        
        # Extract nouns, proper nouns, and adjectives
        for token in doc:
            if (token.pos_ in ['NOUN', 'PROPN', 'ADJ'] and 
                not token.is_stop and 
                len(token.text) > 2):
                keywords.append(token.text.lower())
        
        # Return top keywords
        return list(set(keywords))[:10]
    
    def _contains_tables(self, text: str) -> bool:
        """Check if text contains table-like content."""
        table_indicators = ['|', '\t', 'table', 'row', 'column']
        return any(indicator in text.lower() for indicator in table_indicators)
    
    def _contains_images(self, text: str) -> bool:
        """Check if text contains image references."""
        image_indicators = ['image', 'img', 'figure', 'fig.', 'photo', 'picture']
        return any(indicator in text.lower() for indicator in image_indicators)
    
    def _contains_links(self, text: str) -> bool:
        """Check if text contains links."""
        link_patterns = [r'http[s]?://', r'www\.', r'\[.*?\]\(.*?\)']
        return any(re.search(pattern, text) for pattern in link_patterns)

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def extract_article_metadata(self, documents: List[Document]) -> ArticleMetadata:
        """
        Extract article titles, authors, and publication metadata using semantic analysis.
        
        Args:
            documents: List of documents to process
            
        Returns:
            ArticleMetadata object with extracted information and confidence scores
        """
        logger.info(f"Extracting article metadata from {len(documents)} documents")
        
        # Step 1: Analyze document structure
        doc_structure = self._analyze_document_structure(documents)
        
        # Step 2: Extract metadata using semantic analysis
        metadata = self._extract_metadata_semantically(documents, doc_structure)
        
        # Step 3: Validate and cross-reference metadata
        if self.config.validate_metadata:
            metadata = self._validate_metadata(metadata, documents, doc_structure)
        
        logger.info(f"Extracted metadata: title='{metadata.title}', "
                   f"authors={len(metadata.authors)}, confidence={metadata.confidence_scores}")
        
        return metadata

    def _analyze_document_structure(self, documents: List[Document]) -> DocumentStructure:
        """Analyze document structure to identify sections and hierarchy."""
        structure = DocumentStructure()
        
        for doc in documents:
            text = doc.page_content
            
            # Identify sections using structural analysis
            sections = self._identify_document_sections(text)
            structure.sections.extend(sections)
            
            # Extract title and author candidates
            title_candidates = self._extract_title_candidates(text)
            author_candidates = self._extract_author_candidates(text)
            
            structure.title_candidates.extend(title_candidates)
            structure.author_candidates.extend(author_candidates)
            
            # Classify section types
            for section in sections:
                section_type = self._classify_section_type(section['content'])
                structure.section_types[section['id']] = section_type
        
        # Build hierarchy
        structure.hierarchy = self._build_document_hierarchy(structure.sections)
        
        return structure

    def _identify_document_sections(self, text: str) -> List[Dict[str, Any]]:
        """Identify document sections using structural analysis."""
        sections = []
        lines = text.split('\n')
        current_section = None
        section_id = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Check if line is a section header
            if self._is_section_header(line):
                # Save previous section
                if current_section:
                    sections.append(current_section)
                
                # Start new section
                section_id += 1
                current_section = {
                    'id': f'section_{section_id}',
                    'header': line,
                    'content': line,
                    'start_line': i,
                    'level': self._get_header_level(line)
                }
            elif current_section:
                # Add content to current section
                current_section['content'] += '\n' + line
        
        # Add final section
        if current_section:
            sections.append(current_section)
        
        return sections

    def _is_section_header(self, line: str) -> bool:
        """Check if a line is a section header."""
        # Markdown headers
        if re.match(r'^#{1,6}\s+', line):
            return True
        
        # Numbered sections
        if re.match(r'^\d+\.\s+[A-Z]', line):
            return True
        
        # All caps headers (common in scientific papers)
        if line.isupper() and len(line) > 3 and len(line) < 100:
            return True
        
        # Common section keywords
        section_keywords = [
            'abstract', 'introduction', 'methodology', 'methods', 'results',
            'discussion', 'conclusion', 'references', 'bibliography',
            'acknowledgments', 'appendix', 'figure', 'table'
        ]
        
        line_lower = line.lower()
        return any(keyword in line_lower for keyword in section_keywords)

    def _get_header_level(self, header: str) -> int:
        """Get the hierarchical level of a header."""
        if header.startswith('#'):
            return header.count('#', 0, header.find(' '))
        elif re.match(r'^\d+\.', header):
            return len(header.split('.')[0])
        else:
            return 1

    def _extract_title_candidates(self, text: str) -> List[str]:
        """Extract potential title candidates from text."""
        candidates = []
        
        # Pattern 1: Markdown headers
        header_pattern = r'^#{1,3}\s+(.+)$'
        headers = re.findall(header_pattern, text, re.MULTILINE)
        candidates.extend(headers)
        
        # Pattern 2: All caps lines (common for titles)
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if (line.isupper() and len(line) > 10 and len(line) < 200 and
                not line.startswith('ABSTRACT') and not line.startswith('INTRODUCTION')):
                candidates.append(line)
        
        # Pattern 3: Lines with title-like characteristics
        for line in lines:
            line = line.strip()
            if (len(line) > 10 and len(line) < 200 and
                line[0].isupper() and line[-1] not in '.!?' and
                not line.startswith('By ') and not line.startswith('Authors: <AUTHORS>
                candidates.append(line)
        
        return list(set(candidates))  # Remove duplicates

    def _extract_author_candidates(self, text: str) -> List[str]:
        """Extract potential author candidates from text."""
        candidates = []
        
        # Pattern 1: "Authors: <AUTHORS>
        author_patterns = [
            r'(?:Authors?|By):\s*(.+)',
            r'(?:Written by|Prepared by):\s*(.+)',
            r'(?:Author|Authors):\s*(.+)'
        ]
        
        for pattern in author_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                # Split by common separators
                names = re.split(r'[,;&]|\band\b', match)
                candidates.extend([name.strip() for name in names if name.strip()])
        
        # Pattern 2: Email patterns (often contain author names)
        email_pattern = r'([a-zA-Z]+\.[a-zA-Z]+@[a-zA-Z]+\.[a-zA-Z]+)'
        emails = re.findall(email_pattern, text)
        for email in emails:
            name_part = email.split('@')[0]
            if '.' in name_part:
                name = name_part.replace('.', ' ').title()
                candidates.append(name)
        
        # Pattern 3: Affiliation patterns (often contain author names)
        affiliation_pattern = r'([A-Z][a-z]+\s+[A-Z][a-z]+)\s+(?:University|Institute|Department|Center)'
        affiliations = re.findall(affiliation_pattern, text)
        candidates.extend(affiliations)
        
        return list(set(candidates))  # Remove duplicates

    def _classify_section_type(self, content: str) -> str:
        """Classify the type of a document section."""
        content_lower = content.lower()
        
        # Abstract section
        if 'abstract' in content_lower[:100]:
            return 'abstract'
        
        # Introduction section
        if 'introduction' in content_lower[:100]:
            return 'introduction'
        
        # Methods/Methodology section
        if any(keyword in content_lower[:100] for keyword in ['method', 'methodology', 'materials']):
            return 'methods'
        
        # Results section
        if 'result' in content_lower[:100]:
            return 'results'
        
        # Discussion section
        if 'discussion' in content_lower[:100]:
            return 'discussion'
        
        # Conclusion section
        if 'conclusion' in content_lower[:100]:
            return 'conclusion'
        
        # References section
        if any(keyword in content_lower[:100] for keyword in ['reference', 'bibliography', 'cited']):
            return 'references'
        
        return 'content'

    def _build_document_hierarchy(self, sections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build document hierarchy from sections."""
        hierarchy = {'root': {'children': [], 'level': 0}}
        
        for section in sections:
            level = section.get('level', 1)
            section_id = section['id']
            
            # Find parent at appropriate level
            parent = self._find_parent_in_hierarchy(hierarchy, level - 1)
            if parent:
                if 'children' not in parent:
                    parent['children'] = []
                parent['children'].append({
                    'id': section_id,
                    'header': section['header'],
                    'level': level,
                    'type': section.get('type', 'content')
                })
        
        return hierarchy

    def _find_parent_in_hierarchy(self, hierarchy: Dict[str, Any], target_level: int) -> Optional[Dict[str, Any]]:
        """Find parent node at target level in hierarchy."""
        def find_recursive(node, current_level):
            if current_level == target_level:
                return node
            
            if 'children' in node:
                for child in node['children']:
                    result = find_recursive(child, current_level + 1)
                    if result:
                        return result
            
            return None
        
        return find_recursive(hierarchy['root'], 0)

    def _extract_metadata_semantically(self, documents: List[Document], 
                                     doc_structure: DocumentStructure) -> ArticleMetadata:
        """Extract metadata using semantic analysis."""
        metadata = ArticleMetadata()
        
        # Extract title using semantic analysis
        metadata.title = self._extract_title_semantically(documents, doc_structure)
        
        # Extract authors using semantic analysis
        metadata.authors = self._extract_authors_semantically(documents, doc_structure)
        
        # Extract affiliations
        metadata.affiliations = self._extract_affiliations_semantically(documents, doc_structure)
        
        # Extract abstract
        metadata.abstract = self._extract_abstract_semantically(documents, doc_structure)
        
        # Extract keywords
        metadata.keywords = self._extract_keywords_semantically(documents, doc_structure)
        
        # Extract publication information
        metadata.publication_info = self._extract_publication_info_semantically(documents, doc_structure)
        
        return metadata

    def _extract_title_semantically(self, documents: List[Document], 
                                   doc_structure: DocumentStructure) -> Optional[str]:
        """Extract title using semantic analysis."""
        if not doc_structure.title_candidates:
            return None
        
        # Score candidates based on multiple factors
        scored_candidates = []
        
        for candidate in doc_structure.title_candidates:
            score = 0.0
            
            # Factor 1: Length (titles are typically 10-200 characters)
            if 10 <= len(candidate) <= 200:
                score += 0.3
            
            # Factor 2: Position (titles are often at the beginning)
            for doc in documents:
                if candidate in doc.page_content[:1000]:  # First 1000 characters
                    score += 0.2
            
            # Factor 3: Capitalization pattern
            if candidate[0].isupper() and not candidate.isupper():
                score += 0.2
            
            # Factor 4: No ending punctuation
            if not candidate.endswith(('.', '!', '?')):
                score += 0.1
            
            # Factor 5: Semantic relevance (if we have sentence transformers)
            if self.sentence_model:
                # Check if candidate is semantically similar to common title patterns
                title_embeddings = self.sentence_model.encode([candidate])
                # This is a simplified check - in practice, you'd compare against a corpus of titles
                score += 0.2
            
            scored_candidates.append((candidate, score))
        
        # Return the highest scoring candidate
        if scored_candidates:
            best_candidate = max(scored_candidates, key=lambda x: x[1])
            return best_candidate[0]
        
        return None

    def _extract_authors_semantically(self, documents: List[Document], 
                                    doc_structure: DocumentStructure) -> List[str]:
        """Extract authors using semantic analysis."""
        if not doc_structure.author_candidates:
            return []
        
        # Score and validate author candidates
        valid_authors = []
        
        for candidate in doc_structure.author_candidates:
            if self._is_valid_author_name(candidate):
                valid_authors.append(candidate)
        
        # Remove duplicates and similar names
        unique_authors = self._deduplicate_authors(valid_authors)
        
        return unique_authors

    def _is_valid_author_name(self, name: str) -> bool:
        """Check if a name is a valid author name."""
        # Basic validation rules
        if len(name) < 3 or len(name) > 100:
            return False
        
        # Should contain at least one space (first and last name)
        if ' ' not in name:
            return False
        
        # Should not contain numbers or special characters (except for some names)
        if re.search(r'\d', name):
            return False
        
        # Should start with capital letters
        words = name.split()
        if not all(word[0].isupper() for word in words):
            return False
        
        # Should not be common words
        common_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with']
        if any(word.lower() in common_words for word in words):
            return False
        
        return True

    def _deduplicate_authors(self, authors: List[str]) -> List[str]:
        """Remove duplicate and similar author names."""
        unique_authors = []
        
        for author in authors:
            # Check if this author is similar to any existing author
            is_duplicate = False
            for existing_author in unique_authors:
                similarity = self._calculate_name_similarity(author, existing_author)
                if similarity > 0.8:  # 80% similarity threshold
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_authors.append(author)
        
        return unique_authors

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two author names."""
        # Simple similarity calculation
        words1 = set(name1.lower().split())
        words2 = set(name2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)

    def _extract_affiliations_semantically(self, documents: List[Document], 
                                         doc_structure: DocumentStructure) -> List[str]:
        """Extract author affiliations using semantic analysis."""
        affiliations = []
        
        # Look for affiliation patterns in the document
        affiliation_patterns = [
            r'([A-Z][a-zA-Z\s&]+(?:University|Institute|Department|Center|School|College|Laboratory|Lab))',
            r'([A-Z][a-zA-Z\s&]+(?:Corporation|Company|Inc|Ltd|LLC))',
            r'([A-Z][a-zA-Z\s&]+(?:Hospital|Medical Center|Clinic))'
        ]
        
        for doc in documents:
            text = doc.page_content
            for pattern in affiliation_patterns:
                matches = re.findall(pattern, text)
                affiliations.extend([match.strip() for match in matches])
        
        # Remove duplicates
        return list(set(affiliations))

    def _extract_abstract_semantically(self, documents: List[Document], 
                                     doc_structure: DocumentStructure) -> Optional[str]:
        """Extract abstract using semantic analysis."""
        # Look for abstract section
        for section in doc_structure.sections:
            if doc_structure.section_types.get(section['id']) == 'abstract':
                return section['content']
        
        # Fallback: look for abstract-like content
        for doc in documents:
            text = doc.page_content
            # Look for "Abstract:" followed by content
            abstract_match = re.search(r'Abstract:?\s*(.+?)(?=\n\n|\n[A-Z]|$)', text, re.DOTALL | re.IGNORECASE)
            if abstract_match:
                return abstract_match.group(1).strip()
        
        return None

    def _extract_keywords_semantically(self, documents: List[Document], 
                                     doc_structure: DocumentStructure) -> List[str]:
        """Extract keywords using semantic analysis."""
        keywords = []
        
        # Look for keywords section
        for doc in documents:
            text = doc.page_content
            # Look for "Keywords:" followed by keywords
            keywords_match = re.search(r'Keywords:?\s*(.+?)(?=\n\n|\n[A-Z]|$)', text, re.DOTALL | re.IGNORECASE)
            if keywords_match:
                keywords_text = keywords_match.group(1)
                # Split by common separators
                keywords.extend([kw.strip() for kw in re.split(r'[,;]', keywords_text) if kw.strip()])
        
        # Also extract keywords using NLP if available
        if self.nlp and self.config.extract_keywords:
            for doc in documents:
                doc_keywords = self._extract_keywords(doc.page_content)
                keywords.extend(doc_keywords)
        
        # Remove duplicates and limit to top keywords
        unique_keywords = list(set(keywords))
        return unique_keywords[:20]  # Limit to top 20 keywords

    def _extract_publication_info_semantically(self, documents: List[Document], 
                                             doc_structure: DocumentStructure) -> Dict[str, Any]:
        """Extract publication information using semantic analysis."""
        publication_info = {}
        
        for doc in documents:
            text = doc.page_content
            
            # Extract journal/conference name
            journal_patterns = [
                r'(?:Journal|Conference|Proceedings|Transactions)\s+of\s+([A-Z][a-zA-Z\s&]+)',
                r'([A-Z][a-zA-Z\s&]+)\s+(?:Journal|Conference|Proceedings)',
                r'(?:Published in|Appeared in)\s+([A-Z][a-zA-Z\s&]+)'
            ]
            
            for pattern in journal_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    publication_info['journal'] = match.group(1).strip()
                    break
            
            # Extract publication date
            date_patterns = [
                r'(\d{4})',  # Year
                r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
                r'(\d{4})-(\d{1,2})-(\d{1,2})'  # YYYY-MM-DD
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    if len(match.groups()) == 1:
                        publication_info['year'] = match.group(1)
                    else:
                        publication_info['date'] = match.group(0)
                    break
            
            # Extract DOI
            doi_match = re.search(r'DOI:?\s*(10\.\d+/[^\s]+)', text, re.IGNORECASE)
            if doi_match:
                publication_info['doi'] = doi_match.group(1)
            
            # Extract volume/issue
            volume_match = re.search(r'Volume\s+(\d+)', text, re.IGNORECASE)
            if volume_match:
                publication_info['volume'] = volume_match.group(1)
            
            issue_match = re.search(r'Issue\s+(\d+)', text, re.IGNORECASE)
            if issue_match:
                publication_info['issue'] = issue_match.group(1)
        
        return publication_info

    def _validate_metadata(self, metadata: ArticleMetadata, documents: List[Document], 
                          doc_structure: DocumentStructure) -> ArticleMetadata:
        """Validate and cross-reference extracted metadata."""
        # Validate title
        if metadata.title:
            metadata.confidence_scores['title'] = self._validate_title(metadata.title, documents)
        
        # Validate authors
        if metadata.authors:
            metadata.confidence_scores['authors'] = self._validate_authors(metadata.authors, documents)
        
        # Validate abstract
        if metadata.abstract:
            metadata.confidence_scores['abstract'] = self._validate_abstract(metadata.abstract, documents)
        
        # Cross-reference information
        if self.config.enable_cross_reference:
            metadata = self._cross_reference_metadata(metadata, documents, doc_structure)
        
        return metadata

    def _validate_title(self, title: str, documents: List[Document]) -> float:
        """Validate title and return confidence score."""
        score = 0.0
        
        # Check if title appears in multiple places
        title_mentions = 0
        for doc in documents:
            if title.lower() in doc.page_content.lower():
                title_mentions += 1
        
        if title_mentions > 1:
            score += 0.3
        
        # Check title characteristics
        if 10 <= len(title) <= 200:
            score += 0.2
        
        if title[0].isupper() and not title.isupper():
            score += 0.2
        
        if not title.endswith(('.', '!', '?')):
            score += 0.1
        
        # Check semantic coherence (if sentence transformers available)
        if self.sentence_model:
            # This is a simplified check - in practice, you'd compare against title corpus
            score += 0.2
        
        return min(score, 1.0)

    def _validate_authors(self, authors: List[str], documents: List[Document]) -> float:
        """Validate authors and return confidence score."""
        if not authors:
            return 0.0
        
        score = 0.0
        
        # Check if authors appear in multiple places
        author_mentions = 0
        for author in authors:
            for doc in documents:
                if author.lower() in doc.page_content.lower():
                    author_mentions += 1
        
        if author_mentions > len(authors):
            score += 0.3
        
        # Check author name validity
        valid_authors = sum(1 for author in authors if self._is_valid_author_name(author))
        score += (valid_authors / len(authors)) * 0.4
        
        # Check for email patterns (often indicate valid authors)
        email_pattern = r'[a-zA-Z]+\.[a-zA-Z]+@[a-zA-Z]+\.[a-zA-Z]+'
        emails = re.findall(email_pattern, str(documents))
        if emails:
            score += 0.3
        
        return min(score, 1.0)

    def _validate_abstract(self, abstract: str, documents: List[Document]) -> float:
        """Validate abstract and return confidence score."""
        score = 0.0
        
        # Check if abstract appears in document
        for doc in documents:
            if abstract.lower() in doc.page_content.lower():
                score += 0.4
        
        # Check abstract characteristics
        if 50 <= len(abstract) <= 2000:
            score += 0.2
        
        if abstract.endswith('.'):
            score += 0.1
        
        # Check for abstract-like content
        abstract_keywords = ['study', 'research', 'investigate', 'analyze', 'examine', 'present']
        if any(keyword in abstract.lower() for keyword in abstract_keywords):
            score += 0.3
        
        return min(score, 1.0)

    def _cross_reference_metadata(self, metadata: ArticleMetadata, documents: List[Document], 
                                 doc_structure: DocumentStructure) -> ArticleMetadata:
        """Cross-reference metadata across different sources."""
        # Cross-reference title with abstract
        if metadata.title and metadata.abstract:
            if self.sentence_model:
                title_embedding = self.sentence_model.encode([metadata.title])
                abstract_embedding = self.sentence_model.encode([metadata.abstract])
                similarity = self._cosine_similarity(title_embedding[0], abstract_embedding[0])
                
                if similarity > 0.3:  # Title and abstract should be somewhat related
                    metadata.confidence_scores['cross_reference'] = similarity
        
        # Cross-reference authors with affiliations
        if metadata.authors and metadata.affiliations:
            # Check if author names appear near affiliation information
            author_affiliation_matches = 0
            for doc in documents:
                text = doc.page_content
                for author in metadata.authors:
                    for affiliation in metadata.affiliations:
                        # Check if author and affiliation appear close together
                        author_pos = text.lower().find(author.lower())
                        affiliation_pos = text.lower().find(affiliation.lower())
                        
                        if (author_pos != -1 and affiliation_pos != -1 and
                            abs(author_pos - affiliation_pos) < 500):  # Within 500 characters
                            author_affiliation_matches += 1
            
            if author_affiliation_matches > 0:
                metadata.confidence_scores['author_affiliation_match'] = min(
                    author_affiliation_matches / len(metadata.authors), 1.0
                )
        
        return metadata


# Factory function for easy instantiation
def create_semantic_chunking_service(
    strategy: str = "hybrid",
    chunk_size: int = 800,
    chunk_overlap: int = 160,
    **kwargs
) -> SemanticChunkingService:
    """
    Factory function to create a semantic chunking service with common configurations.
    
    Args:
        strategy: Chunking strategy ('recursive', 'semantic', 'structural', 'hybrid')
        chunk_size: Target chunk size in characters
        chunk_overlap: Overlap between chunks in characters
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured SemanticChunkingService instance
    """
    config = ChunkingConfig(
        strategy=ChunkingStrategy(strategy),
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        **kwargs
    )
    
    return SemanticChunkingService(config) 
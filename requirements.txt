# Core Web Framework
Flask==2.3.3
Flask-Login==0.6.3
Flask-WTF==1.2.2
Flask-Limiter==3.12
Werkzeug==2.3.7
Jinja2==3.1.3
WTForms==3.1.1
itsdangerous==2.1.2
MarkupSafe==2.1.5
markdown==3.4.4

# Security & Authentication
bcrypt==4.1.2
cryptography==41.0.7
pyOpenSSL==23.2.0

# System Monitoring & Health
psutil==5.9.8

# HTTP & API
requests==2.31.0
httpx==0.27.0
aiohttp==3.9.3
urllib3==2.2.1
certifi==2024.2.2

# Database & Storage
chromadb==0.4.24
chroma-hnswlib==0.7.3

# AI & Machine Learning
langchain
langchain-core
langchain-community
langchain-ollama
langchain-chroma
langchain-text-splitters
ollama==0.4.8

# LlamaIndex Integration
llama-index>=0.10.0
llama-index-llms-langchain>=0.1.0
llama-index-embeddings-langchain>=0.1.0
llama-index-llms-ollama>=0.1.0
llama-index-embeddings-ollama>=0.1.0
llama-index-readers-file>=0.1.0
llama-index-readers-web>=0.1.0

# PDF Processing
PyMuPDF==1.23.22
pdf2image==1.16.3
pdfplumber==0.10.3
pdfminer.six==20221105
pypdf==3.17.4
pypdfium2==4.22.0
camelot-py==0.11.0  # 0.11.0 is the last version supporting Python 3.10
# tabula-py==2.7.0  # Optional, for table extraction

# Image Processing
Pillow==9.5.0
opencv-python==4.7.0.72
opencv-python-headless==4.7.0.72
numpy==1.24.4

# Natural Language Processing
spacy==3.8.7
scispacy==0.5.4
spacy-legacy==3.0.12
spacy-loggers==1.0.4
srsly==2.4.6
thinc==8.1.10
wasabi==1.1.2
weasel==0.3.4
catalogue==2.0.10
confection==0.0.4
preshed==3.0.8
murmurhash==1.0.9
cymem==2.0.7
blis==0.7.9

# Semantic Chunking Dependencies
sentence-transformers==2.2.2
transformers==4.35.0
torch==2.1.0
numpy==1.24.4
scikit-learn==1.3.0
# spaCy English model - installed separately
# en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.5.0/en_core_web_sm-3.5.0-py3-none-any.whl
# To use the CRAFT model for scientific NER, install with:
# pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.1/en_ner_craft_md-0.5.1.tar.gz

# Web Scraping & Parsing
beautifulsoup4==4.12.3
lxml==4.9.3
soupsieve==2.4.1
cssselect==1.2.0
fake-useragent==1.5.1
fake-http-header==0.3.3
Crawl4AI==0.5.0

# Geocoding & Location
geocoder==1.38.1
geopy==2.3.0
geoip2==4.7.0
maxminddb==2.3.0
geographiclib==2.0

# Configuration & Environment
python-dotenv==1.0.1
pydantic
pydantic-settings==2.0.3
pydantic_core==2.16.3
PyYAML==6.0.1

# File & System Utilities
python-multipart==0.0.6
filetype==1.2.0

# Data Processing
tqdm==4.66.2
orjson==3.9.15
python-dateutil==2.8.2
pytz==2023.3

# Utilities
click==8.1.7
colorama==0.4.6
humanize==4.6.0
rich==13.3.5
tenacity==8.2.3

# Development & Testing (Optional - can be moved to requirements-dev.txt)
# pytest==7.4.0
# pytest-cov==4.1.0
# black==23.3.0
# flake8==6.0.0
# mypy==1.4.1
# ruff==0.0.284

# Production Server (Optional - can be moved to requirements-prod.txt)
# gunicorn==20.1.0
# uvicorn==0.22.0

# Monitoring & Logging (Optional)
# psutil==5.9.8

# Note: The following packages were removed as they appear unused in the codebase:
# - torch, transformers, sentence-transformers (heavy ML packages)
# - pandas, matplotlib, scipy, scikit-learn (data science packages)
# - fastapi, starlette (FastAPI framework - not used)
# - gradio, gradio_client (Gradio UI - not used)
# - kubernetes (K8s client - not used)
# - openai, google-generativeai (external AI APIs - not used)
# - playwright (browser automation - not used)
# - and many other unused dependencies

# Background job processing
rq>=1.15.1
redis>=4.0.0

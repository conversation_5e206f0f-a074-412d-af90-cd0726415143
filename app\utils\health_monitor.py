"""
System Health Monitoring for ERDB Document Management System

This module provides comprehensive system health monitoring including:
- Database performance metrics
- Disk usage monitoring
- Memory usage tracking
- Application performance metrics
- System resource alerts
"""

import os
import psutil
import sqlite3
import logging
import datetime
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

# Import database connection with fallback
try:
    from app.utils.db_connection import get_connection
    HAS_DB_CONNECTION = True
except ImportError:
    HAS_DB_CONNECTION = False
    def get_connection(*args, **kwargs):
        return None

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    active_connections: int
    database_size_mb: float
    uptime_seconds: int

@dataclass
class DatabaseMetrics:
    """Database performance metrics."""
    database_name: str
    size_mb: float
    page_count: int
    cache_hit_ratio: float
    active_connections: int
    last_vacuum: Optional[str]
    integrity_check: bool

@dataclass
class HealthStatus:
    """Overall system health status."""
    status: str  # 'healthy', 'warning', 'critical'
    score: int  # 0-100
    issues: List[str]
    recommendations: List[str]
    last_check: str

class HealthMonitor:
    """System health monitoring and alerting."""
    
    def __init__(self):
        self.thresholds = {
            'cpu_warning': 70.0,
            'cpu_critical': 90.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'db_size_warning': 1000.0,  # 1GB
            'db_size_critical': 5000.0,  # 5GB
            'cache_hit_ratio_warning': 0.8,
            'cache_hit_ratio_critical': 0.6
        }
        
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1000
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Database metrics
            db_size = self._get_database_size()
            active_connections = self._get_active_connections()
            
            # Uptime
            uptime = psutil.boot_time()
            uptime_seconds = int(datetime.datetime.now().timestamp() - uptime)
            
            metrics = SystemMetrics(
                timestamp=datetime.datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available_mb=memory.available / (1024 * 1024),
                disk_usage_percent=disk.percent,
                disk_free_gb=disk.free / (1024 * 1024 * 1024),
                active_connections=active_connections,
                database_size_mb=db_size,
                uptime_seconds=uptime_seconds
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)
            
            return metrics
        
        except Exception as e:
            logger.error(f"Error getting system metrics: {str(e)}")
            raise
    
    def get_database_metrics(self) -> List[DatabaseMetrics]:
        """Get database performance metrics."""
        databases = [
            "./erdb_main.db"  # Unified database
        ]
        
        metrics = []
        for db_path in databases:
            if os.path.exists(db_path):
                try:
                    with get_connection(db_path) as conn:
                        cursor = conn.cursor()
                        
                        # Get database size
                        cursor.execute("PRAGMA page_count")
                        page_count = cursor.fetchone()[0]
                        cursor.execute("PRAGMA page_size")
                        page_size = cursor.fetchone()[0]
                        size_mb = (page_count * page_size) / (1024 * 1024)
                        
                        # Get cache hit ratio
                        cursor.execute("PRAGMA cache_hit")
                        cache_hit = cursor.fetchone()[0]
                        cursor.execute("PRAGMA cache_miss")
                        cache_miss = cursor.fetchone()[0]
                        
                        total_requests = cache_hit + cache_miss
                        cache_hit_ratio = cache_hit / total_requests if total_requests > 0 else 1.0
                        
                        # Check integrity
                        cursor.execute("PRAGMA integrity_check")
                        integrity_result = cursor.fetchone()[0]
                        integrity_ok = integrity_result == 'ok'
                        
                        # Get last vacuum info (if available)
                        last_vacuum = None
                        try:
                            cursor.execute("SELECT last_vacuum FROM sqlite_stat1 LIMIT 1")
                            result = cursor.fetchone()
                            if result:
                                last_vacuum = result[0]
                        except:
                            pass
                        
                        metrics.append(DatabaseMetrics(
                            database_name=os.path.basename(db_path),
                            size_mb=size_mb,
                            page_count=page_count,
                            cache_hit_ratio=cache_hit_ratio,
                            active_connections=1,  # Simplified for now
                            last_vacuum=last_vacuum,
                            integrity_check=integrity_ok
                        ))
                
                except Exception as e:
                    logger.error(f"Error getting metrics for {db_path}: {str(e)}")
        
        return metrics
    
    def check_health_status(self) -> HealthStatus:
        """Check overall system health and return status."""
        try:
            system_metrics = self.get_system_metrics()
            db_metrics = self.get_database_metrics()
            
            issues = []
            recommendations = []
            score = 100
            
            # Check CPU usage
            if system_metrics.cpu_percent > self.thresholds['cpu_critical']:
                issues.append(f"CPU usage critical: {system_metrics.cpu_percent:.1f}%")
                recommendations.append("Consider scaling up CPU resources or optimizing queries")
                score -= 30
            elif system_metrics.cpu_percent > self.thresholds['cpu_warning']:
                issues.append(f"CPU usage high: {system_metrics.cpu_percent:.1f}%")
                recommendations.append("Monitor CPU usage and consider optimization")
                score -= 10
            
            # Check memory usage
            if system_metrics.memory_percent > self.thresholds['memory_critical']:
                issues.append(f"Memory usage critical: {system_metrics.memory_percent:.1f}%")
                recommendations.append("Increase system memory or optimize memory usage")
                score -= 30
            elif system_metrics.memory_percent > self.thresholds['memory_warning']:
                issues.append(f"Memory usage high: {system_metrics.memory_percent:.1f}%")
                recommendations.append("Monitor memory usage and consider cleanup")
                score -= 10
            
            # Check disk usage
            if system_metrics.disk_usage_percent > self.thresholds['disk_critical']:
                issues.append(f"Disk usage critical: {system_metrics.disk_usage_percent:.1f}%")
                recommendations.append("Free up disk space immediately")
                score -= 30
            elif system_metrics.disk_usage_percent > self.thresholds['disk_warning']:
                issues.append(f"Disk usage high: {system_metrics.disk_usage_percent:.1f}%")
                recommendations.append("Consider cleanup of old files and backups")
                score -= 10
            
            # Check database metrics
            for db_metric in db_metrics:
                if db_metric.size_mb > self.thresholds['db_size_critical']:
                    issues.append(f"Database {db_metric.database_name} size critical: {db_metric.size_mb:.1f}MB")
                    recommendations.append(f"Run VACUUM on {db_metric.database_name} and consider archiving old data")
                    score -= 20
                elif db_metric.size_mb > self.thresholds['db_size_warning']:
                    issues.append(f"Database {db_metric.database_name} size large: {db_metric.size_mb:.1f}MB")
                    recommendations.append(f"Consider maintenance on {db_metric.database_name}")
                    score -= 5
                
                if db_metric.cache_hit_ratio < self.thresholds['cache_hit_ratio_critical']:
                    issues.append(f"Database {db_metric.database_name} cache hit ratio critical: {db_metric.cache_hit_ratio:.2f}")
                    recommendations.append(f"Optimize queries and indexes for {db_metric.database_name}")
                    score -= 15
                elif db_metric.cache_hit_ratio < self.thresholds['cache_hit_ratio_warning']:
                    issues.append(f"Database {db_metric.database_name} cache hit ratio low: {db_metric.cache_hit_ratio:.2f}")
                    recommendations.append(f"Consider query optimization for {db_metric.database_name}")
                    score -= 5
                
                if not db_metric.integrity_check:
                    issues.append(f"Database {db_metric.database_name} integrity check failed")
                    recommendations.append(f"Run integrity check and repair {db_metric.database_name}")
                    score -= 25
            
            # Determine status
            if score >= 80:
                status = 'healthy'
            elif score >= 60:
                status = 'warning'
            else:
                status = 'critical'
            
            return HealthStatus(
                status=status,
                score=score,
                issues=issues,
                recommendations=recommendations,
                last_check=system_metrics.timestamp
            )
        
        except Exception as e:
            logger.error(f"Error checking health status: {str(e)}")
            return HealthStatus(
                status='unknown',
                score=0,
                issues=[f"Health check failed: {str(e)}"],
                recommendations=["Check system logs and restart monitoring"],
                last_check=datetime.datetime.now().isoformat()
            )
    
    def _get_database_size(self) -> float:
        """Get total database size in MB."""
        total_size = 0
        databases = [
            "./erdb_main.db"  # Unified database
        ]
        
        for db_path in databases:
            if os.path.exists(db_path):
                total_size += os.path.getsize(db_path)
        
        return total_size / (1024 * 1024)
    
    def _get_active_connections(self) -> int:
        """Get number of active database connections."""
        # This is a simplified implementation
        # In a real system, you'd track actual connection pool usage
        return len(self.metrics_history) if self.metrics_history else 0
    
    def get_metrics_history(self, hours: int = 24) -> List[SystemMetrics]:
        """Get metrics history for the specified number of hours."""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        cutoff_timestamp = cutoff_time.isoformat()
        
        return [
            metric for metric in self.metrics_history
            if metric.timestamp >= cutoff_timestamp
        ]
    
    def export_metrics(self, file_path: str) -> bool:
        """Export metrics to JSON file."""
        try:
            data = {
                'system_metrics': [asdict(m) for m in self.metrics_history],
                'database_metrics': [asdict(m) for m in self.get_database_metrics()],
                'health_status': asdict(self.check_health_status()),
                'export_timestamp': datetime.datetime.now().isoformat()
            }
            
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Metrics exported to {file_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return False

# Global health monitor instance
_health_monitor: Optional[HealthMonitor] = None

def get_health_monitor() -> HealthMonitor:
    """Get the global health monitor instance."""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor()
    return _health_monitor

def check_system_health() -> HealthStatus:
    """Quick health check function."""
    monitor = get_health_monitor()
    return monitor.check_health_status()

def get_system_metrics() -> SystemMetrics:
    """Get current system metrics."""
    monitor = get_health_monitor()
    return monitor.get_system_metrics() 
{"test_timestamp": "2025-07-29T11:11:55.451355", "metadata_extraction": {"timestamp": "2025-07-29T11:11:54.785031", "test_documents": {"scientific_paper": {"title": "# EFFECTS OF CLIMATE CHANGE ON FOREST ECOSYSTEMS", "authors": ["Dr. <PERSON>", "Prof. <PERSON>"], "affiliations": ["Stanford University"], "abstract": "**Abstract:** This study investigates the long-term effects of climate change on forest ecosystems in North America. We analyzed data from 50 forest plots over a 20-year period to understand how temperature and precipitation changes affect biodiversity and ecosystem health.\n\n**Keywords:** climate change, forest ecosystems, biodiversity, temperature, precipitation\n", "keywords": ["journal", "** climate change", "forest ecosystems", "profound", "changes", "environmental", "temperature", "precipitation", "data", "science", "johnson", "biodiversity", "today", "results"], "publication_info": {"journal": "Environmental Science", "year": "2020"}, "confidence_scores": {"title": 0.30000000000000004, "authors": 0.4, "abstract": 0.9000000000000001, "author_affiliation_match": 1.0}, "extraction_method": "semantic"}, "conference_paper": {"title": "Conference: International Conference on Machine Learning 2023", "authors": ["<PERSON>", "<PERSON>", "Harvard University"], "affiliations": ["PhD\nDepartment", "Harvard University\nDepartment", "Yale University"], "abstract": "Abstract: We present a novel machine learning approach for biodiversity conservation...\n\nKeywords: machine learning, biodiversity, conservation, AI\n", "keywords": ["ecological", "garcia", "james", "AI", "results", "data", "science", "biodiversity", "location", "ecology", "machine", "computer", "conservation", "machine learning"], "publication_info": {"journal": "ICML", "year": "2022"}, "confidence_scores": {"title": 0.5, "authors": 0.4, "abstract": 0.9000000000000001, "author_affiliation_match": 1.0}, "extraction_method": "semantic"}, "technical_report": {"title": "Contact: <EMAIL>", "authors": ["Dr. <PERSON>", "Advanced Research", "Senior Research Scientist", "<PERSON>"], "affiliations": ["Natural Language Processing Lab", "Advanced Research Institute\nDepartment"], "abstract": null, "keywords": ["performance", "retrieval", "senior", "lisa", "results", "report", "jennifer", "code", "hybrid", "character"], "publication_info": {"year": "2023"}, "confidence_scores": {"title": 0.5, "authors": 0.7, "author_affiliation_match": 1.0}, "extraction_method": "semantic"}, "journal_article": {"title": "References:", "authors": ["<PERSON>", "Prof. <PERSON>", "<PERSON>", "Dr. <PERSON>", "Dr. <PERSON>"], "affiliations": ["Rachel Green\n\nDepartment", "New York\nSchool"], "abstract": "Abstract:\nUrban wildlife populations face unique challenges from climate change...\n\nKeywords: urban wildlife, climate adaptation, biodiversity, city ecosystems\n", "keywords": ["journal", "british", "adaptation", "corresponding", "materials", "climate adaptation", "environmental", "access", "results", "populations", "unique", "biodiversity", "urban wildlife", "city ecosystems"], "publication_info": {"journal": "Applied Ecology\nVolume", "year": "2023", "doi": "10.1111/jae.2023.001", "volume": "60", "issue": "4"}, "confidence_scores": {"title": 0.5, "authors": 0.7, "abstract": 0.6000000000000001, "author_affiliation_match": 1.0}, "extraction_method": "semantic"}}, "summary": {"total_documents": 4, "successful_extractions": 4, "average_confidence": 0.6958333333333333}}, "chunking_integration": {"scientific_paper": {"error": "1 validation error for Document\npage_content\n  Input should be a valid string [type=string_type, input_value=Document(metadata={'Heade...Volume:** 15, Issue: 3'), input_type=Document]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type"}, "conference_paper": {"error": "string index out of range"}, "technical_report": {"error": "string index out of range"}, "journal_article": {"error": "string index out of range"}}, "extraction_comparison": {"scientific_paper": {"enhanced": {"title": "# EFFECTS OF CLIMATE CHANGE ON FOREST ECOSYSTEMS", "authors": ["Dr. <PERSON>", "Prof. <PERSON>"], "confidence": 0.30000000000000004}, "basic": {"title": "# EFFECTS OF CLIMATE CHANGE ON FOREST ECOSYSTEMS", "authors": ["** Dr. <PERSON>", "Prof. <PERSON>", "Dr. <PERSON>"], "confidence": 0.5}, "improvement": {"title_accuracy": false, "author_count": -1, "confidence_improvement": -0.19999999999999996}}, "conference_paper": {"enhanced": {"title": "Conference: International Conference on Machine Learning 2023", "authors": ["<PERSON>", "<PERSON>", "Harvard University"], "confidence": 0.5}, "basic": {"title": "MACHINE LEARNING APPLICATIONS IN BIODIVERSITY CONSERVATION", "authors": ["<PERSON>", "<PERSON>", "PhD"], "confidence": 0.5}, "improvement": {"title_accuracy": true, "author_count": 0, "confidence_improvement": 0.0}}, "technical_report": {"enhanced": {"title": "Contact: <EMAIL>", "authors": ["Dr. <PERSON>", "Advanced Research", "Senior Research Scientist", "<PERSON>"], "confidence": 0.5}, "basic": {"title": "TECHNICAL REPORT: IMPLEMENTATION OF SEMANTIC CHUNKING FOR DOCUMENT PROCESSING", "authors": ["Dr. <PERSON>", "Senior Research Scientist"], "confidence": 0.5}, "improvement": {"title_accuracy": true, "author_count": 2, "confidence_improvement": 0.0}}, "journal_article": {"enhanced": {"title": "References:", "authors": ["<PERSON>", "Prof. <PERSON>", "<PERSON>", "Dr. <PERSON>", "Dr. <PERSON>"], "confidence": 0.5}, "basic": {"title": null, "authors": ["Dr. <PERSON>", "Dr. <PERSON>", "Prof. <PERSON>", "<EMAIL>"], "confidence": 0.0}, "improvement": {"title_accuracy": true, "author_count": 1, "confidence_improvement": 0.5}}}, "summary": {"total_documents_tested": 4, "enhanced_extraction_success_rate": 1.0, "average_metadata_confidence": 0.6958333333333333, "average_chunk_metadata_coverage": 0.0}}
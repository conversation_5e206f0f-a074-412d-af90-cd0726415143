{% extends "admin_base_adminlte.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
    {% if current_user.is_authenticated %}
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Welcome to Document Management System
                    </h3>
                </div>
                <div class="card-body">
                    <p class="mb-0">
                        <strong>Hello, {{ current_user.username }}!</strong> 
                        Manage your documents, configure AI models, and analyze system performance from this dashboard.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Row -->
    <div class="row">
        <!-- Upload Content Card -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><i class="fas fa-upload"></i></h3>
                    <p>Upload Content</p>
                </div>
                <div class="icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <a href="{{ url_for('upload_file') }}" class="small-box-footer">
                    Upload PDFs & URLs <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Files Management Card -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><i class="fas fa-file-alt"></i></h3>
                    <p>Manage Files</p>
                </div>
                <div class="icon">
                    <i class="fas fa-folder-open"></i>
                </div>
                <a href="{{ url_for('list_files') }}" class="small-box-footer">
                    View & Delete Files <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Model Settings Card -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3><i class="fas fa-cogs"></i></h3>
                    <p>AI Configuration</p>
                </div>
                <div class="icon">
                    <i class="fas fa-robot"></i>
                </div>
                <a href="{{ url_for('unified_config') }}" class="small-box-footer">
                    Configure Models <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <!-- Analytics Card -->
        <div class="col-lg-3 col-md-6 col-sm-6 col-12">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3><i class="fas fa-chart-bar"></i></h3>
                    <p>Analytics</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <a href="{{ url_for('admin.analytics_dashboard') }}" class="small-box-footer">
                    View Statistics <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Feature Categories -->
    <div class="row">
        <!-- Content Management -->
        <div class="col-lg-4 col-md-6">
            <div class="card card-outline card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-folder mr-2"></i>
                        Content Management
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if current_user.has_dashboard_permission('upload_content') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('upload_file') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-upload mr-2"></i>Upload Content
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('manage_files') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('list_files') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-file-alt mr-2"></i>Manage Files
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('manage_forms') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('list_forms') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-wpforms mr-2"></i>Forms
                            </a>
                        </div>
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('list_submissions') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-inbox mr-2"></i>Submissions
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat & Analytics -->
        <div class="col-lg-4 col-md-6">
            <div class="card card-outline card-success">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments mr-2"></i>
                        Chat & Analytics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if current_user.has_dashboard_permission('chat_history') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('chat_history') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-history mr-2"></i>Chat History
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('chat_sessions') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('view_sessions') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-comment-dots mr-2"></i>Chat Sessions
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('ai_analytics') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('admin.analytics_dashboard') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-chart-bar mr-2"></i>AI Analytics
                            </a>
                        </div>
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('admin.location_map') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-map-marked-alt mr-2"></i>Location Map
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- System & User Management -->
        <div class="col-lg-4 col-md-12">
            <div class="card card-outline card-warning">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs mr-2"></i>
                        System & Users
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if current_user.has_dashboard_permission('model_settings') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('unified_config') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-robot mr-2"></i>Model Settings
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('greeting_management') %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('greeting_management') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-comment-alt mr-2"></i>Greetings
                            </a>
                        </div>
                        {% endif %}
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('user.admin_users') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-users mr-2"></i>Manage Users
                            </a>
                        </div>
                        <div class="col-12 mb-2">
                            <a href="{{ url_for('user.admin_permission_groups') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-user-shield mr-2"></i>Permissions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Tools -->
    {% if current_user.has_dashboard_permission('clean_urls') or current_user.has_dashboard_permission('html_generator') %}
    <div class="row">
        <div class="col-12">
            <div class="card card-outline card-secondary">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-2"></i>
                        System Tools
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if current_user.has_dashboard_permission('clean_urls') %}
                        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-2">
                            <a href="{{ url_for('clean_urls') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-broom mr-2"></i>Clean URLs
                            </a>
                        </div>
                        {% endif %}
                        {% if current_user.has_dashboard_permission('html_generator') %}
                        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-2">
                            <a href="{{ url_for('html_generator_page') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-code mr-2"></i>HTML Generator
                            </a>
                        </div>
                        {% endif %}
                        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-2">
                            <a href="{{ url_for('user.admin_activity_logs') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-clipboard-list mr-2"></i>Activity Logs
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-2">
                            <a href="{{ url_for('user.admin_permission_audit') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-search mr-2"></i>Permission Audit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- Non-authenticated User View -->
    <div class="row">
        <div class="col-12">
            <div class="card card-outline card-danger">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Access Denied
                    </h3>
                </div>
                <div class="card-body">
                    <p>You need to be logged in to access the admin dashboard.</p>
                    <a href="{{ url_for('user.login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt mr-2"></i>Login
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize AdminLTE features
    $('.info-box').hover(
        function() {
            $(this).addClass('elevation-2');
        },
        function() {
            $(this).removeClass('elevation-2');
        }
    );
    
    $('.small-box').hover(
        function() {
            $(this).addClass('elevation-2');
        },
        function() {
            $(this).removeClass('elevation-2');
        }
    );
});
</script>
{% endblock %} 
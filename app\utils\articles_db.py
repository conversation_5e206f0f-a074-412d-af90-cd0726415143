"""
Database utilities for managing extracted articles from PDFs.
Provides functions for storing, retrieving, and managing article metadata.
"""

import sqlite3
import logging
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from app.utils.content_db import get_db_connection

logger = logging.getLogger(__name__)

def store_extracted_article(pdf_document_id: int, page_number: int, title: str, authors: str,
                          title_confidence: float = 0.0, authors_confidence: float = 0.0,
                          overall_confidence: float = 0.0, extraction_method: str = 'standard',
                          font_size_title: Optional[float] = None, font_size_authors: Optional[float] = None,
                          position_data: Optional[Dict] = None, title_original_case: Optional[str] = None,
                          authors_original_case: Optional[str] = None) -> Optional[int]:
    """
    Store an extracted article in the database.
    
    Args:
        pdf_document_id: ID of the PDF document
        page_number: Page number where article was found
        title: Article title (should be in ALL CAPS)
        authors: Article authors (should be in ALL CAPS)
        title_confidence: Confidence score for title extraction (0.0-1.0)
        authors_confidence: Confidence score for authors extraction (0.0-1.0)
        overall_confidence: Overall confidence score (0.0-1.0)
        extraction_method: Method used for extraction
        font_size_title: Font size of the title
        font_size_authors: Font size of the authors
        position_data: Dictionary with position information (bbox, coordinates)
        title_original_case: Original case of title before ALL CAPS conversion
        authors_original_case: Original case of authors before ALL CAPS conversion
    
    Returns:
        ID of the inserted article record, or None on error
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Convert position_data to JSON string if provided
        position_json = json.dumps(position_data) if position_data else None
        
        cursor.execute('''
            INSERT INTO extracted_articles 
            (pdf_document_id, page_number, title, authors, title_confidence, authors_confidence,
             overall_confidence, extraction_method, font_size_title, font_size_authors,
             position_data, title_original_case, authors_original_case, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ''', (pdf_document_id, page_number, title, authors, title_confidence, authors_confidence,
              overall_confidence, extraction_method, font_size_title, font_size_authors,
              position_json, title_original_case, authors_original_case))
        
        article_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"Stored article with ID {article_id}: {title[:50]}...")
        return article_id
        
    except sqlite3.Error as e:
        logger.error(f"Error storing extracted article: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_articles_by_pdf(pdf_document_id: int) -> List[Dict[str, Any]]:
    """
    Get all articles for a specific PDF document.
    
    Args:
        pdf_document_id: ID of the PDF document
    
    Returns:
        List of article dictionaries
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ea.*, pd.filename, pd.original_filename, pd.category
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            WHERE ea.pdf_document_id = ?
            ORDER BY ea.page_number, ea.id
        ''', (pdf_document_id,))
        
        rows = cursor.fetchall()
        articles = []
        
        for row in rows:
            article = dict(row)
            # Parse position_data JSON if present
            if article.get('position_data'):
                try:
                    article['position_data'] = json.loads(article['position_data'])
                except json.JSONDecodeError:
                    article['position_data'] = None
            articles.append(article)
        
        return articles
        
    except sqlite3.Error as e:
        logger.error(f"Error retrieving articles for PDF {pdf_document_id}: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def search_articles(search_query: Optional[str] = None, category: Optional[str] = None,
                   min_confidence: float = 0.0, page: int = 1, limit: int = 20,
                   sort_by: str = 'created_at', sort_order: str = 'desc') -> Dict[str, Any]:
    """
    Search and filter articles with pagination.
    
    Args:
        search_query: Search term for titles and authors
        category: Filter by PDF category
        min_confidence: Minimum confidence threshold
        page: Page number for pagination (1-based)
        limit: Number of results per page
        sort_by: Field to sort by (title, authors, overall_confidence, created_at)
        sort_order: Sort order (asc, desc)
    
    Returns:
        Dictionary with articles, pagination info, and totals
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Build WHERE clause
        where_conditions = ["ea.overall_confidence >= ?"]
        params = [min_confidence]
        
        if search_query:
            where_conditions.append("(ea.title LIKE ? OR ea.authors LIKE ?)")
            search_term = f"%{search_query}%"
            params.extend([search_term, search_term])
        
        if category:
            where_conditions.append("pd.category = ?")
            params.append(category)
        
        where_clause = " AND ".join(where_conditions)
        
        # Validate sort parameters
        valid_sort_fields = ['title', 'authors', 'overall_confidence', 'created_at', 'page_number']
        if sort_by not in valid_sort_fields:
            sort_by = 'created_at'
        
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'desc'
        
        # Get total count
        count_query = f'''
            SELECT COUNT(*)
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            WHERE {where_clause}
        '''
        
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # Get paginated results
        offset = (page - 1) * limit
        
        results_query = f'''
            SELECT ea.*, pd.filename, pd.original_filename, pd.category
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            WHERE {where_clause}
            ORDER BY ea.{sort_by} {sort_order.upper()}
            LIMIT ? OFFSET ?
        '''
        
        cursor.execute(results_query, params + [limit, offset])
        rows = cursor.fetchall()
        
        articles = []
        for row in rows:
            article = dict(row)
            # Parse position_data JSON if present
            if article.get('position_data'):
                try:
                    article['position_data'] = json.loads(article['position_data'])
                except json.JSONDecodeError:
                    article['position_data'] = None
            articles.append(article)
        
        return {
            'articles': articles,
            'total': total,
            'page': page,
            'limit': limit,
            'has_next': (page * limit) < total,
            'has_prev': page > 1,
            'total_pages': (total + limit - 1) // limit
        }
        
    except sqlite3.Error as e:
        logger.error(f"Error searching articles: {str(e)}")
        return {
            'articles': [],
            'total': 0,
            'page': page,
            'limit': limit,
            'has_next': False,
            'has_prev': False,
            'total_pages': 0
        }
    finally:
        if conn:
            conn.close()

def delete_articles_by_pdf(pdf_document_id: int) -> bool:
    """
    Delete all articles for a specific PDF document.
    
    Args:
        pdf_document_id: ID of the PDF document
    
    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM extracted_articles WHERE pdf_document_id = ?', (pdf_document_id,))
        deleted_count = cursor.rowcount
        conn.commit()
        
        logger.info(f"Deleted {deleted_count} articles for PDF document {pdf_document_id}")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Error deleting articles for PDF {pdf_document_id}: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

def get_article_statistics() -> Dict[str, Any]:
    """
    Get statistics about extracted articles.
    
    Returns:
        Dictionary with article statistics
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Total articles
        cursor.execute('SELECT COUNT(*) FROM extracted_articles')
        total_articles = cursor.fetchone()[0]
        
        # Articles by category
        cursor.execute('''
            SELECT pd.category, COUNT(*) as count
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            GROUP BY pd.category
            ORDER BY count DESC
        ''')
        by_category = [dict(row) for row in cursor.fetchall()]
        
        # Average confidence scores
        cursor.execute('''
            SELECT 
                AVG(overall_confidence) as avg_overall,
                AVG(title_confidence) as avg_title,
                AVG(authors_confidence) as avg_authors
            FROM extracted_articles
        ''')
        confidence_stats = dict(cursor.fetchone())
        
        # Articles by extraction method
        cursor.execute('''
            SELECT extraction_method, COUNT(*) as count
            FROM extracted_articles
            GROUP BY extraction_method
            ORDER BY count DESC
        ''')
        by_method = [dict(row) for row in cursor.fetchall()]
        
        return {
            'total_articles': total_articles,
            'by_category': by_category,
            'confidence_stats': confidence_stats,
            'by_extraction_method': by_method
        }
        
    except sqlite3.Error as e:
        logger.error(f"Error getting article statistics: {str(e)}")
        return {}
    finally:
        if conn:
            conn.close()

def store_articles_from_extraction(pdf_document_id: int, articles_data: List[Dict[str, Any]]) -> List[int]:
    """
    Store multiple articles from enhanced extraction results.

    Args:
        pdf_document_id: ID of the PDF document
        articles_data: List of article dictionaries from extract_articles_with_confidence

    Returns:
        List of article IDs that were successfully stored
    """
    stored_ids = []

    try:
        for article in articles_data:
            # Prepare position data
            position_data = {
                "title_position": article.get("title_metadata", {}).get("position"),
                "author_position": article.get("author_metadata", {}).get("position")
            }

            # Store the article
            article_id = store_extracted_article(
                pdf_document_id=pdf_document_id,
                page_number=article["page"],
                title=article["title"],
                authors=article["authors"],
                title_confidence=article["title_confidence"],
                authors_confidence=article["authors_confidence"],
                overall_confidence=article["overall_confidence"],
                extraction_method=article["extraction_method"],
                font_size_title=article.get("title_metadata", {}).get("font_size"),
                font_size_authors=article.get("author_metadata", {}).get("font_size"),
                position_data=position_data,
                title_original_case=article["title_original_case"],
                authors_original_case=article["authors_original_case"]
            )

            if article_id:
                stored_ids.append(article_id)
                logger.info(f"Stored article {article_id}: {article['title'][:50]}...")
            else:
                logger.error(f"Failed to store article: {article['title'][:50]}...")

    except Exception as e:
        logger.error(f"Error storing articles from extraction: {str(e)}")

    return stored_ids

def update_article_confidence(article_id: int, title_confidence: float,
                            authors_confidence: float, overall_confidence: float) -> bool:
    """
    Update confidence scores for an existing article.

    Args:
        article_id: ID of the article to update
        title_confidence: New title confidence score
        authors_confidence: New authors confidence score
        overall_confidence: New overall confidence score

    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE extracted_articles
            SET title_confidence = ?, authors_confidence = ?, overall_confidence = ?,
                updated_at = datetime('now')
            WHERE id = ?
        ''', (title_confidence, authors_confidence, overall_confidence, article_id))

        success = cursor.rowcount > 0
        conn.commit()

        if success:
            logger.info(f"Updated confidence scores for article {article_id}")
        else:
            logger.warning(f"No article found with ID {article_id}")

        return success

    except sqlite3.Error as e:
        logger.error(f"Error updating article confidence: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

def get_low_confidence_articles(confidence_threshold: float = 0.7) -> List[Dict[str, Any]]:
    """
    Get articles with confidence scores below the threshold for review.

    Args:
        confidence_threshold: Threshold below which articles are considered low confidence

    Returns:
        List of low confidence articles
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ea.*, pd.filename, pd.original_filename, pd.category
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            WHERE ea.overall_confidence < ?
            ORDER BY ea.overall_confidence ASC, ea.created_at DESC
        ''', (confidence_threshold,))

        rows = cursor.fetchall()
        articles = []

        for row in rows:
            article = dict(row)
            # Parse position_data JSON if present
            if article.get('position_data'):
                try:
                    article['position_data'] = json.loads(article['position_data'])
                except json.JSONDecodeError:
                    article['position_data'] = None
            articles.append(article)

        return articles

    except sqlite3.Error as e:
        logger.error(f"Error retrieving low confidence articles: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def get_articles_by_extraction_method(extraction_method: str) -> List[Dict[str, Any]]:
    """
    Get articles by extraction method for analysis and comparison.

    Args:
        extraction_method: The extraction method to filter by

    Returns:
        List of articles using the specified extraction method
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ea.*, pd.filename, pd.original_filename, pd.category
            FROM extracted_articles ea
            JOIN pdf_documents pd ON ea.pdf_document_id = pd.id
            WHERE ea.extraction_method = ?
            ORDER BY ea.overall_confidence DESC, ea.created_at DESC
        ''', (extraction_method,))

        rows = cursor.fetchall()
        articles = []

        for row in rows:
            article = dict(row)
            # Parse position_data JSON if present
            if article.get('position_data'):
                try:
                    article['position_data'] = json.loads(article['position_data'])
                except json.JSONDecodeError:
                    article['position_data'] = None
            articles.append(article)

        return articles

    except sqlite3.Error as e:
        logger.error(f"Error retrieving articles by extraction method: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

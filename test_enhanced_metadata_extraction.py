#!/usr/bin/env python3
"""
Enhanced Metadata Extraction Test Script

This script demonstrates the enhanced article title and author extraction capabilities
using the hybrid semantic chunking system. It tests various document types and
shows the improvements over basic pattern matching.
"""

import os
import sys
import json
import logging
from typing import Dict, Any, List
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.semantic_chunking_service import (
    SemanticChunkingService, 
    ChunkingConfig, 
    ArticleMetadata,
    create_semantic_chunking_service
)
from langchain.schema import Document

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedMetadataExtractionTester:
    """Test class for enhanced metadata extraction capabilities."""
    
    def __init__(self):
        """Initialize the tester with semantic chunking service."""
        self.semantic_chunker = create_semantic_chunking_service(
            strategy="hybrid",
            chunk_size=800,
            chunk_overlap=160,
            extract_article_metadata=True,
            extract_publication_info=True,
            validate_metadata=True,
            confidence_threshold=0.7,
            enable_cross_reference=True
        )
        
        # Test documents with different formats
        self.test_documents = self._create_test_documents()
    
    def _create_test_documents(self) -> Dict[str, Document]:
        """Create test documents with various formats and structures."""
        documents = {}
        
        # Test Document 1: Scientific Paper Format
        scientific_paper = """
# EFFECTS OF CLIMATE CHANGE ON FOREST ECOSYSTEMS

**Authors: <AUTHORS>

**Affiliations:** 
- Department of Environmental Science, University of California
- Institute of Forest Research, Stanford University
- Center for Climate Studies, MIT

**Abstract:** This study investigates the long-term effects of climate change on forest ecosystems in North America. We analyzed data from 50 forest plots over a 20-year period to understand how temperature and precipitation changes affect biodiversity and ecosystem health.

**Keywords:** climate change, forest ecosystems, biodiversity, temperature, precipitation

**Introduction:** Climate change represents one of the most significant challenges facing forest ecosystems today...

**Methods:** We conducted field surveys across 50 forest plots in North America...

**Results:** Our analysis revealed significant changes in forest composition...

**Discussion:** The findings suggest that climate change is having profound effects...

**References:**
1. Smith, J. et al. (2020). Climate Change and Forests. Nature, 45(2), 123-145.
2. Brown, A. et al. (2019). Ecosystem Responses. Science, 78(4), 234-256.

**DOI:** 10.1234/forest.2023.001
**Published:** 2023
**Journal:** Journal of Environmental Science
**Volume:** 15, Issue: 3
"""
        documents['scientific_paper'] = Document(
            page_content=scientific_paper,
            metadata={'source': 'scientific_paper_test', 'type': 'research_article'}
        )
        
        # Test Document 2: Conference Paper Format
        conference_paper = """
MACHINE LEARNING APPLICATIONS IN BIODIVERSITY CONSERVATION

Authors: <AUTHORS>
Department of Computer Science, Harvard University
Department of Biology, Yale University

Abstract: We present a novel machine learning approach for biodiversity conservation...

Keywords: machine learning, biodiversity, conservation, AI

1. INTRODUCTION
Biodiversity conservation is critical for ecosystem health...

2. METHODOLOGY
Our approach combines deep learning with ecological data...

3. RESULTS
The model achieved 85% accuracy in species identification...

4. CONCLUSION
Machine learning shows promise for conservation efforts...

References:
[1] Garcia, M. et al. (2022). ML in Conservation. ICML Proceedings.
[2] Wilson, J. (2021). AI for Ecology. AAAI Conference.

Conference: International Conference on Machine Learning 2023
Date: July 15-21, 2023
Location: Honolulu, Hawaii
"""
        documents['conference_paper'] = Document(
            page_content=conference_paper,
            metadata={'source': 'conference_paper_test', 'type': 'conference_paper'}
        )
        
        # Test Document 3: Technical Report Format
        technical_report = """
TECHNICAL REPORT: IMPLEMENTATION OF SEMANTIC CHUNKING FOR DOCUMENT PROCESSING

Prepared by: Dr. Robert Kim, Senior Research Scientist
Technical Lead: Lisa Thompson, PhD
Contributors: David Park, Jennifer Lee

Organization: Advanced Research Institute
Department: Natural Language Processing Lab
Date: December 2023

Executive Summary:
This report presents the implementation and evaluation of semantic chunking...

1. Background
Document processing systems traditionally rely on character-based chunking...

2. Implementation
We developed a hybrid semantic chunking pipeline...

3. Results
The semantic chunking approach improved retrieval accuracy by 40%...

4. Recommendations
Based on our findings, we recommend...

Appendix A: Technical Specifications
Appendix B: Performance Metrics
Appendix C: Code Implementation

Contact: <EMAIL>
Project Code: SC-2023-001
"""
        documents['technical_report'] = Document(
            page_content=technical_report,
            metadata={'source': 'technical_report_test', 'type': 'technical_report'}
        )
        
        # Test Document 4: Journal Article Format
        journal_article = """
Journal of Applied Ecology
Volume 60, Issue 4, Pages 789-812
Published: August 2023

Urban Wildlife Adaptation to Climate Change: A Case Study

By: Dr. Amanda Foster, Dr. Carlos Mendez, Prof. Rachel Green

Department of Urban Ecology, University of Toronto
Wildlife Conservation Society, New York
School of Environmental Studies, University of British Columbia

Corresponding Author: <EMAIL>

Abstract:
Urban wildlife populations face unique challenges from climate change...

Keywords: urban wildlife, climate adaptation, biodiversity, city ecosystems

Introduction:
Urban environments present unique challenges for wildlife...

Materials and Methods:
We conducted field surveys in 25 urban parks...

Results:
Our analysis revealed that urban wildlife species...

Discussion:
The findings indicate that urban wildlife...

Acknowledgments:
We thank the city parks department for access...

References:
Foster, A. et al. (2023). Urban Wildlife Adaptation. Journal of Applied Ecology, 60(4), 789-812.
Mendez, C. (2022). Climate Change in Cities. Urban Ecology, 15(2), 45-67.

DOI: 10.1111/jae.2023.001
"""
        documents['journal_article'] = Document(
            page_content=journal_article,
            metadata={'source': 'journal_article_test', 'type': 'journal_article'}
        )
        
        return documents
    
    def test_metadata_extraction(self) -> Dict[str, Any]:
        """Test metadata extraction on all document types."""
        results = {
            'timestamp': datetime.now().isoformat(),
            'test_documents': {},
            'summary': {
                'total_documents': len(self.test_documents),
                'successful_extractions': 0,
                'average_confidence': 0.0
            }
        }
        
        logger.info(f"Testing enhanced metadata extraction on {len(self.test_documents)} document types")
        
        total_confidence = 0.0
        successful_extractions = 0
        
        for doc_name, document in self.test_documents.items():
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {doc_name.upper()}")
            logger.info(f"{'='*60}")
            
            try:
                # Extract metadata using enhanced semantic chunking
                metadata = self.semantic_chunker.extract_article_metadata([document])
                
                # Store results
                doc_results = {
                    'title': metadata.title,
                    'authors': metadata.authors,
                    'affiliations': metadata.affiliations,
                    'abstract': metadata.abstract,
                    'keywords': metadata.keywords,
                    'publication_info': metadata.publication_info,
                    'confidence_scores': metadata.confidence_scores,
                    'extraction_method': metadata.extraction_method
                }
                
                results['test_documents'][doc_name] = doc_results
                
                # Log results
                logger.info(f"Title: {metadata.title}")
                logger.info(f"Authors: <AUTHORS>
                logger.info(f"Affiliations: {metadata.affiliations}")
                logger.info(f"Abstract: {metadata.abstract[:100]}..." if metadata.abstract else "No abstract found")
                logger.info(f"Keywords: {metadata.keywords}")
                logger.info(f"Publication Info: {metadata.publication_info}")
                logger.info(f"Confidence Scores: {metadata.confidence_scores}")
                
                # Calculate average confidence
                if metadata.confidence_scores:
                    avg_confidence = sum(metadata.confidence_scores.values()) / len(metadata.confidence_scores)
                    total_confidence += avg_confidence
                    successful_extractions += 1
                    logger.info(f"Average Confidence: {avg_confidence:.3f}")
                
                logger.info(f"Extraction Method: {metadata.extraction_method}")
                
            except Exception as e:
                logger.error(f"Error extracting metadata from {doc_name}: {e}")
                results['test_documents'][doc_name] = {'error': str(e)}
        
        # Calculate summary statistics
        if successful_extractions > 0:
            results['summary']['successful_extractions'] = successful_extractions
            results['summary']['average_confidence'] = total_confidence / successful_extractions
        
        return results
    
    def test_chunking_with_metadata(self) -> Dict[str, Any]:
        """Test chunking with enhanced metadata integration."""
        logger.info(f"\n{'='*60}")
        logger.info("TESTING CHUNKING WITH ENHANCED METADATA")
        logger.info(f"{'='*60}")
        
        results = {}
        
        for doc_name, document in self.test_documents.items():
            logger.info(f"\nTesting chunking with metadata: {doc_name}")
            
            try:
                # Process document with semantic chunking
                chunks = self.semantic_chunker.chunk_documents([document])
                
                # Extract metadata
                metadata = self.semantic_chunker.extract_article_metadata([document])
                
                # Check metadata in chunks
                chunks_with_metadata = 0
                for i, chunk in enumerate(chunks):
                    if 'article_title' in chunk.metadata or 'article_authors' in chunk.metadata:
                        chunks_with_metadata += 1
                        logger.info(f"Chunk {i+1}: Has enhanced metadata")
                
                results[doc_name] = {
                    'total_chunks': len(chunks),
                    'chunks_with_metadata': chunks_with_metadata,
                    'metadata_coverage': chunks_with_metadata / len(chunks) if chunks else 0,
                    'extracted_title': metadata.title,
                    'extracted_authors': metadata.authors
                }
                
                logger.info(f"Total chunks: {len(chunks)}")
                logger.info(f"Chunks with metadata: {chunks_with_metadata}")
                logger.info(f"Metadata coverage: {chunks_with_metadata/len(chunks)*100:.1f}%" if chunks else "N/A")
                
            except Exception as e:
                logger.error(f"Error in chunking test for {doc_name}: {e}")
                results[doc_name] = {'error': str(e)}
        
        return results
    
    def compare_with_basic_extraction(self) -> Dict[str, Any]:
        """Compare enhanced extraction with basic pattern matching."""
        logger.info(f"\n{'='*60}")
        logger.info("COMPARING ENHANCED VS BASIC EXTRACTION")
        logger.info(f"{'='*60}")
        
        comparison_results = {}
        
        for doc_name, document in self.test_documents.items():
            logger.info(f"\nComparing extraction methods for: {doc_name}")
            
            # Enhanced extraction (already tested above)
            enhanced_metadata = self.semantic_chunker.extract_article_metadata([document])
            
            # Basic extraction (simple pattern matching)
            basic_metadata = self._basic_extraction(document.page_content)
            
            comparison = {
                'enhanced': {
                    'title': enhanced_metadata.title,
                    'authors': enhanced_metadata.authors,
                    'confidence': enhanced_metadata.confidence_scores.get('title', 0) if enhanced_metadata.title else 0
                },
                'basic': {
                    'title': basic_metadata['title'],
                    'authors': basic_metadata['authors'],
                    'confidence': basic_metadata['confidence']
                },
                'improvement': {
                    'title_accuracy': enhanced_metadata.title != basic_metadata['title'],
                    'author_count': len(enhanced_metadata.authors) - len(basic_metadata['authors']),
                    'confidence_improvement': (enhanced_metadata.confidence_scores.get('title', 0) if enhanced_metadata.title else 0) - basic_metadata['confidence']
                }
            }
            
            comparison_results[doc_name] = comparison
            
            logger.info(f"Enhanced Title: {enhanced_metadata.title}")
            logger.info(f"Basic Title: {basic_metadata['title']}")
            logger.info(f"Enhanced Authors: <AUTHORS>
            logger.info(f"Basic Authors: <AUTHORS>
            logger.info(f"Title Accuracy Improved: {comparison['improvement']['title_accuracy']}")
            logger.info(f"Author Count Difference: {comparison['improvement']['author_count']}")
            logger.info(f"Confidence Improvement: {comparison['improvement']['confidence_improvement']:.3f}")
        
        return comparison_results
    
    def _basic_extraction(self, text: str) -> Dict[str, Any]:
        """Basic pattern matching extraction for comparison."""
        import re
        
        # Basic title extraction
        title = None
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if (line.isupper() and len(line) > 10 and len(line) < 200 and
                not line.startswith('ABSTRACT') and not line.startswith('INTRODUCTION')):
                title = line
                break
        
        # Basic author extraction
        authors = []
        author_pattern = r'(?:Authors?|By):\s*(.+)'
        matches = re.findall(author_pattern, text, re.IGNORECASE)
        for match in matches:
            names = re.split(r'[,;&]|\band\b', match)
            authors.extend([name.strip() for name in names if name.strip()])
        
        return {
            'title': title,
            'authors': authors,
            'confidence': 0.5 if title else 0.0  # Basic confidence
        }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests and generate comprehensive report."""
        logger.info("Starting comprehensive enhanced metadata extraction test")
        logger.info("=" * 80)
        
        # Run all tests
        metadata_results = self.test_metadata_extraction()
        chunking_results = self.test_chunking_with_metadata()
        comparison_results = self.compare_with_basic_extraction()
        
        # Compile comprehensive results
        comprehensive_results = {
            'test_timestamp': datetime.now().isoformat(),
            'metadata_extraction': metadata_results,
            'chunking_integration': chunking_results,
            'extraction_comparison': comparison_results,
            'summary': {
                'total_documents_tested': len(self.test_documents),
                'enhanced_extraction_success_rate': len([r for r in metadata_results['test_documents'].values() if 'error' not in r]) / len(self.test_documents),
                'average_metadata_confidence': metadata_results['summary']['average_confidence'],
                'average_chunk_metadata_coverage': sum(r.get('metadata_coverage', 0) for r in chunking_results.values() if 'error' not in r) / len(chunking_results)
            }
        }
        
        # Save results to file
        output_file = f"enhanced_metadata_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(comprehensive_results, f, indent=2, default=str)
        
        logger.info(f"\n{'='*80}")
        logger.info("COMPREHENSIVE TEST RESULTS")
        logger.info(f"{'='*80}")
        logger.info(f"Total documents tested: {comprehensive_results['summary']['total_documents_tested']}")
        logger.info(f"Enhanced extraction success rate: {comprehensive_results['summary']['enhanced_extraction_success_rate']:.1%}")
        logger.info(f"Average metadata confidence: {comprehensive_results['summary']['average_metadata_confidence']:.3f}")
        logger.info(f"Average chunk metadata coverage: {comprehensive_results['summary']['average_chunk_metadata_coverage']:.1%}")
        logger.info(f"Results saved to: {output_file}")
        
        return comprehensive_results


def main():
    """Main function to run the enhanced metadata extraction tests."""
    print("Enhanced Metadata Extraction Test")
    print("=" * 50)
    print("This script demonstrates the enhanced article title and author")
    print("extraction capabilities using the hybrid semantic chunking system.")
    print()
    
    try:
        # Initialize tester
        tester = EnhancedMetadataExtractionTester()
        
        # Run comprehensive test
        results = tester.run_comprehensive_test()
        
        print("\nTest completed successfully!")
        print(f"Check the generated JSON file for detailed results.")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"\nTest failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 
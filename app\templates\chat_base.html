<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ERDB Knowledge Products - Powered by ERDB AI{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Toastify CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">
    <!-- Chat Interface CSS -->
    <link rel="stylesheet" href="/static/css/chat.css">
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>

    <script>
        // Configure marked.js to properly handle links and other markdown elements
        marked.setOptions({
            breaks: true,  // Add line breaks
            gfm: true,     // Enable GitHub Flavored Markdown
            headerIds: false,
            mangle: false,
            sanitize: false, // Allow HTML in markdown
            renderer: (function() {
                const renderer = new marked.Renderer();

                // Override the link renderer to add target="_blank" and other attributes
                renderer.link = function(href, title, text) {
                    const link = marked.Renderer.prototype.link.call(this, href, title, text);
                    return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="text-primary" ');
                };

                return renderer;
            })()
        });
    </script>

    {% block head %}{% endblock %}
</head>
<body class="bg-secondary min-vh-100 d-flex flex-column">
    {% block modals %}{% endblock %}

    <div class="container-fluid py-3 flex-grow-1 d-flex flex-column">
        <div class="card bg-card border-standard shadow h-100 d-flex flex-column">
            <div class="card-header bg-card border-bottom border-standard d-flex flex-column align-items-center">
                {% if cover_image_url %}
                    <img src="{{ cover_image_url }}" alt="Chat Cover Image" class="chat-cover-image mb-2" style="max-width: 100%; max-height: 180px; object-fit: cover; border-radius: 0.5rem;"/>
                {% else %}
                    <img src="/static/images/cover-placeholder.png" alt="Chat Cover Image" class="chat-cover-image mb-2" style="max-width: 100%; max-height: 180px; object-fit: cover; border-radius: 0.5rem;"/>
                {% endif %}
                <div class="w-100 d-flex justify-content-between align-items-center">
                    <h1 class="h4 text-primary mb-0">ERDB Knowledge Hub - Powered by ERDB AI</h1>
                    <div class="d-flex align-items-center gap-3">
                        <a href="{{ url_for('admin.admin_dashboard') }}" class="text-primary">
                            <i class="fas fa-tachometer-alt me-1"></i>Admin Dashboard
                        </a>
                        <button id="logout-button" class="btn btn-sm btn-outline-danger" title="Logout and clear chat history">
                            <i class="fas fa-sign-out-alt me-1"></i><span id="logout-text">Logout</span>
                        </button>
                        <button id="theme-toggle" class="btn btn-sm btn-outline-secondary" type="button">
                            <i id="theme-icon" class="fas fa-moon"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Script Loading Management -->
    <script>
        // Prevent duplicate script loading
        window.loadedScripts = window.loadedScripts || new Set();
    </script>

    <!-- Shared Utilities -->
    <script src="/static/js/utilities.js" onload="window.loadedScripts.add('/static/js/utilities.js')"></script>

    {% block scripts %}{% endblock %}
</body>
</html>

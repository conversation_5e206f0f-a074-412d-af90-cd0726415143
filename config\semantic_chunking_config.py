"""
Semantic Chunking Configuration

This module provides configuration presets and settings for the semantic chunking service.
It includes different strategies optimized for various content types and use cases.
"""

from typing import Dict, Any
from app.services.semantic_chunking_service import ChunkingStrategy, ContentType, ChunkingConfig


# Default configuration for semantic chunking
DEFAULT_SEMANTIC_CONFIG = {
    "strategy": "hybrid",
    "chunk_size": 800,
    "chunk_overlap": 160,
    "similarity_threshold": 0.7,
    "max_chunk_size": 1200,
    "min_chunk_size": 200,
    "structural_split": True,
    "semantic_refinement": True,
    "metadata_enrichment": True,
    "preserve_hierarchy": True,
    "extract_keywords": True,
    "content_type_detection": True
}

# Performance presets for different use cases
CHUNKING_PRESETS = {
    "high_precision": {
        "name": "High Precision",
        "description": "Smaller chunks for precise, focused answers",
        "config": {
            "strategy": "hybrid",
            "chunk_size": 500,
            "chunk_overlap": 50,
            "similarity_threshold": 0.8,
            "max_chunk_size": 800,
            "min_chunk_size": 100,
            "structural_split": True,
            "semantic_refinement": True,
            "metadata_enrichment": True
        }
    },
    "optimal_rag": {
        "name": "Optimal RAG",
        "description": "Best overall performance for RAG systems",
        "config": {
            "strategy": "hybrid",
            "chunk_size": 800,
            "chunk_overlap": 160,
            "similarity_threshold": 0.7,
            "max_chunk_size": 1200,
            "min_chunk_size": 200,
            "structural_split": True,
            "semantic_refinement": True,
            "metadata_enrichment": True
        }
    },
    "high_recall": {
        "name": "High Recall",
        "description": "Larger chunks for comprehensive answers",
        "config": {
            "strategy": "hybrid",
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "similarity_threshold": 0.6,
            "max_chunk_size": 1500,
            "min_chunk_size": 300,
            "structural_split": True,
            "semantic_refinement": True,
            "metadata_enrichment": True
        }
    },
    "scientific_papers": {
        "name": "Scientific Papers",
        "description": "Optimized for academic and research documents",
        "config": {
            "strategy": "hybrid",
            "chunk_size": 600,
            "chunk_overlap": 120,
            "similarity_threshold": 0.75,
            "max_chunk_size": 1000,
            "min_chunk_size": 150,
            "structural_split": True,
            "semantic_refinement": True,
            "metadata_enrichment": True,
            "extract_keywords": True,
            "content_type_detection": True
        }
    },
    "web_content": {
        "name": "Web Content",
        "description": "Optimized for web pages and HTML content",
        "config": {
            "strategy": "structural",
            "chunk_size": 700,
            "chunk_overlap": 140,
            "similarity_threshold": 0.65,
            "max_chunk_size": 1100,
            "min_chunk_size": 200,
            "structural_split": True,
            "semantic_refinement": False,
            "metadata_enrichment": True
        }
    },
    "technical_docs": {
        "name": "Technical Documentation",
        "description": "Optimized for technical and API documentation",
        "config": {
            "strategy": "hybrid",
            "chunk_size": 900,
            "chunk_overlap": 180,
            "similarity_threshold": 0.7,
            "max_chunk_size": 1300,
            "min_chunk_size": 250,
            "structural_split": True,
            "semantic_refinement": True,
            "metadata_enrichment": True
        }
    }
}

# Content type specific configurations
CONTENT_TYPE_CONFIGS = {
    ContentType.SCIENTIFIC_PAPER: {
        "strategy": "hybrid",
        "chunk_size": 600,
        "chunk_overlap": 120,
        "similarity_threshold": 0.75,
        "structural_split": True,
        "semantic_refinement": True,
        "metadata_enrichment": True,
        "extract_keywords": True
    },
    ContentType.TECHNICAL_DOCUMENT: {
        "strategy": "hybrid",
        "chunk_size": 900,
        "chunk_overlap": 180,
        "similarity_threshold": 0.7,
        "structural_split": True,
        "semantic_refinement": True,
        "metadata_enrichment": True
    },
    ContentType.WEB_CONTENT: {
        "strategy": "structural",
        "chunk_size": 700,
        "chunk_overlap": 140,
        "similarity_threshold": 0.65,
        "structural_split": True,
        "semantic_refinement": False,
        "metadata_enrichment": True
    },
    ContentType.GENERAL_DOCUMENT: {
        "strategy": "hybrid",
        "chunk_size": 800,
        "chunk_overlap": 160,
        "similarity_threshold": 0.7,
        "structural_split": True,
        "semantic_refinement": True,
        "metadata_enrichment": True
    }
}

# Strategy descriptions for UI
STRATEGY_DESCRIPTIONS = {
    "recursive": {
        "name": "Recursive Character",
        "description": "Traditional character-based splitting with overlap",
        "pros": ["Fast", "Simple", "Consistent"],
        "cons": ["No semantic awareness", "May break context", "Fixed boundaries"]
    },
    "semantic": {
        "name": "Semantic",
        "description": "Meaning-based chunking using sentence similarity",
        "pros": ["Semantic coherence", "Context preservation", "Intelligent grouping"],
        "cons": ["Slower", "Requires ML models", "More complex"]
    },
    "structural": {
        "name": "Structural",
        "description": "Document structure-aware splitting (headers, sections)",
        "pros": ["Preserves hierarchy", "Respects document structure", "Good for formatted content"],
        "cons": ["Requires structured content", "May not work for plain text", "Limited semantic understanding"]
    },
    "hybrid": {
        "name": "Hybrid Semantic",
        "description": "Combines structural pre-splitting with semantic refinement",
        "pros": ["Best of both worlds", "Optimal for RAG", "Rich metadata"],
        "cons": ["Most complex", "Slowest", "Requires more resources"]
    }
}

# Performance metrics and recommendations
PERFORMANCE_METRICS = {
    "processing_speed": {
        "recursive": "Fast",
        "semantic": "Medium",
        "structural": "Fast",
        "hybrid": "Slow"
    },
    "memory_usage": {
        "recursive": "Low",
        "semantic": "Medium",
        "structural": "Low",
        "hybrid": "High"
    },
    "retrieval_accuracy": {
        "recursive": "Good",
        "semantic": "Excellent",
        "structural": "Very Good",
        "hybrid": "Best"
    },
    "context_preservation": {
        "recursive": "Fair",
        "semantic": "Excellent",
        "structural": "Very Good",
        "hybrid": "Best"
    }
}


def get_chunking_config(preset: str = "optimal_rag", **overrides) -> ChunkingConfig:
    """
    Get a chunking configuration based on preset name.
    
    Args:
        preset: Name of the preset to use
        **overrides: Additional configuration overrides
        
    Returns:
        ChunkingConfig instance
    """
    if preset not in CHUNKING_PRESETS:
        preset = "optimal_rag"
    
    config_dict = CHUNKING_PRESETS[preset]["config"].copy()
    config_dict.update(overrides)
    
    return ChunkingConfig(**config_dict)


def get_content_type_config(content_type: ContentType, **overrides) -> ChunkingConfig:
    """
    Get chunking configuration optimized for specific content type.
    
    Args:
        content_type: Type of content being processed
        **overrides: Additional configuration overrides
        
    Returns:
        ChunkingConfig instance
    """
    config_dict = CONTENT_TYPE_CONFIGS.get(content_type, CONTENT_TYPE_CONFIGS[ContentType.GENERAL_DOCUMENT]).copy()
    config_dict.update(overrides)
    
    return ChunkingConfig(**config_dict)


def get_available_presets() -> Dict[str, Dict[str, Any]]:
    """Get all available chunking presets."""
    return CHUNKING_PRESETS


def get_strategy_info(strategy: str) -> Dict[str, Any]:
    """Get information about a specific chunking strategy."""
    return STRATEGY_DESCRIPTIONS.get(strategy, STRATEGY_DESCRIPTIONS["recursive"])


def get_performance_comparison() -> Dict[str, Dict[str, str]]:
    """Get performance comparison across all strategies."""
    return PERFORMANCE_METRICS


def validate_chunking_config(config: Dict[str, Any]) -> bool:
    """
    Validate chunking configuration parameters.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        # Check required fields
        required_fields = ["chunk_size", "chunk_overlap"]
        for field in required_fields:
            if field not in config:
                return False
        
        # Validate numeric ranges
        if not (100 <= config["chunk_size"] <= 2000):
            return False
        
        if not (0 <= config["chunk_overlap"] <= config["chunk_size"]):
            return False
        
        if "similarity_threshold" in config:
            if not (0.0 <= config["similarity_threshold"] <= 1.0):
                return False
        
        # Validate strategy
        if "strategy" in config:
            valid_strategies = [s.value for s in ChunkingStrategy]
            if config["strategy"] not in valid_strategies:
                return False
        
        return True
        
    except (KeyError, TypeError, ValueError):
        return False


def get_recommended_config(content_type: str = None, use_case: str = None) -> Dict[str, Any]:
    """
    Get recommended configuration based on content type and use case.
    
    Args:
        content_type: Type of content being processed
        use_case: Specific use case (precision, recall, etc.)
        
    Returns:
        Recommended configuration dictionary
    """
    if use_case:
        if use_case in CHUNKING_PRESETS:
            return CHUNKING_PRESETS[use_case]["config"]
    
    if content_type:
        content_type_enum = getattr(ContentType, content_type.upper(), ContentType.GENERAL_DOCUMENT)
        return CONTENT_TYPE_CONFIGS[content_type_enum]
    
    # Default to optimal RAG
    return CHUNKING_PRESETS["optimal_rag"]["config"] 
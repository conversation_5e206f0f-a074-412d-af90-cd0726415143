#!/usr/bin/env python3
"""
Test script for enhanced title extraction
Tests the improved logic that can handle titles with varying font sizes
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.pdf_processor import (
    extract_titles_and_authors_from_ocr_pdf,
    extract_titles_and_authors_from_pdf,
    extract_text_with_font_sizes_ocr
)

def test_enhanced_title_extraction():
    """Test the enhanced title extraction logic"""
    
    # Path to the problematic PDF
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"
    
    print("=" * 80)
    print("🧪 TESTING ENHANCED TITLE EXTRACTION")
    print("=" * 80)
    print(f"PDF Path: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    print()
    
    if not os.path.exists(pdf_path):
        print("❌ PDF file not found!")
        return
    
    # Test 1: Enhanced OCR-based extraction
    print("=" * 80)
    print("📄 TEST 1: ENHANCED OCR-BASED EXTRACTION")
    print("=" * 80)
    
    try:
        ocr_articles = extract_titles_and_authors_from_ocr_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,  # Don't skip first page for debugging
            min_title_font=8,  # Very permissive
            min_title_length=2,
            max_title_gap=100,  # Large gap for multi-line titles
            max_author_gap=120,
            min_upper_ratio=0.3,  # Very permissive
            min_author_font=6,
            max_author_font=25,
            title_font_ratio=0.6  # Very permissive
        )
        
        print("\n" + "=" * 80)
        print("📊 ENHANCED OCR EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(ocr_articles)} articles")
        
        for i, article in enumerate(ocr_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ OCR extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Enhanced Native PDF extraction
    print("\n" + "=" * 80)
    print("📄 TEST 2: ENHANCED NATIVE PDF EXTRACTION")
    print("=" * 80)
    
    try:
        native_articles = extract_titles_and_authors_from_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,
            min_title_font=8,
            min_title_length=2,
            max_title_gap=100,
            max_author_gap=120,
            min_upper_ratio=0.3,
            min_author_font=6,
            max_author_font=25,
            title_font_ratio=0.6
        )
        
        print("\n" + "=" * 80)
        print("📊 ENHANCED NATIVE PDF EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(native_articles)} articles")
        
        for i, article in enumerate(native_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ Native PDF extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Raw text blocks analysis
    print("\n" + "=" * 80)
    print("📄 TEST 3: RAW TEXT BLOCKS ANALYSIS")
    print("=" * 80)
    
    try:
        ocr_text_data = extract_text_with_font_sizes_ocr(pdf_path, debug=True)
        
        print(f"\nFound {len(ocr_text_data)} pages with OCR data")
        
        for page_data in ocr_text_data[:2]:  # First 2 pages only
            page_num = page_data["page"]
            font_analysis = page_data.get("font_size_analysis", {})
            text_blocks = font_analysis.get("text_blocks", [])
            
            print(f"\n📄 Page {page_num}: {len(text_blocks)} text blocks")
            print("-" * 60)
            
            # Show first 30 text blocks with font size analysis
            for i, block in enumerate(text_blocks[:30]):
                text = block.get('text', '')
                font_size = block.get('font_size', 0)
                bbox = block.get('bbox', [0,0,0,0])
                confidence = block.get('confidence', 100)
                
                # Highlight potential title parts
                is_upper = text.isupper() if text else False
                word_count = len(text.split()) if text else 0
                highlight = "🔍" if is_upper and word_count >= 2 else "  "
                
                print(f"{highlight} {i+1:2d}. Font: {font_size:2.0f}pt | Pos: ({bbox[0]:4.0f}, {bbox[1]:4.0f}) | Conf: {confidence:3.0f}% | '{text}'")
            
            if len(text_blocks) > 30:
                print(f"... and {len(text_blocks) - 30} more blocks")
                
    except Exception as e:
        print(f"❌ Raw OCR extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("✅ ENHANCED EXTRACTION TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_enhanced_title_extraction() 
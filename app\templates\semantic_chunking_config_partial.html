<!-- Semantic Chunking Configuration Section -->
<div class="config-section">
    <h2 class="config-section-header">Semantic Chunking Configuration</h2>
    <p class="config-section-description">Configure advanced semantic chunking strategies for optimal document processing and retrieval performance.</p>
    
    <!-- Semantic Chunking Status Panel -->
    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900 dark:to-indigo-900 p-6 rounded-lg border border-purple-200 dark:border-purple-600 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-2">Advanced Semantic Chunking</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Current Strategy</div>
                        <div class="text-purple-600 dark:text-purple-400 font-semibold">{{ semantic_config.strategy|title }}</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Advanced processing</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Performance</div>
                        <div class="text-indigo-600 dark:text-indigo-400 font-semibold">{{ performance_metrics[semantic_config.strategy] }}</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Retrieval accuracy</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Processing</div>
                        <div class="text-blue-600 dark:text-blue-400 font-semibold">{{ processing_speed[semantic_config.strategy] }}</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Speed vs quality</div>
                    </div>
                </div>
                <div class="text-sm text-purple-700 dark:text-purple-300">
                    <p><strong>Benefits:</strong> Semantic chunking creates more meaningful, contextually coherent chunks that preserve document structure while optimizing for retrieval relevance.</p>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Text contrast fixes for semantic chunking config */
        .config-section-header { color: #1a202c !important; }
        .config-section-description { color: #4a5568 !important; }
        
        /* Form element text colors */
        textarea, input, select { color: #1a202c !important; }
        
        /* Label text colors */
        label.text-sm.font-medium.text-gray-700,
        .text-gray-700 { color: #2d3748 !important; }
        
        /* Helper text colors */
        .text-xs.text-gray-500 { color: #6b7280 !important; }
        
        /* Strategy cards */
        .strategy-card { 
            border: 2px solid #e2e8f0; 
            transition: all 0.3s ease;
        }
        .strategy-card:hover { 
            border-color: #8b5cf6; 
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
        }
        .strategy-card.selected { 
            border-color: #8b5cf6; 
            background-color: #f3f4f6;
        }
        
        /* Preset buttons */
        .preset-btn {
            transition: all 0.2s ease;
        }
        .preset-btn:hover {
            transform: translateY(-1px);
        }
    </style>

    <!-- Strategy Selection -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Chunking Strategy</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {% for strategy, info in strategy_info.items() %}
            <div class="strategy-card p-4 rounded-lg cursor-pointer {% if semantic_config.strategy == strategy %}selected{% endif %}"
                 onclick="selectStrategy('{{ strategy }}')">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-800 dark:text-gray-200">{{ info.name }}</h4>
                    <input type="radio" name="chunking_strategy" value="{{ strategy }}" 
                           {% if semantic_config.strategy == strategy %}checked{% endif %}
                           class="text-purple-600 focus:ring-purple-500">
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ info.description }}</p>
                <div class="space-y-1">
                    <div class="text-xs font-medium text-green-600">Pros:</div>
                    <ul class="text-xs text-gray-500 space-y-1">
                        {% for pro in info.pros %}
                        <li>• {{ pro }}</li>
                        {% endfor %}
                    </ul>
                    <div class="text-xs font-medium text-red-600 mt-2">Cons:</div>
                    <ul class="text-xs text-gray-500 space-y-1">
                        {% for con in info.cons %}
                        <li>• {{ con }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Performance Presets -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Performance Presets</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for preset_id, preset in chunking_presets.items() %}
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-800 dark:text-gray-200">{{ preset.name }}</h4>
                    <button type="button" 
                            onclick="applyPreset('{{ preset_id }}')"
                            class="preset-btn px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors">
                        Apply
                    </button>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ preset.description }}</p>
                <div class="text-xs text-gray-500 space-y-1">
                    <div><strong>Chunk Size:</strong> {{ preset.config.chunk_size }} chars</div>
                    <div><strong>Overlap:</strong> {{ preset.config.chunk_overlap }} chars</div>
                    <div><strong>Strategy:</strong> {{ preset.config.strategy|title }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Advanced Configuration -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Basic Parameters -->
        <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Basic Parameters</h3>
            
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="semantic_chunk_size" class="block text-sm font-medium text-gray-700 mb-2">
                    Chunk Size (characters) <span class="text-purple-600 font-semibold">({{ semantic_config.chunk_size }})</span>
                </label>
                <input type="number" id="semantic_chunk_size" name="semantic_chunk_size" 
                       min="200" max="1500" step="50" value="{{ semantic_config.chunk_size }}"
                       class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                <p class="mt-2 text-xs text-gray-500">Target size for each chunk in characters.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="semantic_chunk_overlap" class="block text-sm font-medium text-gray-700 mb-2">
                    Chunk Overlap (characters) <span class="text-purple-600 font-semibold">({{ semantic_config.chunk_overlap }})</span>
                </label>
                <input type="number" id="semantic_chunk_overlap" name="semantic_chunk_overlap" 
                       min="0" max="300" step="10" value="{{ semantic_config.chunk_overlap }}"
                       class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
                <p class="mt-2 text-xs text-gray-500">Overlap between consecutive chunks for context continuity.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="similarity_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                    Semantic Similarity Threshold <span class="text-purple-600 font-semibold">({{ semantic_config.similarity_threshold }})</span>
                </label>
                <input type="range" id="similarity_threshold" name="similarity_threshold" 
                       min="0.5" max="0.9" step="0.05" value="{{ semantic_config.similarity_threshold }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0.5 (Loose)</span>
                    <span>0.9 (Strict)</span>
                </div>
                <p class="mt-2 text-xs text-gray-500">Threshold for grouping semantically similar sentences.</p>
            </div>
        </div>

        <!-- Advanced Options -->
        <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Advanced Options</h3>
            
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" name="structural_split" 
                           {% if semantic_config.structural_split %}checked{% endif %}
                           class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Structural Pre-Splitting</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Split by document structure (headers, sections) before semantic processing.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" name="semantic_refinement" 
                           {% if semantic_config.semantic_refinement %}checked{% endif %}
                           class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Semantic Refinement</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Apply semantic similarity analysis to group related content.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" name="metadata_enrichment" 
                           {% if semantic_config.metadata_enrichment %}checked{% endif %}
                           class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Metadata Enrichment</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Add rich metadata including keywords, content type, and quality scores.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" name="extract_keywords" 
                           {% if semantic_config.extract_keywords %}checked{% endif %}
                           class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Extract Keywords</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Automatically extract semantic keywords from chunks for better retrieval.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" name="content_type_detection" 
                           {% if semantic_config.content_type_detection %}checked{% endif %}
                           class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Content Type Detection</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Automatically detect and optimize for different content types.</p>
            </div>
        </div>
    </div>

    <!-- Performance Comparison -->
    <div class="mt-8">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Performance Comparison</h3>
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-700">
                            <th class="text-left py-2 px-4 font-medium text-gray-700">Strategy</th>
                            <th class="text-left py-2 px-4 font-medium text-gray-700">Processing Speed</th>
                            <th class="text-left py-2 px-4 font-medium text-gray-700">Memory Usage</th>
                            <th class="text-left py-2 px-4 font-medium text-gray-700">Retrieval Accuracy</th>
                            <th class="text-left py-2 px-4 font-medium text-gray-700">Context Preservation</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for strategy, metrics in performance_comparison.items() %}
                        <tr class="border-b border-gray-100 dark:border-gray-800">
                            <td class="py-2 px-4 font-medium text-gray-800">{{ strategy|title }}</td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 text-xs rounded-full 
                                    {% if metrics.processing_speed == 'Fast' %}bg-green-100 text-green-800
                                    {% elif metrics.processing_speed == 'Medium' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ metrics.processing_speed }}
                                </span>
                            </td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 text-xs rounded-full 
                                    {% if metrics.memory_usage == 'Low' %}bg-green-100 text-green-800
                                    {% elif metrics.memory_usage == 'Medium' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ metrics.memory_usage }}
                                </span>
                            </td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 text-xs rounded-full 
                                    {% if metrics.retrieval_accuracy == 'Best' %}bg-green-100 text-green-800
                                    {% elif metrics.retrieval_accuracy == 'Excellent' %}bg-blue-100 text-blue-800
                                    {% elif metrics.retrieval_accuracy == 'Very Good' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ metrics.retrieval_accuracy }}
                                </span>
                            </td>
                            <td class="py-2 px-4">
                                <span class="px-2 py-1 text-xs rounded-full 
                                    {% if metrics.context_preservation == 'Best' %}bg-green-100 text-green-800
                                    {% elif metrics.context_preservation == 'Excellent' %}bg-blue-100 text-blue-800
                                    {% elif metrics.context_preservation == 'Very Good' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ metrics.context_preservation }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function selectStrategy(strategy) {
    // Update radio button
    document.querySelector(`input[name="chunking_strategy"][value="${strategy}"]`).checked = true;
    
    // Update visual selection
    document.querySelectorAll('.strategy-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
    
    // Update form data
    updateSemanticConfig();
}

function applyPreset(presetId) {
    // Get preset configuration
    const presets = {{ chunking_presets|tojson }};
    const preset = presets[presetId];
    
    if (!preset) return;
    
    const config = preset.config;
    
    // Update form fields
    document.getElementById('semantic_chunk_size').value = config.chunk_size;
    document.getElementById('semantic_chunk_overlap').value = config.chunk_overlap;
    document.getElementById('similarity_threshold').value = config.similarity_threshold;
    
    // Update strategy
    document.querySelector(`input[name="chunking_strategy"][value="${config.strategy}"]`).checked = true;
    selectStrategy(config.strategy);
    
    // Update checkboxes
    document.querySelector('input[name="structural_split"]').checked = config.structural_split || false;
    document.querySelector('input[name="semantic_refinement"]').checked = config.semantic_refinement || false;
    document.querySelector('input[name="metadata_enrichment"]').checked = config.metadata_enrichment || false;
    document.querySelector('input[name="extract_keywords"]').checked = config.extract_keywords || false;
    document.querySelector('input[name="content_type_detection"]').checked = config.content_type_detection || false;
    
    // Show success message
    showNotification(`Applied ${preset.name} preset`, 'success');
}

function updateSemanticConfig() {
    // Collect form data
    const formData = new FormData();
    
    // Basic parameters
    formData.append('semantic_chunk_size', document.getElementById('semantic_chunk_size').value);
    formData.append('semantic_chunk_overlap', document.getElementById('semantic_chunk_overlap').value);
    formData.append('similarity_threshold', document.getElementById('similarity_threshold').value);
    
    // Strategy
    const selectedStrategy = document.querySelector('input[name="chunking_strategy"]:checked');
    if (selectedStrategy) {
        formData.append('chunking_strategy', selectedStrategy.value);
    }
    
    // Advanced options
    formData.append('structural_split', document.querySelector('input[name="structural_split"]').checked);
    formData.append('semantic_refinement', document.querySelector('input[name="semantic_refinement"]').checked);
    formData.append('metadata_enrichment', document.querySelector('input[name="metadata_enrichment"]').checked);
    formData.append('extract_keywords', document.querySelector('input[name="extract_keywords"]').checked);
    formData.append('content_type_detection', document.querySelector('input[name="content_type_detection"]').checked);
    
    // Send to server
    fetch('/admin/update_semantic_chunking', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Semantic chunking configuration updated', 'success');
        } else {
            showNotification('Failed to update configuration: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Error updating configuration: ' + error.message, 'error');
    });
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Update configuration when form fields change
    const formFields = [
        'semantic_chunk_size', 'semantic_chunk_overlap', 'similarity_threshold',
        'structural_split', 'semantic_refinement', 'metadata_enrichment',
        'extract_keywords', 'content_type_detection'
    ];
    
    formFields.forEach(fieldId => {
        const element = document.getElementById(fieldId) || document.querySelector(`input[name="${fieldId}"]`);
        if (element) {
            element.addEventListener('change', updateSemanticConfig);
        }
    });
    
    // Strategy radio buttons
    document.querySelectorAll('input[name="chunking_strategy"]').forEach(radio => {
        radio.addEventListener('change', updateSemanticConfig);
    });
});

function showNotification(message, type) {
    // Simple notification function - you can replace with your preferred notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script> 
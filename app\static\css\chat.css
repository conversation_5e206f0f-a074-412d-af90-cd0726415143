/* Chat Interface Styles */

/* Chat container and layout */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.chat-box {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: var(--bg-secondary);
}

/* Chat messages */
.chat-message {
    margin-bottom: 1rem;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.chat-message.fade-in {
    opacity: 0;
}

.user-message {
    background-color: var(--primary-50);
    border: 1px solid var(--primary-200);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-left: auto;
    max-width: 80%;
}

.bot-message {
    background-color: var(--success-50);
    border: 1px solid var(--success-200);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-right: auto;
    max-width: 80%;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-avatar {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
}

.message-sender {
    font-weight: var(--font-weight-medium);
    margin-right: 0.5rem;
}

.message-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-content {
    color: var(--text-primary);
}

/* Anti-hallucination mode badges */
.mode-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    margin-top: 0.5rem;
}

.mode-strict {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.mode-balanced {
    background-color: var(--success-100);
    color: var(--success-800);
}

.mode-creative {
    background-color: var(--danger-100);
    color: var(--danger-800);
}

/* Document thumbnails */
.document-thumbnails-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.document-thumbnail {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.cover-image {
    order: -1;
    border: 2px solid var(--primary-600);
    border-radius: var(--border-radius);
    padding: 0.25rem;
    background-color: var(--primary-50);
}

/* Sources section */
.sources-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.source-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.source-icon {
    margin-right: 0.5rem;
    color: var(--primary-600);
}

.truncate-url {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: middle;
}

@media (max-width: 768px) {
    .truncate-url {
        max-width: 150px;
    }
    
    .user-message, .bot-message {
        max-width: 90%;
    }
}

/* Modal styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal-backdrop);
}

.modal-content {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1.5rem;
    max-width: 500px;
    width: 100%;
}

/* Ensure images in prose content are responsive */
.prose img {
    max-width: 100%;
    height: auto;
}

/* Related images grid */
.related-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.related-image-item {
    position: relative;
    height: 6rem;
    overflow: hidden;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.related-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem;
    font-size: var(--font-size-xs);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Chat input area */
.chat-input-container {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-card);
}

.chat-input-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.chat-input-textarea {
    resize: none;
    min-height: 38px;
    max-height: 150px;
    overflow-y: auto;
}

/* Loading indicator */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.loading-dots {
    display: flex;
    align-items: center;
}

.loading-dot {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    border-radius: 50%;
    background-color: var(--primary-600);
    animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dot-pulse {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Dark mode adjustments */
.dark-mode .user-message {
    background-color: var(--primary-900);
    border-color: var(--primary-700);
    color: var(--secondary-100);
}

.dark-mode .bot-message {
    background-color: var(--success-900);
    border-color: var(--success-700);
    color: var(--secondary-100);
}

.dark-mode .mode-strict {
    background-color: var(--primary-900);
    color: var(--primary-200);
}

.dark-mode .mode-balanced {
    background-color: var(--success-900);
    color: var(--success-200);
}

.dark-mode .mode-creative {
    background-color: var(--danger-900);
    color: var(--danger-200);
}

.dark-mode .cover-image {
    border-color: var(--primary-400);
    background-color: var(--primary-900);
}

.dark-mode .related-image-caption {
    background-color: rgba(0, 0, 0, 0.9);
}

/* Follow-up questions */
.follow-up-questions {
    margin-top: 1rem;
}

.follow-up-question {
    display: inline-block;
    margin: 0.25rem;
    padding: 0.5rem 0.75rem;
    background-color: var(--primary-50);
    border: 1px solid var(--primary-200);
    border-radius: var(--border-radius);
    color: var(--primary-700);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.follow-up-question:hover {
    background-color: var(--primary-100);
    border-color: var(--primary-300);
    color: var(--primary-800);
}

.dark-mode .follow-up-question {
    background-color: var(--primary-900);
    border-color: var(--primary-700);
    color: var(--primary-300);
}

.dark-mode .follow-up-question:hover {
    background-color: var(--primary-800);
    border-color: var(--primary-600);
    color: var(--primary-200);
}

.chat-cover-image {
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%;
    max-height: 180px;
    object-fit: cover;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 2px solid var(--primary-200);
    background: var(--primary-50);
}

.dark-mode .chat-cover-image {
    border: 2px solid var(--primary-700);
    background: var(--primary-900);
}

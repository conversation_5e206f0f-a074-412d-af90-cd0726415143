# Production Deployment Guide: LlamaIndex + LangChain Integration

## 🚀 Overview

This guide provides step-by-step instructions for deploying the LlamaIndex + LangChain hybrid integration in a production environment. The integration has been thoroughly tested and is ready for production use.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.8+ (3.10+ recommended)
- **Memory**: Minimum 8GB RAM (16GB+ recommended for large document processing)
- **Storage**: Sufficient space for document storage and vector databases
- **Ollama**: Installed and running with required models

### Required Ollama Models
```bash
# Install required models
ollama pull llama3.1:8b-instruct-q4_K_M
ollama pull nomic-embed-text
```

### Dependencies
All dependencies are listed in `requirements.txt` and have been tested:
```bash
pip install -r requirements.txt
```

## 🔧 Production Configuration

### 1. Environment Setup

Create a production environment file:
```bash
# .env.production
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secure-secret-key
DATABASE_URL=your-production-database-url
OLLAMA_BASE_URL=http://localhost:11434
```

### 2. LlamaIndex Configuration

Update `config/rag_extraction_config.py` for production:

```python
LLAMAINDEX_CONFIG = {
    "enabled": True,
    "ollama_model": "llama3.1:8b-instruct-q4_K_M",
    "retrieval_strategy": "hybrid",
    "similarity_top_k": 5,
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "response_mode": "tree_summarize",
    "streaming": True,
    "structured_answer_filtering": True,
    "hybrid_alpha": 0.5,
    "enable_hybrid_retriever": True,
    "enable_performance_monitoring": True,
    # Production-specific settings
    "max_concurrent_queries": 10,
    "query_timeout": 300,  # 5 minutes
    "embedding_batch_size": 50,
    "index_persistence": True,
    "cache_embeddings": True
}
```

### 3. Performance Optimization

#### Memory Management
```python
# In your application startup
import gc
import psutil

def optimize_memory():
    """Optimize memory usage for production"""
    gc.collect()
    if psutil.virtual_memory().percent > 80:
        logger.warning("High memory usage detected")
```

#### Query Optimization
```python
# Configure for your use case
update_llamaindex_config(
    similarity_top_k=5,      # Balance between speed and quality
    chunk_size=1000,         # Optimal for most documents
    response_mode="tree_summarize",  # Best quality
    streaming=True           # Better user experience
)
```

## 🚀 Deployment Steps

### Step 1: Production Environment Setup

```bash
# Create production virtual environment
python -m venv venv_production
source venv_production/bin/activate  # Linux/Mac
# or
venv_production\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Install production dependencies
pip install gunicorn uwsgi
```

### Step 2: Database Setup

```bash
# Initialize production database
python -c "from app import create_app; app = create_app(); app.app_context().push(); from app.models import db; db.create_all()"

# Run migrations if needed
python scripts/migrations/run_migrations.py
```

### Step 3: Ollama Service Setup

```bash
# Start Ollama service
ollama serve

# Verify models are available
ollama list
```

### Step 4: Application Deployment

#### Option A: Gunicorn (Recommended)
```bash
# Create gunicorn config
cat > gunicorn.conf.py << EOF
bind = "0.0.0.0:8080"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# Start application
gunicorn -c gunicorn.conf.py app:create_app()
```

#### Option B: Docker Deployment
```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:create_app()"]
```

### Step 5: Reverse Proxy Setup (Nginx)

```nginx
# /etc/nginx/sites-available/erdb-ai
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # LlamaIndex query timeout
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Static files
    location /static/ {
        alias /path/to/your/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 Monitoring and Logging

### 1. Application Logging

```python
# Configure production logging
import logging
from logging.handlers import RotatingFileHandler

def setup_production_logging():
    """Setup production logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            RotatingFileHandler('logs/app.log', maxBytes=10000000, backupCount=5),
            logging.StreamHandler()
        ]
    )
```

### 2. Performance Monitoring

```python
# Monitor LlamaIndex performance
from app.utils.performance_monitor import get_performance_stats

def monitor_llamaindex_performance():
    """Monitor LlamaIndex performance metrics"""
    stats = get_performance_stats()
    
    # Log performance metrics
    logger.info(f"LlamaIndex Performance: {stats}")
    
    # Alert on performance issues
    if stats.get('avg_query_time', 0) > 60:
        logger.warning("Slow query performance detected")
```

### 3. Health Checks

```python
# Health check endpoint
@app.route('/health')
def health_check():
    """Production health check"""
    try:
        # Check Ollama connection
        import requests
        response = requests.get('http://localhost:11434/api/tags')
        
        # Check database connection
        from app.models import db
        db.session.execute('SELECT 1')
        
        return {
            'status': 'healthy',
            'ollama': 'connected',
            'database': 'connected',
            'llamaindex': 'enabled'
        }, 200
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e)
        }, 500
```

## 🔒 Security Considerations

### 1. API Security
```python
# Rate limiting
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/api/query')
@limiter.limit("10 per minute")
def query_documents():
    """Rate-limited query endpoint"""
    pass
```

### 2. Input Validation
```python
# Validate query inputs
from marshmallow import Schema, fields

class QuerySchema(Schema):
    query = fields.Str(required=True, validate=lambda x: len(x) <= 1000)
    strategy = fields.Str(validate=lambda x: x in ['hybrid', 'standard', 'multimodal'])

@app.route('/api/query', methods=['POST'])
def query_documents():
    schema = QuerySchema()
    data = schema.load(request.json)
    # Process query...
```

### 3. File Upload Security
```python
# Secure file upload
import os
from werkzeug.utils import secure_filename

ALLOWED_EXTENSIONS = {'pdf', 'txt', 'doc', 'docx'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return {'error': 'No file provided'}, 400
    
    file = request.files['file']
    if file.filename == '':
        return {'error': 'No file selected'}, 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Process file...
```

## 📈 Scaling Considerations

### 1. Horizontal Scaling
```python
# Redis for session management
from flask_session import Session
from redis import Redis

app.config['SESSION_TYPE'] = 'redis'
app.config['SESSION_REDIS'] = Redis(host='localhost', port=6379)
Session(app)
```

### 2. Load Balancing
```nginx
# Nginx load balancer configuration
upstream app_servers {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
    server 127.0.0.1:8083;
}

server {
    listen 80;
    location / {
        proxy_pass http://app_servers;
        # ... other proxy settings
    }
}
```

### 3. Database Scaling
```python
# Database connection pooling
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    'postgresql://user:pass@localhost/dbname',
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

## 🧪 Testing in Production

### 1. Smoke Tests
```python
# production_smoke_test.py
def test_production_health():
    """Basic production health check"""
    response = requests.get('http://localhost:8080/health')
    assert response.status_code == 200
    assert response.json()['status'] == 'healthy'

def test_llamaindex_integration():
    """Test LlamaIndex integration in production"""
    # Test document processing
    # Test query processing
    # Test performance metrics
```

### 2. Load Testing
```bash
# Install locust
pip install locust

# Run load test
locust -f load_test.py --host=http://localhost:8080
```

## 🔄 Backup and Recovery

### 1. Database Backup
```bash
# Automated database backup
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump your_database > backup_$DATE.sql
gzip backup_$DATE.sql
```

### 2. Vector Database Backup
```python
# Backup ChromaDB
import shutil
import os

def backup_chromadb():
    """Backup ChromaDB data"""
    source = "data/unified_chroma"
    backup_dir = f"backups/chromadb_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    shutil.copytree(source, backup_dir)
    logger.info(f"ChromaDB backed up to {backup_dir}")
```

## 📋 Deployment Checklist

- [ ] Ollama models installed and tested
- [ ] Production environment configured
- [ ] Database initialized and migrated
- [ ] Application deployed with Gunicorn/uWSGI
- [ ] Reverse proxy configured (Nginx)
- [ ] SSL certificates installed
- [ ] Monitoring and logging configured
- [ ] Health checks implemented
- [ ] Security measures in place
- [ ] Backup procedures tested
- [ ] Load testing completed
- [ ] Documentation updated

## 🚨 Troubleshooting

### Common Issues

1. **Ollama Connection Issues**
   ```bash
   # Check Ollama service
   curl http://localhost:11434/api/tags
   
   # Restart Ollama
   pkill ollama
   ollama serve
   ```

2. **Memory Issues**
   ```python
   # Monitor memory usage
   import psutil
   memory = psutil.virtual_memory()
   if memory.percent > 90:
       logger.critical("Critical memory usage")
   ```

3. **Slow Query Performance**
   ```python
   # Optimize configuration
   update_llamaindex_config(
       similarity_top_k=3,
       chunk_size=800,
       response_mode="compact"
   )
   ```

## 📞 Support

For production support:
- Check logs: `tail -f logs/app.log`
- Monitor performance: Use the built-in performance monitoring
- Review configuration: `config/rag_extraction_config.py`
- Test integration: Run `python test_llamaindex_integration.py`

## 🎯 Next Steps

1. **Monitor Performance**: Use the built-in performance monitoring
2. **Optimize Configuration**: Adjust settings based on usage patterns
3. **Scale as Needed**: Add more workers or servers as load increases
4. **Update Regularly**: Keep dependencies and models updated
5. **Gather Feedback**: Monitor user experience and query quality

---

**Congratulations!** Your LlamaIndex + LangChain integration is now production-ready and deployed successfully! 🚀 
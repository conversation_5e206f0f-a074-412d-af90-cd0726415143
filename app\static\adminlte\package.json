{"name": "admin-lte", "description": "Responsive open source admin dashboard and control panel.", "version": "4.0.0-rc3", "type": "module", "license": "MIT", "author": "Colorlib <https://colorlib.com>", "main": "dist/js/adminlte.min.js", "scripts": {"start": "npm run dev", "dev": "npm-run-all --parallel watch docs-serve", "build": "npm-run-all clean compile", "css": "npm-run-all css-compile css-prefix css-rtl css-minify", "css-compile": "sass --style expanded --load-path=\"node_modules\" --source-map --embed-sources --no-error-css --quiet src/scss/:dist/css/", "css-rtl": "cross-env NODE_ENV=RTL postcss --config src/config/postcss.config.mjs --dir \"dist/css\" --ext \".rtl.css\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*.rtl.css\"", "css-lint": "stylelint \"src/scss/**/*.scss\" --cache --cache-location .cache/.stylelintcache --rd", "css-minify": "npm-run-all --aggregate-output --parallel css-minify-*", "css-minify-main": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*rtl*.css\"", "css-minify-rtl": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*rtl.css\" \"!dist/css/*.min.css\"", "css-prefix": "postcss --config src/config/postcss.config.mjs --replace \"dist/css/*.css\" \"!dist/css/*.rtl*.css\" \"!dist/css/*.min.css\"", "js": "npm-run-all js-compile js-minify", "js-compile": "rollup --config src/config/rollup.config.js --sourcemap", "js-lint": "eslint --format=stylish .", "js-minify": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/adminlte.js.map,includeSources,url=adminlte.min.js.map\" --output dist/js/adminlte.min.js dist/js/adminlte.js", "bundlewatch": "bundlewatch --config .bundlewatch.config.json", "lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "docs-compile": "npm run copy-assets && astro --config src/config/astro.config.mjs build", "docs-lint": "astro --config src/config/astro.config.mjs check", "docs-format": "prettier --write \"dist/**/*.html\"", "docs-serve": "npm run copy-assets && astro --config src/config/astro.config.mjs dev --open --port 3000", "copy-assets": "rimraf src/html/public && mkdir -p src/html/public && cp -r dist/css src/html/public/ && cp -r dist/js src/html/public/ && cp -r dist/assets src/html/public/", "flatten-build": "cp -r dist/html/* dist/ && rm -rf dist/html && rm -rf dist/.astro", "assets": "node src/config/assets.config.mjs", "lint": "npm-run-all --aggregate-output --continue-on-error --parallel js-lint css-lint docs-lint lockfile-lint", "clean": "<PERSON><PERSON><PERSON> dist", "compile": "npm-run-all css js assets copy-assets docs-compile docs-format flatten-build", "production": "npm-run-all clean lint compile bundlewatch", "watch": "concurrently \"npm:watch-*\"", "watch-css-main": "nodemon --watch src/scss/ --ext scss --exec \"npm-run-all css-lint css-compile copy-assets\"", "watch-css-dist": "nodemon --watch dist/css/ --ext css --ignore \"dist/css/*.rtl.*\" --exec \"npm run css-rtl\"", "watch-js-main": "nodemon --watch src/ts/ --ext ts --exec \"npm-run-all js-lint js-compile copy-assets\"", "watch-assets": "nodemon --watch src/assets/ --exec \"npm run assets\""}, "keywords": ["css", "sass", "responsive", "admin", "template", "theme", "framework", "control-panel", "dashboard"], "homepage": "https://adminlte.io", "style": "dist/css/adminlte.css", "sass": "src/scss/adminlte.scss", "repository": {"type": "git", "url": "git://github.com/ColorlibHQ/AdminLTE.git"}, "bugs": {"url": "https://github.com/ColorlibHQ/AdminLTE/issues"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.3.0", "@rollup/plugin-typescript": "^12.1.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "astro": "^5.10.0", "autoprefixer": "^10.4.21", "bootstrap": "^5.3.7", "bundlewatch": "^0.4.1", "clean-css-cli": "^5.6.3", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^9.30.0", "eslint-config-xo": "^0.47.0", "eslint-config-xo-typescript": "^8.0.1", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-unicorn": "^59.0.1", "fs-extra": "^11.3.0", "lockfile-lint": "^4.14.1", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "rimraf": "^6.0.1", "rollup": "^4.44.0", "rtlcss": "^4.3.0", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-twbs-bootstrap": "^16.1.0", "terser": "^5.43.1", "tslib": "^2.8.1", "typescript": "^5.8.3"}}
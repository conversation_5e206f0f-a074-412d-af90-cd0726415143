# [AdminLTE - Bootstrap 5 Admin Dashboard](https://adminlte.io)

[![npm version](https://img.shields.io/npm/v/admin-lte/latest.svg)](https://www.npmjs.com/package/admin-lte)
[![Packagist](https://img.shields.io/packagist/v/almasaeed2010/adminlte.svg)](https://packagist.org/packages/almasaeed2010/adminlte)
[![cdn version](https://data.jsdelivr.com/v1/package/npm/admin-lte/badge)](https://www.jsdelivr.com/package/npm/admin-lte)
[![Discord Invite](https://img.shields.io/badge/discord-join%20now-green)](https://discord.gg/jfdvjwFqfz)
[![Netlify Status](https://api.netlify.com/api/v1/badges/1277b36b-08f3-43fa-826a-4b4d24614b3c/deploy-status)](https://app.netlify.com/sites/adminlte-v4/deploys)

**AdminLTE** is a fully responsive administration template. Based on **[Bootstrap 5](https://getbootstrap.com/)** framework and also the JavaScript plugins.
Highly customizable and easy to use. Fits many screen resolutions from small mobile devices to large desktops.

## What's New in v4.0.0-rc3

**Production Deployment & Cross-Platform Compatibility** - This release resolves critical production deployment issues:

- **Fixed Production Builds** - Resolved CSS/JS path issues, sidebar navigation, and image loading in all deployment scenarios
- **Smart Path Resolution** - Automatic relative path calculation works for root deployment, sub-folders, and CDN hosting  
- **RTL CSS Fix** - Eliminated rtlcss interference with standard LTR production builds
- **Updated Dependencies** - Bootstrap 5.3.7, Bootstrap Icons 1.13.1, OverlayScrollbars 2.11.0
- **Zero Console Errors** - Fixed all CDN integrity mismatches and runtime issues
- **FTP/Static Host Ready** - Perfect compatibility with traditional hosting and modern static platforms

**Key Improvements:**
- ✅ Development and production environments now behave identically
- ✅ Images, CSS, and JavaScript load correctly in any deployment structure  
- ✅ Sidebar navigation displays properly with badges and arrow indicators
- ✅ All CDN resources load without console errors
- ✅ Complete production build included in repository for easy deployment

See the [CHANGELOG.md](CHANGELOG.md) for complete details.

## Looking for Premium Templates?

AdminLTE.io just opened a new premium templates page. Hand picked to ensure the best quality and the most affordable
prices. Visit <https://adminlte.io/premium> for more information.

!["AdminLTE Presentation"](https://adminlte.io/AdminLTE3.png "AdminLTE Presentation")

**AdminLTE** has been carefully coded with clear comments in all of its JS, SCSS and HTML files.
SCSS has been used to increase code customizability.

## Quick start

### Development

To start developing with AdminLTE:

1. **Install dependencies:** `npm install`
2. **Start development server:** `npm start` *(opens browser at http://localhost:3000)*
3. **Start coding!** Files auto-compile and refresh on changes

### Production Build

To build for production:

1. **Full production build:** `npm run production` *(includes linting and optimization)*
2. **Quick build:** `npm run build` *(faster for development/testing)*

### Available Scripts

- `npm start` - Start development server with file watching
- `npm run build` - Build all assets for development
- `npm run production` - Full production build with linting and bundlewatch
- `npm run lint` - Run all linters (JS, CSS, docs, lockfile)
- `npm run css` - Build CSS only
- `npm run js` - Build JavaScript only

## Browser Support

AdminLTE supports all modern browsers with the latest Bootstrap 5.3.7:
- Chrome (latest)
- Firefox (latest) 
- Safari (latest)
- Edge (latest)

## Contributing

- Highly welcome.
- For your extra reference check [AdminLTE v4 Contribution Guide](https://github.com/ColorlibHQ/AdminLTE#contributing)
- First thing first, you should have bit knowledge about NodeJS.
- Github Knowledge.
- Install NodeJS LTS version.
- Clone this Repository to your machine and change to `master` branch.
- Go to Cloned Folder.
- In cli/bash run `npm install` it will install dependency from `package.json`.
- After installation completes, run `npm start`
- Cool, Send your changes in PR to `master` branch.

## Sponsorship

Support AdminLTE development by becoming a sponsor.
[Github Sponsors](https://github.com/sponsors/danny007in) or
[PayPal](https://www.paypal.me/daniel007in)

## License

AdminLTE is an open source project by [AdminLTE.io](https://adminlte.io) that is licensed under [MIT](https://opensource.org/licenses/MIT).
AdminLTE.io reserves the right to change the license of future releases.

## Image Credits

- [Pixeden](http://www.pixeden.com/psd-web-elements/flat-responsive-showcase-psd)
- [Graphicsfuel](https://www.graphicsfuel.com/2013/02/13-high-resolution-blur-backgrounds/)
- [Pickaface](https://pickaface.net/)
- [Unsplash](https://unsplash.com/)
- [Uifaces](http://uifaces.com/)

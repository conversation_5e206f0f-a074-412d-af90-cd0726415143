from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.services.vector_db import get_vector_db
from app.services.llamaindex_vector_db import get_llamaindex_vector_db
import logging
import os
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_core.documents import Document
import numpy as np
from typing import List, Dict, Any, Tuple
# Import the vision processor module
from app.services import vision_processor
# Import the query configuration system
from config.settings.query_config import get_query_config, QueryConfiguration
from app.utils.content_db import get_source_url_by_id
from app.services.vector_db import similarity_search_with_category_filter
from app.utils.helpers import list_categories
# COMMENTED OUT: No longer using backend scientific name formatting
# from app.utils.validate_and_italicize_scientific_names import validate_and_italicize_scientific_names
from app.utils.config import DEFAULT_MODELS_FILE

# Import performance monitoring decorators
from app.utils.performance_monitor import (
    performance_monitor,
    get_performance_monitor
)
from app.utils.chroma_performance import monitor_similarity_search

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def score_document_relevance(doc: Document, question: str) -> float:
    """
    Score the relevance of a document to a question.
    Higher score means more relevant.

    Args:
        doc: The document to score
        question: The question to compare against

    Returns:
        float: A relevance score between 0 and 1
    """
    # Simple keyword matching for now
    # Count how many words from the question appear in the document
    question_words = set(question.lower().split())
    # Remove common stop words
    stop_words = {'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'through', 'over', 'before', 'between', 'after', 'since', 'without', 'under', 'within', 'along', 'following', 'across', 'behind', 'beyond', 'plus', 'except', 'but', 'up', 'out', 'around', 'down', 'off', 'above', 'near', 'and', 'or', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will', 'would', 'shall', 'should', 'may', 'might', 'must', 'of'}
    question_words = question_words - stop_words

    content_lower = doc.page_content.lower()

    # Count occurrences of question words in the document
    word_matches = sum(1 for word in question_words if word in content_lower)

    # Calculate basic relevance score
    if len(question_words) > 0:
        basic_score = word_matches / len(question_words)
    else:
        basic_score = 0

    # Boost score if document contains exact phrases from the question
    # This helps with multi-word concepts
    phrase_boost = 0
    for i in range(2, min(5, len(question.split()) + 1)):  # Check phrases of length 2-4 words
        for j in range(len(question.split()) - i + 1):
            phrase = ' '.join(question.split()[j:j+i]).lower()
            if len(phrase) > 3 and phrase in content_lower:  # Only consider phrases longer than 3 chars
                phrase_boost += 0.1 * i  # Longer matching phrases get higher boost

    # Calculate final score, capped at 1.0
    final_score = min(1.0, basic_score + phrase_boost)

    # Log the score for debugging
    logger.debug(f"Document relevance score: {final_score:.2f} for doc: {doc.metadata.get('source', 'Unknown')[:30]}...")

    return final_score

def filter_relevant_documents(docs: List[Document], question: str, threshold: float = 0.2) -> List[Tuple[Document, float]]:
    """
    Filter and sort documents by relevance to the question.

    Args:
        docs: List of documents to filter
        question: The question to compare against
        threshold: Minimum relevance score to keep a document

    Returns:
        List of (document, score) tuples, sorted by relevance (highest first)
    """
    # Score each document
    scored_docs = [(doc, score_document_relevance(doc, question)) for doc in docs]

    # Filter by threshold and sort by score (descending)
    relevant_docs = [(doc, score) for doc, score in scored_docs if score >= threshold]
    relevant_docs.sort(key=lambda x: x[1], reverse=True)

    # Log the filtering results
    logger.info(f"Filtered documents: {len(relevant_docs)}/{len(docs)} passed threshold {threshold}")
    for i, (doc, score) in enumerate(relevant_docs[:5]):  # Log top 5 for debugging
        logger.info(f"Top doc {i+1}: score={score:.2f}, source={doc.metadata.get('source', 'Unknown')}")

    return relevant_docs

def extract_images_and_links(text, base_url=None):
    """Extract image URLs and download links from text content."""
    images = []
    pdf_links = []

    # Extract JPG image URLs
    jpg_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:jpg|jpeg|JPG|JPEG)')
    jpg_img_matches = jpg_img_pattern.findall(text)

    # Extract PNG image URLs (excluding those with "logo" in the filename)
    png_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:png|PNG)')
    png_img_matches = [url for url in png_img_pattern.findall(text) if 'logo' not in url.lower()]

    # Combine JPG and filtered PNG images
    priority_img_matches = jpg_img_matches + png_img_matches

    # Extract other image URLs as backup
    other_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:gif|bmp|GIF|BMP)')
    other_img_matches = other_img_pattern.findall(text)

    # Combine with priority images first
    img_matches = priority_img_matches + other_img_matches

    # Extract PDF/document download links
    pdf_pattern = re.compile(r'https?://[^\s<>"]+\.(?:pdf|doc|docx|xls|xlsx|ppt|pptx|PDF|DOC|DOCX|XLS|XLSX|PPT|PPTX)')
    pdf_matches = pdf_pattern.findall(text)

    # Extract links with "download" or "request" in them
    download_pattern = re.compile(r'https?://[^\s<>"]+(?:download|request|copy)[^\s<>"]*')
    download_matches = download_pattern.findall(text)

    # Combine and deduplicate
    images = list(set(img_matches))
    pdf_links = list(set(pdf_matches + download_matches))

    # If base_url is provided, try to scrape the page for additional images and links
    if base_url and base_url.startswith('http'):
        try:
            response = requests.get(base_url, timeout=10, verify=False)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Find images - prioritize JPG and PNG images
                priority_images = []
                other_images = []
                for img in soup.find_all('img', src=True):
                    img_src = img['src'] if img and 'src' in img.attrs else ''
                    if not img_src:
                        continue
                    
                    img_url = str(img_src)
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = urljoin(base_url, img_url)

                    # Separate JPG/PNG images from other formats
                    if img_url.lower().endswith(('.jpg', '.jpeg')):
                        priority_images.append(img_url)
                    elif img_url.lower().endswith('.png'):
                        # Skip PNG files with "logo" in the filename (case insensitive)
                        if 'logo' not in img_url.lower():
                            priority_images.append(img_url)
                    elif img_url.lower().endswith(('.gif', '.bmp')):
                        other_images.append(img_url)

                # Add priority images first, then others if needed
                images.extend(priority_images)
                images.extend(other_images)

                # Find download links
                for a in soup.find_all('a', href=True):
                    href = a['href'] if a and 'href' in a.attrs else ''
                    if not href or not isinstance(href, str):
                        continue
                    if not href.startswith(('http://', 'https://')):
                        href = urljoin(base_url, href)
                    if href.endswith(('.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx')):
                        pdf_links.append(href)
                    elif any(keyword in href.lower() for keyword in ['download', 'request', 'copy']):
                        pdf_links.append(href)
        except Exception as e:
            logger.warning(f"Failed to scrape additional content from {base_url}: {str(e)}")

    # Deduplicate again
    images = list(set(images))
    pdf_links = list(set(pdf_links))

    return images, pdf_links

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def query_category(category: str, question: str, anti_hallucination_mode: str = 'strict', client_name: str = None, session_id: str = None, device_fingerprint: str = None, selected_model: str = None):
    try:
        # Validate category parameter
        if not category or not category.strip():
            raise ValueError("Category parameter is required and cannot be empty")
        
        category = category.strip()
        logger.info(f"Processing query for category: '{category}'")
        
        # Check if category exists in available categories
        available_categories = list_categories()
        if category not in available_categories:
            logger.warning(f"Category '{category}' not found in available categories: {available_categories}")
            # Don't raise an error here, just log a warning as the category might exist in the vector DB
            # even if not in the file system categories
        
        # Start timing for analytics
        import time
        start_time = time.time()

        # Load query configuration
        config = get_query_config()
        logger.info(f"Loaded query configuration: retrieval_k={config.retrieval_k}, relevance_threshold={config.relevance_threshold}")

        # Initialize LLM with the selected model from request or environment variable
        model_name = selected_model if selected_model else os.getenv('LLM_MODEL', 'llama3.1:8b-instruct-q4_K_M')
        embedding_model = os.getenv('TEXT_EMBEDDING_MODEL', 'mxbai-embed-large:latest')

        logger.info(f"Using LLM model: {model_name} (selected: {selected_model is not None})")

        # Load mode-specific model parameters from config file
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)
            model_params_all = defaults.get('model_parameters', {})
            # Fallback to 'balanced' if mode not found
            mode_key = anti_hallucination_mode if anti_hallucination_mode in model_params_all else 'balanced'
            model_params = model_params_all.get(mode_key, model_params_all.get('balanced', {}))
        else:
            model_params = {}

        temperature = float(model_params.get('temperature', 0.7))
        num_ctx = int(model_params.get('num_ctx', 4096))
        num_predict = int(model_params.get('num_predict', 256))
        top_p = float(model_params.get('top_p', 0.9))
        top_k = int(model_params.get('top_k', 40))
        repeat_penalty = float(model_params.get('repeat_penalty', 1.1))
        system_prompt = model_params.get('system_prompt', 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.')

        logger.info(f"Using LLM model: {model_name}, Embedding model: {embedding_model}, Anti-hallucination mode: {anti_hallucination_mode}")
        logger.info(f"Model parameters: temperature={temperature}, num_ctx={num_ctx}, num_predict={num_predict}, top_p={top_p}, top_k={top_k}, repeat_penalty={repeat_penalty}")

        try:
            # Initialize ChatOllama with model parameters
            llm = ChatOllama(
                model=model_name,
                temperature=temperature,
                num_ctx=num_ctx,
                num_predict=num_predict,
                top_p=top_p,
                top_k=top_k,
                repeat_penalty=repeat_penalty,
                system=system_prompt
            )
            logger.info(f"Successfully initialized ChatOllama with model: {model_name} and custom parameters")
        except Exception as model_error:
            logger.error(f"Failed to initialize ChatOllama with model {model_name}: {str(model_error)}")
            # Fall back to a different model if the specified one fails
            fallback_model = 'llama3.1:8b-instruct-q4_K_M'
            if model_name != fallback_model:
                logger.info(f"Trying fallback model: {fallback_model}")
                try:
                    # Initialize fallback model with the same parameters
                    llm = ChatOllama(
                        model=fallback_model,
                        temperature=temperature,
                        num_ctx=num_ctx,
                        num_predict=num_predict,
                        top_p=top_p,
                        top_k=top_k,
                        repeat_penalty=repeat_penalty,
                        system=system_prompt
                    )
                    logger.info(f"Successfully initialized ChatOllama with fallback model: {fallback_model} and custom parameters")
                    # Update model name for analytics
                    model_name = fallback_model
                except Exception as fallback_error:
                    logger.error(f"Failed to initialize fallback model {fallback_model}: {str(fallback_error)}")
                    raise ValueError(f"Could not initialize any LLM model. Original error: {str(model_error)}, Fallback error: {str(fallback_error)}")
            else:
                raise

        # Get vector database - try LlamaIndex first, fallback to LangChain
        use_llamaindex = os.getenv('USE_LLAMAINDEX_FOR_QUERIES', 'true').lower() == 'true'
        
        if use_llamaindex:
            try:
                # Use LlamaIndex vector database
                llamaindex_db = get_llamaindex_vector_db()
                docs_with_scores = llamaindex_db.similarity_search_with_score(
                    query=question,
                    category=category,
                    k=config.retrieval_k
                )
                # Convert to LangChain Document format for compatibility
                docs = [doc for doc, score in docs_with_scores]
                logger.info(f"Retrieved {len(docs)} documents using LlamaIndex for category: {category}")
            except Exception as e:
                logger.warning(f"LlamaIndex retrieval failed, falling back to LangChain: {str(e)}")
                # Fallback to LangChain
                db = get_vector_db(category)
                docs = similarity_search_with_category_filter(
                    query=question,
                    category=category,
                    k=config.retrieval_k
                )
        else:
            # Use traditional LangChain approach
            db = get_vector_db(category)
            docs = similarity_search_with_category_filter(
                query=question,
                category=category,
                k=config.retrieval_k
            )

        # Log retrieved documents for debugging
        logger.info(f"Retrieved {len(docs)} documents for category {category}, question: {question}")
        
        # Additional validation: Double-check that all documents are from the correct category
        incorrect_category_docs = []
        for i, doc in enumerate(docs):
            doc_category = doc.metadata.get('category', 'Unknown')
            if doc_category != category:
                incorrect_category_docs.append({
                    'index': i,
                    'expected_category': category,
                    'actual_category': doc_category,
                    'source': doc.metadata.get('source', 'Unknown')
                })
                logger.warning(f"Document {i} has incorrect category: expected '{category}', got '{doc_category}'")
        
        if incorrect_category_docs:
            logger.error(f"Found {len(incorrect_category_docs)} documents with incorrect category: {incorrect_category_docs}")
            # Filter out documents with incorrect category
            docs = [doc for doc in docs if doc.metadata.get('category', 'Unknown') == category]
            logger.info(f"After filtering, {len(docs)} documents remain for category {category}")
        
        # Log category information for each retrieved document
        for i, doc in enumerate(docs):
            doc_category = doc.metadata.get('category', 'Unknown')
            logger.info(f"Document {i}: category='{doc_category}', source='{doc.metadata.get('source', 'Unknown')}'")

        # Check if we have any documents for this category
        if not docs:
            logger.warning(f"No documents found for category '{category}' and query: {question}")
            # Compose the plain answer
            plain_answer = f"I don't have any information in the {category} category to answer your question. Please try a different category or rephrase your question."
            # Remove any links/citations just in case
            plain_answer = re.sub(r'<a [^>]+>(.*?)</a>', r'\1', plain_answer)
            plain_answer = re.sub(r'According to [^\n]+', '', plain_answer)
            return {
                "answer": plain_answer,
                "sources": [],
                "processing_time": 0,
                "category": category,
                "anti_hallucination_mode": anti_hallucination_mode
            }

        # Filter and score documents by relevance using configured threshold
        scored_docs = filter_relevant_documents(docs, question, threshold=config.relevance_threshold)

        # Use only the most relevant documents using configured limits
        min_docs = min(config.min_documents, len(scored_docs))
        max_docs = min(config.max_documents, len(scored_docs))

        # Dynamically adjust the number of documents based on their scores
        # Keep all documents with high scores (>0.5) plus at least min_docs
        high_quality_docs = [(doc, score) for doc, score in scored_docs if score > 0.5]
        if len(high_quality_docs) >= min_docs:
            filtered_docs = high_quality_docs[:max_docs]
        else:
            filtered_docs = scored_docs[:max_docs]

        # Extract just the documents from the scored tuples
        docs = [doc for doc, _ in filtered_docs]

        # Add relevance scores to document metadata for use in the prompt
        for i, (doc, score) in enumerate(filtered_docs):
            doc.metadata["relevance_score"] = f"{score:.2f}"

        logger.info(f"Using {len(docs)} most relevant documents for answering")

        # Enrich document metadata by resolving source_url_id to actual URLs
        docs = enrich_document_metadata(docs)

        # Extract sources, images, and links
        sources = []
        all_images = []
        all_pdf_links = []
        document_thumbnails = []  # Store document thumbnails
        cover_image_added = False

        for i, doc in enumerate(docs):
            logger.info(f"Document {i+1}: source={doc.metadata.get('source')}, type={doc.metadata.get('type')}, content={doc.page_content[:100]}...")

            # Extract images and links from document content
            content_images, content_links = extract_images_and_links(doc.page_content)

            # Get metadata images and links
            metadata_images = []
            metadata_links = []
            try:
                # Safely handle 'images' metadata
                if doc.metadata.get('images'):
                    images_data = doc.metadata.get('images')
                    if isinstance(images_data, str):
                        try:
                            metadata_images = json.loads(images_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to deserialize images metadata: {str(e)}")
                    elif isinstance(images_data, list):
                        metadata_images = images_data
                    else:
                        logger.warning(f"Unexpected images metadata type: {type(images_data)}")

                # Safely handle 'pdf_links' metadata
                if doc.metadata.get('pdf_links'):
                    links_data = doc.metadata.get('pdf_links')
                    if isinstance(links_data, str):
                        try:
                            metadata_links = json.loads(links_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to deserialize pdf_links metadata: {str(e)}")
                    elif isinstance(links_data, list):
                        metadata_links = links_data
                    else:
                        logger.warning(f"Unexpected pdf_links metadata type: {type(links_data)}")
            except Exception as e:
                logger.warning(f"Error processing metadata: {str(e)}")

            # Combine all found images and links
            # Make sure we're only working with strings for deduplication
            content_images_str = [str(img) for img in content_images if img]
            metadata_images_str = [str(img) for img in metadata_images if img]
            content_links_str = [str(link) for link in content_links if link]
            metadata_links_str = [str(link) for link in metadata_links if link]

            # Combine and deduplicate
            doc_images = list(set(content_images_str + metadata_images_str))
            doc_links = list(set(content_links_str + metadata_links_str))

            # If document is from a URL, try to extract more content
            original_url = None
            if doc.metadata.get('type') == 'url' or doc.metadata.get('original_url'):
                original_url = doc.metadata.get('original_url')
                if original_url:
                    url_images, url_links = extract_images_and_links(doc.page_content, original_url)
                    # Convert to strings for deduplication
                    url_images_str = [str(img) for img in url_images if img]
                    url_links_str = [str(link) for link in url_links if link]
                    doc_images.extend(url_images_str)
                    doc_links.extend(url_links_str)

            # Deduplicate
            doc_images = list(set(doc_images))
            doc_links = list(set(doc_links))

            # Add to global lists
            all_images.extend(doc_images)
            all_pdf_links.extend(doc_links)

            # Check for extracted PDF images and tables in metadata
            pdf_images = []
            pdf_tables = []

            try:
                # Extract PDF images from metadata
                if doc.metadata.get('images'):
                    images_data = doc.metadata.get('images')
                    if isinstance(images_data, str):
                        try:
                            images_data = json.loads(images_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse images metadata JSON: {str(e)}")
                            images_data = []

                    if isinstance(images_data, list):
                        for img in images_data:
                            if isinstance(img, dict) and img.get('url'):
                                # This is a processed image with a URL
                                pdf_images.append(img)
                    else:
                        logger.warning(f"Unexpected images_data type: {type(images_data)}")

                # Extract PDF tables from metadata
                if doc.metadata.get('tables'):
                    tables_data = doc.metadata.get('tables')
                    if isinstance(tables_data, str):
                        try:
                            tables_data = json.loads(tables_data)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse tables metadata JSON: {str(e)}")
                            tables_data = []

                    if isinstance(tables_data, list):
                        for table in tables_data:
                            if isinstance(table, dict) and table.get('url'):
                                # This is a processed table with a URL
                                pdf_tables.append(table)
                    else:
                        logger.warning(f"Unexpected tables_data type: {type(tables_data)}")
            except Exception as e:
                logger.warning(f"Error processing PDF images/tables metadata: {str(e)}")

            # Extract thumbnail information if available
            thumbnail_info = None
            if doc.metadata.get("thumbnail_url") and doc.metadata.get("thumbnail_source"):
                # Get the document name for display and citation
                document_name = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", "Unknown"))

                # Get the thumbnail URL and ensure it starts with a single slash
                thumbnail_url = doc.metadata.get("thumbnail_url", "")
                if thumbnail_url and not thumbnail_url.startswith(('http://', 'https://')):
                    # Make sure the URL starts with a single slash
                    thumbnail_url = '/' + thumbnail_url.lstrip('/')

                thumbnail_info = {
                    "url": thumbnail_url,
                    "source": doc.metadata.get("thumbnail_source"),
                    "path": doc.metadata.get("thumbnail_path", ""),
                    "description": doc.metadata.get("thumbnail_description", f"Thumbnail for {document_name}"),
                    "document_name": document_name,
                    "original_url": original_url
                }

                # Log thumbnail information for debugging
                logger.debug(f"Found thumbnail for {document_name}: {thumbnail_url} (source: {doc.metadata.get('thumbnail_source')})")

                # Add to document_thumbnails if not already present
                if not any(t.get("url") == thumbnail_info["url"] for t in document_thumbnails):
                    document_thumbnails.append(thumbnail_info)
                    logger.info(f"Added thumbnail for {document_name} to document_thumbnails")
                    # If this is a cover image, mark it
                    if "/cover_image/" in thumbnail_url:
                        cover_image_added = True

        # If no cover image was added, but one exists in any doc, add it now
        if not cover_image_added:
            for doc in docs:
                thumb_url = doc.metadata.get("thumbnail_url", "")
                if "/cover_image/" in thumb_url:
                    document_name = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", "Unknown"))
                    thumbnail_url = thumb_url
                    if thumbnail_url and not thumbnail_url.startswith(('http://', 'https://')):
                        thumbnail_url = '/' + thumbnail_url.lstrip('/')
                    thumbnail_info = {
                        "url": thumbnail_url,
                        "source": doc.metadata.get("thumbnail_source"),
                        "path": doc.metadata.get("thumbnail_path", ""),
                        "description": doc.metadata.get("thumbnail_description", f"Thumbnail for {document_name}"),
                        "document_name": document_name,
                        "original_url": doc.metadata.get("original_url")
                    }
                    if not any(t.get("url") == thumbnail_info["url"] for t in document_thumbnails):
                        document_thumbnails.insert(0, thumbnail_info)  # Insert at the start
                        logger.info(f"Force-added cover image thumbnail for {document_name} to document_thumbnails")
                    break

        # Create source object - store only string references to images and links
        # to avoid unhashable type errors when serializing to JSON
        for i, doc in enumerate(docs):
            # Publication date fields (ensure no None values)
            pub_year = doc.metadata.get("published_year")
            pub_month_start = doc.metadata.get("published_month_start")
            pub_month_end = doc.metadata.get("published_month_end")
            pub_month_range_str = doc.metadata.get("published_month_range_str")
            file_source = doc.metadata.get('source', '')
            if file_source is None:
                file_source = ''
            source = {
                "source": doc.metadata.get("source", "Unknown"),
                "display_name": doc.metadata.get("original_filename", doc.metadata.get("source", "Unknown")),
                "page": doc.metadata.get("page", None),
                "type": doc.metadata.get("type", "Unknown"),
                "image_count": len(doc_images) + len(pdf_images),
                "link_count": len(doc_links),
                "table_count": len(pdf_tables),
                "original_url": original_url,
                "file_path": f"/download_gated/{file_source}" if doc.metadata.get("type") == "pdf" else None,
                "pdf_images": pdf_images if pdf_images else None,
                "pdf_tables": pdf_tables if pdf_tables else None,
                "thumbnail": document_thumbnails[i] if i < len(document_thumbnails) else None,
                "published_year": str(pub_year) if pub_year is not None else '',
                "published_month_start": str(pub_month_start) if pub_month_start is not None else '',
                "published_month_end": str(pub_month_end) if pub_month_end is not None else '',
                "published_month_range_str": str(pub_month_range_str) if pub_month_range_str is not None else ''
            }
            sources.append(source)

        # Deduplicate global lists - ensure all items are strings
        # We've already converted everything to strings earlier, but let's be extra safe
        all_images = list(set([str(img) for img in all_images if img]))
        all_pdf_links = list(set([str(link) for link in all_pdf_links if link]))

        # Prepare context with source attribution and relevance information for better citations
        context_parts = []
        for i, doc in enumerate(docs):
            source_name = doc.metadata.get("source", f"Source {i+1}")
            original_filename = doc.metadata.get("original_filename", source_name)
            # Use citation_filename if available, otherwise fall back to original_filename
            citation_filename = doc.metadata.get("citation_filename", original_filename)
            source_type = doc.metadata.get("type", "Unknown")
            page_num = doc.metadata.get("page", None)
            original_url = doc.metadata.get("original_url", None)
            relevance_score = doc.metadata.get("relevance_score", "N/A")

            # SIMPLIFIED: Always use gated download link for all PDFs
            # Use the actual (timestamped) filename for file_path (for href), and original filename for display
            file_path = f"/download_gated/{source_name}" if source_type == "pdf" else None
            display_name = doc.metadata.get("original_filename", doc.metadata.get("source", "Unknown"))
            
            # DEBUG: Log filename generation for troubleshooting
            if source_type == "pdf":
                logger.info(f"[File Path Generation] Source: '{source_name}' -> File Path: '{file_path}'")
                logger.info(f"[File Path Generation] Original filename: '{original_filename}', Citation filename: '{citation_filename}'")
                logger.info(f"[File Path Generation] Display name: '{display_name}'")
                
                # Validate that the source_name looks correct (has underscores)
                if '_' not in source_name:
                    logger.warning(f"[File Path Generation] SUSPICIOUS: Source name lacks underscores: '{source_name}'")
                
                # Check if the source_name matches expected pattern
                if not re.match(r'^\d{14}_[A-Z]+_.*\.pdf$', source_name):
                    logger.warning(f"[File Path Generation] MALFORMED: Source name doesn't match expected pattern: '{source_name}'")
                    logger.warning(f"[File Path Generation] Expected pattern: YYYYMMDDHHMMSS_CATEGORY_version.pdf")

            # Build source info with all metadata needed for proper citation
            # Use citation_filename as the primary identifier instead of "Source X" or timestamped filename
            source_info = f"Document: {citation_filename}"
            source_info += f"\nInternal ID: {source_name}"
            source_info += f"\nCitation Filename (USE THIS IN CITATIONS): {citation_filename}"
            source_info += f"\nRelevance Score: {relevance_score}"  # Add relevance score to help model assess source quality
            if page_num:
                source_info += f"\nPage: {page_num}"
            if source_type:
                source_info += f"\nType: {source_type}"

            # Only add original URL for non-PDF sources
            if source_type != 'pdf' and original_url:
                source_info += f"\nOriginal URL: {original_url}"

            if file_path:
                source_info += f"\nFile Path (USE THIS FOR CITATION LINKS): {file_path}"

            # Add a clear separator between metadata and content
            context_parts.append(f"{source_info}\n\n--- DOCUMENT CONTENT ---\n{doc.page_content}\n--- END DOCUMENT CONTENT ---")

        # Get preamble from environment variable or use default
        preamble = os.getenv('QUERY_PREAMBLE', f"""
        CONTEXT INFORMATION:
        - ALL documents below are from the "{category}" category ONLY
        - You are FORBIDDEN from using any information outside of these "{category}" category documents
        - The following documents have been retrieved based on their relevance to your question
        - Each document has a relevance score (0-1) indicating how closely it matches your question
        - Higher relevance scores indicate more reliable sources for answering the question
        - Focus primarily on documents with higher relevance scores
        - If no document directly addresses the question, acknowledge the information gap
        - When citing sources, use File Path for href (includes timestamp) but Citation Filename for display text (no timestamp)
          (e.g., href='/download_gated/20250515085808_canopy_vol45n1.pdf' but display 'canopy_vol45n1.pdf')
        - Citations MUST be in HTML format: "According to <a href='[File Path]' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:underline'>[Citation Filename] (Page X)</a>"
        - ALL PDFs use the gated download link format with full timestamped filename in href
        - NEVER use "Source X:" prefix in citations
        - REMEMBER: You can ONLY use information from the "{category}" category documents provided below
        """)

        # Combine preamble and document contexts
        context = preamble + "\n\n" + "\n\n".join(context_parts)

        # Add explicit instruction for LLM to use file_path for href but citation_filename for display text
        context += "\n\nIMPORTANT: For all PDF citations, use the 'File Path' for href attribute (includes timestamp) but 'Citation Filename' for display text (no timestamp). Format: <a href=\"[File Path]\">[Citation Filename]</a>. Never prepend domain/host to file_path."
        
        # CRITICAL: Add stronger filename validation instructions with concrete examples
        context += "\n\nCRITICAL FILENAME RULES:"
        context += "\n- NEVER modify, remove, or add characters to the File Path URLs"
        context += "\n- Use EXACT File Path from context (e.g., /download_gated/20250706153939_canopy_vol45n1.pdf)"
        context += "\n- DO NOT create new filenames or remove underscores"
        context += "\n- DO NOT change timestamps or category names"
        context += "\n- If File Path is malformed, use it EXACTLY as provided - do not fix it"
        context += "\n- File paths MUST include all underscores and timestamps"
        context += f"\n- All PDFs in {category} category use format: /download_gated/YYYYMMDDHHMMSS_CATEGORY_version.pdf"
        
        # Add concrete examples of what the LLM should produce
        context += "\n\nCORRECT CITATION EXAMPLES:"
        for i, doc in enumerate(docs[:2]):  # Show examples for first 2 documents
            if doc.metadata.get("type") == "pdf":
                source_name = doc.metadata.get("source", "")
                citation_filename = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", ""))
                file_path = f"/download_gated/{source_name}"
                context += f"\n✅ CORRECT: According to <a href=\"{file_path}\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-600 hover:underline\">{citation_filename}</a>"
                context += f"\n❌ WRONG: According to <a href=\"/download_gated/{citation_filename}\" target=\"_blank\">... (missing timestamp)"
                context += f"\n❌ WRONG: According to <a href=\"/download_gated/{source_name.replace('_', '')}\" target=\"_blank\">... (removed underscores)"
                break
        
        # Add specific warning about the malformed pattern we've been seeing
        context += "\n\nSPECIFIC WARNING:"
        context += f"\n- DO NOT generate links like /download_gated/20250706153939{category.lower()}vol45n1.pdf"
        context += f"\n- ALWAYS use the exact File Path provided: /download_gated/20250706153939_{category}_vol45n1.pdf"
        context += "\n- The File Path MUST include underscores and the exact timestamp"

        # Get anti-hallucination mode from environment variable or use parameter
        default_mode = os.getenv('ANTI_HALLUCINATION_MODE', 'strict')
        if anti_hallucination_mode == 'default':
            anti_hallucination_mode = default_mode

        # Get custom anti-hallucination instructions if available
        custom_instructions = os.getenv('ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS', '')

        # Try to load prompt templates from environment variable first, then fallback to config file
        try:
            prompt_templates = json.loads(os.getenv('PROMPT_TEMPLATES', '{}'))
        except json.JSONDecodeError:
            prompt_templates = {}
        
        # If no templates found in environment, load from default_models.json
        if not prompt_templates:
            try:
                if os.path.exists(DEFAULT_MODELS_FILE):
                    with open(DEFAULT_MODELS_FILE, 'r') as f:
                        defaults = json.load(f)
                    prompt_templates = defaults.get('query_parameters', {}).get('prompt_templates', {})
                    logger.info(f"Loaded prompt templates from {DEFAULT_MODELS_FILE}")
                else:
                    logger.warning(f"Default models file not found: {DEFAULT_MODELS_FILE}")
            except Exception as e:
                logger.error(f"Failed to load prompt templates from config file: {str(e)}")
                prompt_templates = {}

        # Define prompt based on anti-hallucination mode
        if anti_hallucination_mode == 'strict':
            # Use template from config if available, otherwise use default
            if prompt_templates and 'strict' in prompt_templates:
                prompt_template = prompt_templates['strict']
            else:
                # Balanced mode – Some reasonable inferences allowed
                prompt_template = f"""You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). You are answering a question based on documents from the "{category}" category.

                CRITICAL RESTRICTIONS:

                1. You MUST base your answers primarily on the provided context and uploaded PDFs from the "{category}" category.
                2. You may make **reasonable inferences** only when the context provides sufficient grounds for them.
                3. You MUST NOT reference information from other categories or outside sources.
                4. You are FORBIDDEN from using general knowledge (e.g., about ERDB, agriculture, or forestry) unless explicitly present in the provided context.
                5. If the context does not contain enough information to answer the question, respond exactly with:  
                   `"I don't have enough information in the provided context from the {category} category to answer this question."`
                6. You MUST NOT refer to or imply the existence of any information beyond the given context and category.
                7. If the context is **empty or insufficient**, DO NOT include any links, citations, or references—just the plain statement above.
                8. If relevant context IS found, you MUST cite your sources using the formatting rules below.

                ANTI-HALLUCINATION GUIDELINES (BALANCED MODE):

                1. Inferences are allowed **only** if grounded in the context and clearly labeled as such.
                2. Always separate factual claims from inferred ones.
                3. If unsure about any detail, state your uncertainty.
                4. Prioritize directly supported information from the context.
                5. Cite specific parts of the context for any direct claim.
                6. You are ONLY working with documents from the "{category}" category.
                7. FINAL REMINDER: If the context is empty or insufficient, do not include any links or citations. If context is found, cite all factual claims appropriately.

                FORMATTING GUIDELINES:

                1. Use bullet points, **bold text**, and markdown for readability where appropriate.
                2. **Citation rules** (apply only if context is present):
                   - Use only the `File Path` from the context for PDF links.
                   - DO NOT use the `Original URL` or any other path for PDFs.
                   - Format:  
                     `According to <a href="/download_gated/filename.pdf" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">filename.pdf (Page X)</a>`
                   - Use File Path for href (includes timestamp) but Citation Filename for display text (no timestamp).  
                     ✅ href="/download_gated/20250515085808_canopy_vol45n1.pdf" display="canopy_vol45n1.pdf"
                   - For non-PDFs (like links), you may use the `Original URL`.
                   - All links must include: `target="_blank"` and `rel="noopener noreferrer"`
                3. If multiple sources are relevant, synthesize and reference each accordingly.
                4. Mention images or downloadable files if referenced in the context.
                5. DO NOT prefix citations with "Source X:" or "Document:" — only use the filename.
                6. When making inferences, use phrases like:  
                   _"Based on the context from the {category} category..."_
                7. FINAL REMINDER: Never add citations if the context is missing or insufficient.

                SCIENTIFIC NAME FORMATTING:

                1. Automatically detect and format all scientific names (binomial nomenclature) using markdown italics.
                2. Include genus and species names (e.g., *Pterocarpus indicus*, *Homo sapiens*).
                3. Include subspecies in italics when present (e.g., *Homo sapiens sapiens*).
                4. Keep author citations and publication years in regular text (e.g., *Escherichia coli* (Migula 1895)).
                5. Watch for these common Philippine species:
                   *Pterocarpus indicus*, *Shorea contorta*, *Dipterocarpus grandiflorus*, *Swietenia macrophylla*,  
                   *Gmelina arborea*, *Eucalyptus camaldulensis*, *Acacia mangium*, *Bambusa blumeana*,  
                   *Dendrocalamus asper*, *Pinus kesiya*, *Rhizophora apiculata*, *Avicennia marina*
                6. Properly format abbreviated names (e.g., *E. coli*, *P. indicus*, *Pinus sp.*).
                7. Only italicize the genus and species parts — NOT the common names or author citations.

                VERIFICATION PROCESS:

                1. First, identify the key information needed to answer the question.
                2. Check if this information is explicitly present in the context from the "{category}" category.
                3. If not present, respond with the required fallback message and DO NOT add citations.
                4. For every statement in your answer, verify it is supported by the context.
                5. Remove any claim that is not directly grounded in the documents.
                6. You are ONLY allowed to use information from the "{category}" category documents.

                **Context (from "{category}" category only):**
                {{context}}

                **Question:**
                {{question}}

                **Answer (based primarily on the provided "{category}" category context):**
                """

        elif anti_hallucination_mode == 'balanced':
            # Use template from config if available, otherwise use default
            if prompt_templates and 'balanced' in prompt_templates:
                prompt_template = prompt_templates['balanced']
            else:
                # Balanced mode - Some reasonable inferences allowed
                prompt_template = f"""You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). You are answering a question about the "{category}" category of documents.

                CRITICAL RESTRICTIONS:
                1. You MUST primarily answer questions based on the provided context documents from the "{category}" category and the uploaded PDFs.
                2. You may make reasonable inferences ONLY when the context provides sufficient basis for them.
                3. You MUST NEVER reference information from other categories or sources outside the provided context.
                4. You are FORBIDDEN from using general knowledge about ERDB, agriculture, forestry, or any other topics unless it is explicitly stated in the provided context.
                5. If the context does not contain sufficient information to answer the question, you MUST respond with: 'I don't have enough information in the provided context from the {category} category to answer this question.'
                6. You MUST NOT reference or imply the existence of any information outside the provided context and selected category.
                7. If the context is empty or insufficient, you MUST NOT include any citations, links, or references to sources in your answer. Your answer must be a plain statement about the lack of information, with NO links or citations of any kind.
                8. If context IS found (i.e., there are relevant documents), you MUST provide citations/links for all factual claims, using the provided context and citation formatting rules below.

                ANTI-HALLUCINATION GUIDELINES (BALANCED MODE):
                1. If the context does not contain sufficient information to answer the question, you may make reasonable inferences based on the available information from the "{category}" category only.
                2. Clearly distinguish between facts from the context and your inferences.
                3. If you're uncertain about any information, explicitly state your uncertainty.
                4. Prioritize information directly supported by the context.
                5. For any claim directly from the context, identify the specific part that supports it.
                6. You are answering about the "{category}" category and ONLY the uploaded PDFs in this category.
                7. FINAL REMINDER: If the context is empty or insufficient, your answer MUST NOT contain any citations, links, or references to sources. If context IS found, you MUST provide citations/links for all factual claims using the provided context and citation formatting rules.

                FORMATTING GUIDELINES:
                1. Use bullet points, bold text, or other markdown features where appropriate
                2. CRITICAL: You MUST cite your sources using HTML links, but ONLY if context is present.
                   - For PDFs, you MUST use the `File Path` provided in the context for the link's `href` attribute. The `File Path` is the ONLY valid source for the `href` attribute.
                   - NEVER use the `Original URL` or any other URL for PDF citations.
                   - The format MUST be: 'According to <a href=\"file_path\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-600 hover:underline\">filename.pdf (Page X)</a>'
                   - The `file_path` for all PDFs will be in the format: '/download_gated/filename.pdf'
                   - For non-PDF sources (like URLs), you may use the `Original URL`.
                   - IMPORTANT: Use File Path for href (includes timestamp) but Citation Filename for display text (no timestamp)
                   - ALL links MUST include target=\"_blank\" and rel=\"noopener noreferrer\" attributes
                3. If the context includes multiple sources, integrate information from all relevant sources
                4. If the context mentions images or downloadable files, reference them in your answer
                5. NEVER use \"Source X:\" or \"Document:\" as a prefix in citations - always use ONLY the original filename.
                6. When making inferences, use phrases like \"Based on the context from the {category} category...\"
                7. FINAL REMINDER: If the context is empty or insufficient, your answer MUST NOT contain any citations, links, or references to sources. If context IS found, you MUST provide citations/links for all factual claims using the provided context and citation formatting rules.

                **Context (from {category} category only):**
                {{context}}

                **Question:**
                {{question}}

                **Answer (based primarily on the provided {category} category context):**
                """
        else:  # 'off' mode
            # Use template from config if available, otherwise use default
            if prompt_templates and 'off' in prompt_templates:
                prompt_template = prompt_templates['off']
            else:
                # Off mode - Allow more creative responses
                prompt_template = f"""You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). You are answering a question about the "{category}" category of documents.

                RESPONSE GUIDELINES (CREATIVE MODE):
                1. Use the provided context from the "{category}" category as your primary source of information.
                2. You may supplement with your knowledge when the context is insufficient, but prioritize the provided context.
                3. Clearly distinguish between facts from the context and additional information you provide.
                4. If you're uncertain about any information, explicitly state your uncertainty.
                5. When adding information beyond the context, use phrases like "Additionally..." or "Beyond what's in the provided {category} category documents..."
                6. You are answering about the "{category}" category and ONLY the uploaded PDFs in this category.
                7. If the context is empty or insufficient, you MUST NOT include any citations, links, or references to sources in your answer. Your answer must be a plain statement about the lack of information, with NO links or citations of any kind.
                8. If context IS found (i.e., there are relevant documents), you MUST provide citations/links for all factual claims, using the provided context and citation formatting rules below.
                9. FINAL REMINDER: If the context is empty or insufficient, your answer MUST NOT contain any citations, links, or references to sources. If context IS found, you MUST provide citations/links for all factual claims using the provided context and citation formatting rules.

                FORMATTING GUIDELINES:
                1. Use bullet points, bold text, or other markdown features where appropriate
                2. When citing the context, use HTML links with this format: 'According to <a href="/download_gated/filename.pdf" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">filename.pdf (Page X)</a>'
                   - CRITICAL: ALL PDFs MUST use the gated download link format: '/download_gated/filename.pdf'. NEVER use any other URL for PDF citations.
                   - IMPORTANT: Use File Path for href (includes timestamp) but Citation Filename for display text (no timestamp)
                   - ALL links MUST include target="_blank" and rel="noopener noreferrer" attributes
                3. If the context includes multiple sources, integrate information from all relevant sources
                4. If the context mentions images or downloadable files, reference them in your answer
                5. NEVER use "Source X:" or "Document:" as a prefix in citations - always use ONLY the original filename.
                6. AUTOMATICALLY identify and format all scientific names (binomial nomenclature) using markdown italics
                7. FINAL REMINDER: If the context is empty or insufficient, your answer MUST NOT contain any citations, links, or references to sources. If context IS found, you MUST provide citations/links for all factual claims using the provided context and citation formatting rules.

                **Context (from {category} category only):**
                {{context}}

                **Question:**
                {{question}}

                **Answer (prioritizing the {category} category context):**
                """

        # Add custom instructions if available
        if custom_instructions:
            # Insert custom instructions before the Context section
            context_index = prompt_template.find("**Context:**")
            if context_index > 0:
                prompt_template = (
                    prompt_template[:context_index] +
                    f"CUSTOM INSTRUCTIONS:\n{custom_instructions}\n\n" +
                    prompt_template[context_index:]
                )

        # Use the mode-specific prompt template instead of the unified template
        # The unified template was overriding the detailed instructions
        prompt = ChatPromptTemplate.from_template(prompt_template)

        # Create chain
        chain = prompt | llm | StrOutputParser()

        # Get answer
        answer = chain.invoke({"context": context, "question": question})

        # Post-processing: rewrite any links that start with 'http://localhost:8080' to be relative
        answer = re.sub(r'https?://localhost:8080(/download_gated/[^"\s>]+)', r'\1', answer)

        # --- BEGIN: NEW Template-Based Citation System ---
        # Convert simple citation placeholders to proper HTML citations
        logger.info(f"[Template Citations] Starting template-based citation conversion")
        logger.info(f"[Template Citations] Original answer preview: {answer[:500]}...")
        
        # ENHANCED DEBUG: Log exactly what citations we're looking for
        logger.info(f"[Template Citations] Looking for citation patterns in answer...")
        
        # Check what citation-like patterns exist in the answer
        citation_patterns_found = []
        
        # Check for square bracket patterns
        square_bracket_matches = re.findall(r'\[([^\]]+)\]', answer)
        if square_bracket_matches:
            logger.info(f"[Template Citations] Found square bracket patterns: {square_bracket_matches}")
            citation_patterns_found.extend(square_bracket_matches)
        
        # Check for common citation phrases
        citation_phrases = [
            r'According to ([^,.\n]+)',
            r'As stated in ([^,.\n]+)',  
            r'Based on ([^,.\n]+)',
            r'From ([^,.\n]+)',
            r'In ([^,.\n]+)',
            r'The ([^,.\n]+) shows',
            r'The ([^,.\n]+) indicates',
            r'The ([^,.\n]+) reports'
        ]
        
        for pattern in citation_phrases:
            matches = re.findall(pattern, answer, re.IGNORECASE)
            if matches:
                logger.info(f"[Template Citations] Found citation phrase matches for '{pattern}': {matches}")
                citation_patterns_found.extend(matches)
        
        # Check for any filename-like patterns (with .pdf extension)
        filename_matches = re.findall(r'([a-zA-Z0-9_\-\.]+\.pdf)', answer, re.IGNORECASE)
        if filename_matches:
            logger.info(f"[Template Citations] Found filename patterns: {filename_matches}")
            citation_patterns_found.extend(filename_matches)
        
        if not citation_patterns_found:
            logger.warning(f"[Template Citations] No citation patterns found in answer!")
            logger.warning(f"[Template Citations] Full answer for analysis: {answer}")
        
        # Convert citation placeholders using our new system
        answer = convert_citation_placeholders(answer, docs)
        
        logger.info(f"[Template Citations] After conversion preview: {answer[:500]}...")
        # --- END: NEW Template-Based Citation System ---

        # SIMPLE FIX: Skip markdown cleanup entirely since we have template citations
        # The template citation system ensures URLs are generated correctly by Python
        # No need to process markdown italics that could corrupt URLs
        logger.info(f"[Simple Fix] Skipping markdown cleanup to preserve URLs")
        logger.info(f"[Simple Fix] Answer preview: {answer[:200]}...")

        # DISABLE scientific name processing to prevent URL corruption
        logger.info(f"[Scientific Names] DISABLED to prevent URL corruption")
        logger.info(f"[Scientific Names] Answer preserved as-is: {answer[:200]}...")
        
        # Check URLs after all processing
        if '/download_gated/' in answer:
            urls_in_answer = re.findall(r'/download_gated/([^"\s>]+)', answer)
            for url in urls_in_answer:
                if '_' not in url and 'pdf' in url:
                    logger.error(f"[Final Check] ❌ CORRUPTED URL DETECTED: /download_gated/{url}")
                    logger.error(f"[Final Check] URL corruption occurred despite template citation system!")
                else:
                    logger.info(f"[Final Check] ✅ URL looks correct: /download_gated/{url}")

        # Post-processing verification to detect potential hallucinations
        hallucination_detected = False
        hallucination_warning = ""

        # Load insufficient info phrases from environment variable or use defaults
        try:
            insufficient_info_phrases = json.loads(os.getenv('INSUFFICIENT_INFO_PHRASES', '[]'))
            if not insufficient_info_phrases:  # If empty, use defaults
                insufficient_info_phrases = [
                    "I don't have enough information",
                    "The provided context does not contain",
                    "There is no information",
                    "No information is provided",
                    "The context doesn't mention",
                    "I cannot find information",
                    "No data is available",
                    "The context does not specify"
                ]
        except json.JSONDecodeError:
            # Fallback to default phrases if JSON parsing fails
            insufficient_info_phrases = [
                "I don't have enough information",
                "The provided context does not contain",
                "There is no information",
                "No information is provided",
                "The context doesn't mention",
                "I cannot find information",
                "No data is available",
                "The context does not specify"
            ]

        has_insufficient_info = any(phrase in answer for phrase in insufficient_info_phrases)

        # Skip hallucination detection if mode is 'off'
        if anti_hallucination_mode == 'off':
            # In 'off' mode, we don't flag hallucinations
            hallucination_detected = False
        # If the answer doesn't indicate insufficient info, check for potential hallucinations
        elif not has_insufficient_info:
            # Extract key statements from the answer (simple approach: split by periods and list items)
            statements = []
            for line in answer.split('\n'):
                # Skip citation lines
                if 'According to [' in line:
                    continue

                # Skip lines that indicate inferences in balanced mode
                if anti_hallucination_mode == 'balanced' and any(phrase in line for phrase in [
                    "Based on the context, it seems",
                    "The information suggests",
                    "It appears that",
                    "This suggests",
                    "It is likely",
                    "We can infer"
                ]):
                    continue

                # Process bullet points separately
                if line.strip().startswith('*') or line.strip().startswith('-'):
                    statements.append(line.strip())
                else:
                    # Split by periods for regular text
                    for statement in line.split('.'):
                        if len(statement.strip()) > config.min_statement_length:  # Use configured minimum length
                            statements.append(statement.strip())

            # Check if key statements are supported by the context
            unsupported_statements = []
            for statement in statements:
                # Skip very short statements and headers using configured minimum length
                if len(statement) < config.min_statement_length or statement.startswith('#'):
                    continue

                # Remove markdown formatting for better matching
                clean_statement = re.sub(r'[*_`#]', '', statement).lower()

                # Check if any document contains supporting evidence for this statement
                supported = False
                for doc in docs:
                    # Simple check: are there significant word overlaps between statement and document?
                    statement_words = set(clean_statement.split())
                    # Remove stop words
                    statement_words = statement_words - {'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'and', 'or', 'but', 'if', 'then', 'else', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now'}

                    # Only consider statements with meaningful content
                    if len(statement_words) < 3:
                        supported = True
                        break

                    # Count overlapping words
                    content_words = set(doc.page_content.lower().split())
                    overlap = statement_words.intersection(content_words)

                    # Use configured threshold based on anti-hallucination mode
                    threshold = config.get_hallucination_threshold(anti_hallucination_mode)

                    # If significant overlap, consider it supported
                    if len(overlap) / len(statement_words) > threshold:
                        supported = True
                        break

                if not supported:
                    unsupported_statements.append(statement)

            # If unsupported statements found, flag as potential hallucination
            if unsupported_statements:
                hallucination_detected = True

                # Different warnings based on mode
                if anti_hallucination_mode == 'strict':
                    hallucination_warning = "\n\n**Note: Some statements in this response may not be directly supported by the provided documents. Please verify any critical information.**"
                elif anti_hallucination_mode == 'balanced':
                    hallucination_warning = "\n\n**Note: This response contains some inferences beyond what is explicitly stated in the documents.**"

                # Log the potential hallucination for debugging
                logger.warning(f"Potential hallucination detected in response to: {question} (mode: {anti_hallucination_mode})")
                logger.warning(f"Unsupported statements: {unsupported_statements}")

                # Add the warning to the answer in strict mode only
                if anti_hallucination_mode == 'strict':
                    answer += hallucination_warning

        # Collect all extracted PDF images with deduplication
        pdf_images = []
        pdf_image_urls = set()  # Track unique image URLs

        # First pass: collect all unique PDF images
        for source in sources:
            if source.get("pdf_images"):
                for img in source.get("pdf_images"):
                    if isinstance(img, dict) and img.get("url"):
                        # Skip images that were marked as filtered by the vision model
                        if img.get("filtered", False):
                            continue

                        img_url = img.get("url")
                        if img_url not in pdf_image_urls:
                            pdf_image_urls.add(img_url)
                            # Create a new image dict with source information
                            enhanced_img = img.copy()
                            enhanced_img["sources"] = [source.get("display_name", "")]
                            enhanced_img["source_pages"] = [img.get("page", "")]
                            pdf_images.append(enhanced_img)
                        else:
                            # Image already exists, add this source to its metadata
                            for existing_img in pdf_images:
                                if existing_img.get("url") == img_url:
                                    source_name = source.get("display_name", "")
                                    if source_name not in existing_img["sources"]:
                                        existing_img["sources"].append(source_name)

                                    page = img.get("page", "")
                                    if page and page not in existing_img["source_pages"]:
                                        existing_img["source_pages"].append(page)
                                    break

        # Check if we need to analyze PDF images with the vision model
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
        if use_vision and pdf_images:
            try:
                # Get the maximum number of images to analyze
                max_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))

                # Extract document context for contextual captioning
                # Create a combined context from the top 3 most relevant documents
                doc_context = ""
                if docs:
                    # Sort docs by relevance score if available
                    sorted_docs = sorted(docs, key=lambda d: float(d.metadata.get("relevance_score", "0.0")), reverse=True)
                    # Take top 3 most relevant documents
                    top_docs = sorted_docs[:3]
                    # Combine their content
                    doc_context = "\n\n".join([doc.page_content for doc in top_docs])
                    # Truncate if too long using configured limit
                    if len(doc_context) > config.max_vision_context_length:
                        doc_context = doc_context[:config.max_vision_context_length] + "..."

                # Analyze PDF images with the vision model, passing document context and query
                pdf_images = vision_processor.analyze_pdf_images(
                    pdf_images,
                    max_images=max_images,
                    document_context=doc_context,
                    query=question
                )

                # Log analytics about image analysis
                if pdf_images and "analyzed_images" in pdf_images[0]:
                    logger.info(f"Analyzed {pdf_images[0].get('analyzed_images', 0)} out of {pdf_images[0].get('total_images', 0)} PDF images with vision model")
                    if pdf_images[0].get("has_context", False):
                        logger.info("Generated contextual captions using document context and query")

                # Sort images by relevance score if available
                pdf_images.sort(key=lambda img: img.get("relevance_score", 5), reverse=True)
            except Exception as e:
                logger.error(f"Error analyzing PDF images with vision model: {str(e)}")
                # Continue with unanalyzed images

        # Collect all extracted PDF tables with deduplication
        pdf_tables = []
        pdf_table_urls = set()  # Track unique table URLs

        # First pass: collect all unique PDF tables
        for source in sources:
            if source.get("pdf_tables"):
                for table in source.get("pdf_tables"):
                    if isinstance(table, dict) and table.get("url"):
                        table_url = table.get("url")
                        if table_url not in pdf_table_urls:
                            pdf_table_urls.add(table_url)
                            # Create a new table dict with source information
                            enhanced_table = table.copy()
                            enhanced_table["sources"] = [source.get("display_name", "")]
                            enhanced_table["source_pages"] = [table.get("page", "")]
                            pdf_tables.append(enhanced_table)
                        else:
                            # Table already exists, add this source to its metadata
                            for existing_table in pdf_tables:
                                if existing_table.get("url") == table_url:
                                    source_name = source.get("display_name", "")
                                    if source_name not in existing_table["sources"]:
                                        existing_table["sources"].append(source_name)

                                    page = table.get("page", "")
                                    if page and page not in existing_table["source_pages"]:
                                        existing_table["source_pages"].append(page)
                                    break

        # Filter for only JPG/JPEG images from URLs with deduplication
        url_jpg_images = []
        url_image_set = set()  # Track unique URL images

        # Collect all original URLs from the documents
        original_urls = [doc.metadata.get('original_url') for doc in docs if doc.metadata.get('original_url')]

        for img_url in all_images:
            # Skip if this URL is already in our PDF images
            if img_url in pdf_image_urls:
                continue

            # Skip if we've already processed this URL image
            if img_url in url_image_set:
                continue

            # Check if img_url is a valid string
            if not isinstance(img_url, str):
                logger.warning(f"Invalid image URL type: {type(img_url)}. Skipping.")
                continue

            # Check if it's a JPG/JPEG/PNG image (case insensitive)
            if re.search(r'\.(jpg|jpeg|png)$', img_url, re.IGNORECASE):
                # Use vision model to determine if the image should be filtered
                # This includes the previous logic (filtering PNGs with 'logo' in filename)
                # but adds content-based filtering using the vision model
                try:
                    if vision_processor.should_filter_image(img_url, is_pdf_image=False):
                        logger.info(f"Filtering out image based on content analysis: {img_url}")
                        continue
                except Exception as filter_err:
                    logger.error(f"Error filtering image {img_url}: {str(filter_err)}")
                    # Continue with the image if filtering fails

                # Only include images from URLs - check if the image URL contains any of our document URLs
                # or if any document URL is part of the image URL
                is_from_url = False
                for url in original_urls:
                    # Extract domain from URL to compare
                    url_domain = re.search(r'https?://([^/]+)', url)
                    img_domain = re.search(r'https?://([^/]+)', img_url)

                    if url_domain and img_domain and url_domain.group(1) == img_domain.group(1):
                        is_from_url = True
                        break

                if is_from_url or not original_urls:  # If no URLs found, include all JPG images
                    # Only add the image if it's not already in our list (strict deduplication)
                    if img_url not in url_jpg_images:
                        url_jpg_images.append(img_url)
                        url_image_set.add(img_url)

        # Format images for display - separate PDF images, URL images, and document thumbnails
        formatted_pdf_images = []
        formatted_url_images = []
        formatted_document_thumbnails = []

        # Sort document thumbnails to prioritize cover images
        # First, separate cover images from other thumbnails
        cover_images = []
        other_thumbnails = []

        for thumbnail in document_thumbnails:
            if isinstance(thumbnail, dict) and thumbnail.get("url"):
                # Check if this is a cover image (from cover_image directory)
                is_cover_image = "/cover_image/" in thumbnail.get("url", "")

                if is_cover_image:
                    cover_images.append(thumbnail)
                    logger.info(f"Found cover image: {thumbnail.get('url')}")
                else:
                    other_thumbnails.append(thumbnail)

        # Format cover images first, then other thumbnails
        for thumbnail in cover_images + other_thumbnails:
            if isinstance(thumbnail, dict) and thumbnail.get("url"):
                img_url = thumbnail.get("url")
                document_name = thumbnail.get("document_name", "Document")
                description = thumbnail.get("description", f"Thumbnail for {document_name}")
                original_url = thumbnail.get("original_url")
                thumbnail_source = thumbnail.get("source", "")
                is_cover_image = "/cover_image/" in img_url

                # Ensure the image URL is properly formatted for local paths
                if img_url and not img_url.startswith(('http://', 'https://')):
                    # Make sure the URL starts with a single slash
                    img_url = '/' + img_url.lstrip('/')
                    logger.info(f"Using local thumbnail path: {img_url}")

                # Create the link URL - use original_url if available, otherwise use the thumbnail URL
                link_url = original_url if original_url else img_url

                # Validate URLs to prevent rendering issues
                if not img_url:
                    logger.warning(f"Empty thumbnail URL for document: {document_name}")
                    continue

                # Log the thumbnail details for debugging
                logger.debug(f"Formatting thumbnail - URL: {img_url}, Link: {link_url}, Document: {document_name}, Source: {thumbnail_source}")

                # Add a CSS class to highlight cover images
                cover_image_class = "cover-image" if is_cover_image else ""

                try:
                    # Format the thumbnail HTML
                    formatted_document_thumbnails.append(
                        f'<div class="image-container {cover_image_class}">'
                        f'<a href="{link_url}" target="_blank" rel="noopener noreferrer">'
                        f'<img src="{img_url}" alt="{description}" class="img-fluid rounded shadow-sm" style="height:10rem;object-fit:cover;" />'
                        f'</a>'
                        f'<div class="text-xs text-gray-500 mt-1">{document_name} <span class="text-xs text-gray-400">({thumbnail_source})</span></div>'
                        f'</div>'
                    )
                    log_message = "Added cover image thumbnail" if is_cover_image else "Added document thumbnail"
                    logger.info(f"{log_message}: {img_url} (source: {thumbnail_source})")
                except Exception as e:
                    logger.error(f"Error formatting thumbnail for {document_name}: {str(e)}")

        # Format PDF-extracted images using configured limit
        for img in pdf_images[:config.max_pdf_images_display]:
            if isinstance(img, dict) and img.get("url"):
                img_url = img.get("url")
                sources = img.get("sources", [])
                pages = img.get("source_pages", [])

                # Create source attribution
                if len(sources) == 1:
                    source_info = f"Image from {sources[0]}"
                    if pages and pages[0]:
                        source_info += f" (Page {pages[0]})"
                else:
                    # Multiple sources
                    source_info = "Image from multiple sources: "
                    source_details = []
                    for i, source in enumerate(sources):
                        if i < len(pages) and pages[i]:
                            source_details.append(f"{source} (Page {pages[i]})")
                        else:
                            source_details.append(source)
                    source_info += ", ".join(source_details)

                # Use AI-generated description if available
                if img.get("ai_description"):
                    ai_desc = img.get("ai_description")
                    # Truncate long descriptions using configured limit
                    if len(ai_desc) > config.image_caption_max_length:
                        ai_desc = ai_desc[:config.image_caption_max_length-3] + "..."
                    caption = f"{ai_desc} ({source_info})"
                    alt_text = ai_desc
                elif img.get("description") and "Image from page" not in img.get("description"):
                    # Use enhanced description if it's not the default one
                    caption = f"{img.get('description')} ({source_info})"
                    alt_text = img.get("description")
                else:
                    # Fallback to source info only
                    caption = source_info
                    alt_text = source_info

                # Add relevance score if available (for debugging/admin view)
                relevance_badge = ""
                if img.get("relevance_score"):
                    score = img.get("relevance_score")
                    relevance_badge = f'<span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 ml-1">Relevance: {score}/10</span>'

                # Add category if available
                category_badge = ""
                if img.get("category") and img.get("category") != "unknown":
                    category = img.get("category")
                    category_badge = f'<span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 ml-1">{category}</span>'

                formatted_pdf_images.append(
                    f'<div class="image-container">'
                    f'<a href="{img_url}" target="_blank" rel="noopener noreferrer">'
                    f'<img src="{img_url}" alt="{alt_text}" class="w-full h-auto rounded-lg shadow-md hover:opacity-90 transition-opacity" />'
                    f'</a>'
                    f'<div class="text-xs text-gray-500 mt-1">{caption}{category_badge}{relevance_badge}</div>'
                    f'</div>'
                )

        # Format URL JPG/PNG images with contextual vision model descriptions using configured limit
        for img_url in url_jpg_images[:config.max_url_images_display]:
            # Get image description from vision model
            try:
                # Check if vision analysis is enabled
                use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'

                if use_vision:
                    # Use the same document context we created for PDF images
                    doc_context = ""
                    if 'doc_context' in locals():
                        doc_context = doc_context
                    elif docs:
                        # Sort docs by relevance score if available
                        sorted_docs = sorted(docs, key=lambda d: float(d.metadata.get("relevance_score", "0.0")), reverse=True)
                        # Take top 3 most relevant documents
                        top_docs = sorted_docs[:3]
                        # Combine their content
                        doc_context = "\n\n".join([doc.page_content for doc in top_docs])
                        # Truncate if too long using configured limit
                        if len(doc_context) > config.max_vision_context_length:
                            doc_context = doc_context[:config.max_vision_context_length] + "..."

                    # Get image content analysis with context
                    image_analysis = vision_processor.detect_image_content(
                        img_url,
                        is_pdf_image=False,
                        document_context=doc_context,
                        query=question
                    )

                    # Create a caption from the analysis
                    if "error" not in image_analysis:
                        # Prefer contextual caption if available
                        if image_analysis.get("contextual_caption"):
                            caption = image_analysis.get("contextual_caption")
                            logger.info(f"Using contextual caption for URL image: {img_url}")
                        else:
                            caption = image_analysis.get("description", "Image from URL")

                        # Truncate long descriptions
                        if len(caption) > 100:
                            caption = caption[:97] + "..."
                    else:
                        caption = "Image from URL"
                else:
                    caption = "Image from URL"
            except Exception as e:
                logger.error(f"Error getting image description: {str(e)}")
                caption = "Image from URL"

            formatted_url_images.append(
                f'<div class="image-container">'
                f'<a href="{img_url}" target="_blank" rel="noopener noreferrer">'
                f'<img src="{img_url}" alt="{caption}" class="w-full h-auto rounded-lg shadow-md hover:opacity-90 transition-opacity" />'
                f'</a>'
                f'<div class="text-xs text-gray-500 mt-1">{caption}</div>'
                f'</div>'
            )

        # Combine all images for backward compatibility
        formatted_images = formatted_pdf_images + formatted_url_images

        # Format tables for display using configured limit
        formatted_tables = []
        for table in pdf_tables[:config.max_tables_display]:
            if isinstance(table, dict) and table.get("url"):
                table_url = table.get("url")
                rows = table.get("rows", 0)
                cols = table.get("columns", 0)
                sources = table.get("sources", [])
                pages = table.get("source_pages", [])
                extraction_method = table.get("extraction_method", "")
                html_content = table.get("html", "")

                # Create caption with table information
                base_caption = f"Table with {rows} rows, {cols} columns"

                # Add source information
                if len(sources) == 1:
                    source_info = f" from {sources[0]}"
                    if pages and pages[0]:
                        source_info += f" (Page {pages[0]})"
                else:
                    # Multiple sources
                    source_info = " from multiple sources: "
                    source_details = []
                    for i, source in enumerate(sources):
                        if i < len(pages) and pages[i]:
                            source_details.append(f"{source} (Page {pages[i]})")
                        else:
                            source_details.append(source)
                    source_info += ", ".join(source_details)

                caption = base_caption + source_info

                # Add extraction method if available
                if extraction_method:
                    caption += f" (extracted using {extraction_method})"

                # Clean up the HTML content for display
                if html_content:
                    # Add Bootstrap-compatible classes to the table
                    html_content = html_content.replace('<table', '<table class="table table-sm table-bordered table-responsive"')

                    # Create a collapsible section with the table
                    formatted_tables.append(
                        f'<div class="table-container mb-4 overflow-auto">'
                        f'<div class="text-sm font-medium mb-1">{caption}</div>'
                        f'<details class="bg-white border rounded-lg overflow-hidden">'
                        f'<summary class="p-3 bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer flex items-center">'
                        f'<svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
                        f'<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>'
                        f'</svg>'
                        f'View Table Content'
                        f'</summary>'
                        f'<div class="p-3 overflow-auto max-h-64">{html_content}</div>'
                        f'</details>'
                        f'<div class="mt-1 text-xs text-gray-500">'
                        f'<a href="{table_url}" target="_blank" class="text-blue-600 hover:underline">Open in new tab</a>'
                        f'</div>'
                        f'</div>'
                    )
                else:
                    # Fallback if no HTML content is available
                    formatted_tables.append(
                        f'<div class="table-container mb-4">'
                        f'<div class="text-sm font-medium mb-1">{caption}</div>'
                        f'<a href="{table_url}" target="_blank" class="block p-3 bg-gray-50 border rounded-lg hover:bg-gray-100 transition-colors">'
                        f'<div class="flex items-center">'
                        f'<svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
                        f'<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>'
                        f'</svg>'
                        f'View Table'
                        f'</div>'
                        f'</a>'
                        f'</div>'
                    )

        # Load followup question templates from environment variable or use default
        if os.getenv('DISABLE_FOLLOWUP_QUESTIONS', 'false').lower() == 'true':
            followup_questions = []
        else:
            try:
                followup_templates = json.loads(os.getenv('FOLLOWUP_QUESTION_TEMPLATES', '{}'))
            except json.JSONDecodeError:
                followup_templates = {}

            # Choose appropriate template based on whether we have insufficient information
            if has_insufficient_info and followup_templates and 'insufficient_info' in followup_templates:
                # Use the insufficient info template if available and needed
                followup_prompt_template = followup_templates['insufficient_info']

                # Get list of categories for the insufficient_info template
                categories = sorted(os.listdir(os.getenv("CHROMA_PATH", "./data/chroma"))) if os.path.exists(os.getenv("CHROMA_PATH", "./data/chroma")) else []
                categories_str = "\n".join([f"- {cat}" for cat in categories])
            else:
                # Use default template or the one from config
                if followup_templates and 'default' in followup_templates:
                    followup_prompt_template = followup_templates['default']
                else:
                    # Default follow-up questions template
                    followup_prompt_template = """You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Based on the original question, the provided context, and the answer given, generate 3 possible follow-up questions that:\n1. Are directly related to the topic of the original question\n2. Can be answered using the provided context\n3. Would naturally follow in a conversation about this topic\n4. Are diverse and cover different aspects of the topic\n5. Are concise and clear (no more than 15 words each)\n6. AUTOMATICALLY format any scientific names (binomial nomenclature) using markdown italics (e.g., *Pterocarpus indicus*, *Homo sapiens*)\n7. Include subspecies in italics when present (e.g., *Homo sapiens sapiens*)\n8. Keep author citations in regular text (e.g., *Escherichia coli* (Migula 1895))\n\n**Original Question:**\n{question}\n\n**Context Summary:**\n{context_summary}\n\n**Answer Provided:**\n{answer}\n\n**Follow-up Questions:**\nProvide exactly 3 follow-up questions in a JSON array format like this:\n[\"Question 1?\", \"Question 2?\", \"Question 3?\"]\n"""

            # Create a shorter context summary for the follow-up questions prompt
            context_summary = "\n".join([
                f"- Document: {doc.metadata.get('original_filename', doc.metadata.get('source', 'Unknown'))}, " +
                f"Type: {doc.metadata.get('type', 'Unknown')}, " +
                f"Content: {doc.page_content[:100]}..."
                for doc in docs[:3]  # Use only top 3 documents for summary
            ])

            followup_prompt = ChatPromptTemplate.from_template(followup_prompt_template)
            followup_chain = followup_prompt | llm | StrOutputParser()

            # Generate follow-up questions
            followup_questions = []
            try:
                # Prepare parameters for the follow-up questions prompt
                invoke_params = {
                    "context_summary": context_summary,
                    "question": question,
                    "answer": answer
                }

                # Add categories parameter if we're using the insufficient_info template
                if has_insufficient_info and 'categories_str' in locals():
                    invoke_params["categories"] = categories_str

                # Generate follow-up questions
                followup_response = followup_chain.invoke(invoke_params)

                # Extract the JSON array of questions
                json_match = re.search(r'\[.*\]', followup_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    followup_questions = json.loads(json_str)
                else:
                    # Fallback: try to parse the entire response as JSON
                    followup_questions = json.loads(followup_response)

                # Ensure we have exactly 3 questions
                if len(followup_questions) > 3:
                    followup_questions = followup_questions[:3]
                while len(followup_questions) < 3:
                    followup_questions.append(f"Can you tell me more about {category}?")

            except Exception as e:
                logger.error(f"Failed to parse follow-up questions: {str(e)}")
                # Provide default follow-up questions if parsing fails
                followup_questions = [
                    f"Can you tell me more about {category}?",
                    "What are the key points from these documents?",
                    "How can I learn more about this topic?"
                ]

        # Extract locations from the user question and AI answer
        locations_extracted = 0
        try:
            from app.services.location_extractor import LocationExtractor
            from app.utils.database import save_extracted_location, save_location_source

            location_extractor = LocationExtractor()

            # Extract locations from user question
            question_locations = location_extractor.extract_locations_from_text(question, context="user_question")

            # Extract locations from AI answer
            answer_locations = location_extractor.extract_locations_from_text(answer, context="ai_response")

            # Combine all locations
            all_chat_locations = question_locations + answer_locations

            for location in all_chat_locations:
                # Try to geocode the location
                from app.services.location_extractor import geocode_location
                geocoding_result = geocode_location(location['location_text'])

                if geocoding_result and geocoding_result.get('status') == 'success':
                    location.update({
                        'latitude': geocoding_result['latitude'],
                        'longitude': geocoding_result['longitude'],
                        'geocoded_address': geocoding_result['formatted_address'],
                        'country': geocoding_result['country'],
                        'region': geocoding_result['region'],
                        'city': geocoding_result['city']
                    })

                # Save the location to database
                location_id = save_extracted_location(location)

                if location_id:
                    # Note: For chat messages, we would need to save the chat_id as source_id
                    # This would require modifying the chat history saving to return the chat_id
                    # For now, we'll save with a placeholder source_id
                    save_location_source(
                        location_id=location_id,
                        source_type='chat_message',
                        source_id=0,  # Placeholder - would need actual chat_id
                        extraction_method=location['extraction_method']
                    )
                    locations_extracted += 1

            if locations_extracted > 0:
                logger.info(f"Extracted {locations_extracted} locations from chat interaction")

        except Exception as e:
            logger.error(f"Error extracting locations from chat: {str(e)}")

        # Calculate processing time for analytics
        processing_time = time.time() - start_time
        logger.info(f"Query processing completed in {processing_time:.2f} seconds")

        # Check if vision model is enabled
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
        vision_model = os.getenv('VISION_MODEL', 'llama3.2-vision:11b-instruct-q4_K_M') if use_vision else None

        # Prepare analytics data
        analytics = {
            "question_length": len(question),
            "answer_length": len(answer),
            "processing_time": processing_time,
            "source_count": len(sources),
            "image_count": len(formatted_url_images) + len(formatted_pdf_images),
            "token_count": None,  # We don't have token count information
            "model_name": model_name,
            "embedding_model": embedding_model,
            "vision_model": vision_model,
            "vision_enabled": use_vision,
            "hallucination_detected": hallucination_detected,
            "category": category,
            "client_name": client_name,
            "session_id": session_id,
            "device_fingerprint": device_fingerprint,
            "locations_extracted": locations_extracted
        }

        # Add image filtering statistics if available
        images_filtered = 0
        total_images_extracted = 0

        # Collect statistics from all sources
        for source in sources:
            # First ensure source is a dictionary
            if not isinstance(source, dict):
                logger.warning(f"Source is not a dictionary: {source}")
                continue

            # Make sure metadata is a dictionary before accessing it
            if source.get("metadata") and isinstance(source.get("metadata"), dict):
                if source["metadata"].get("images_filtered"):
                    images_filtered += source["metadata"].get("images_filtered", 0)
                if source["metadata"].get("total_images_extracted"):
                    total_images_extracted += source["metadata"].get("total_images_extracted", 0)

        # Add to analytics
        analytics["images_filtered"] = images_filtered
        analytics["total_images_extracted"] = total_images_extracted
        analytics["filter_sensitivity"] = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

        # Include hallucination detection metadata in the response
        return {
            "answer": answer,
            "sources": sources,
            "images": formatted_images,  # Keep for backward compatibility
            "url_images": formatted_url_images,  # New field for URL JPG images to display at the top
            "pdf_images": formatted_pdf_images,  # New field for PDF-extracted images
            "document_thumbnails": formatted_document_thumbnails,  # New field for document thumbnails
            "tables": formatted_tables,  # Add extracted tables
            "pdf_links": all_pdf_links[:config.max_pdf_links_display],  # Use configured limit
            "followup_questions": followup_questions,  # Add follow-up questions
            "metadata": {
                "hallucination_detected": hallucination_detected,
                "document_count": len(docs),
                "image_count": len(formatted_images),
                "url_image_count": len(formatted_url_images),
                "pdf_image_count": len(formatted_pdf_images),
                "document_thumbnail_count": len(formatted_document_thumbnails),
                "table_count": len(formatted_tables),
                "link_count": len(all_pdf_links[:config.max_pdf_links_display]),
                "vision_model": vision_model,
                "vision_enabled": use_vision,
                "images_filtered": images_filtered,
                "total_images_extracted": total_images_extracted,
                "filter_sensitivity": os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium'),
                "relevance_scores": [float(doc.metadata.get("relevance_score", 0)) for doc in docs if "relevance_score" in doc.metadata]
            },
            "analytics": analytics  # Include analytics data in the response
        }
    except Exception as e:
        logger.error(f"Query failed: {str(e)}")
        try:
            # Calculate processing time for analytics even in error case
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.info(f"Query processing failed after {processing_time:.2f} seconds")

            # Prepare error analytics data
            # Check if vision model is enabled
            use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
            vision_model = os.getenv('VISION_MODEL', 'llama3.2-vision:11b-instruct-q4_K_M') if use_vision else None

            # Make sure model_name and embedding_model are defined
            if 'model_name' not in locals():
                model_name = os.getenv('LLM_MODEL', 'llama3.1:8b-instruct-q4_K_M')

            if 'embedding_model' not in locals():
                embedding_model = os.getenv('TEXT_EMBEDDING_MODEL', 'mxbai-embed-large:latest')

            analytics = {
                "question_length": len(question),
                "answer_length": 0,  # No answer generated
                "processing_time": processing_time,
                "source_count": 0,
                "image_count": 0,
                "token_count": None,
                "model_name": model_name,
                "embedding_model": embedding_model,
                "vision_model": vision_model,
                "vision_enabled": use_vision,
                "images_filtered": 0,
                "total_images_extracted": 0,
                "filter_sensitivity": os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium'),
                "hallucination_detected": False,
                "category": category,
                "client_name": client_name,
                "session_id": session_id,
                "device_fingerprint": device_fingerprint,
                "error": str(e)
            }

            # Ensure we return a properly formatted dictionary
            return {
                "answer": f"An error occurred while processing the query: {str(e)}",
                "sources": [],
                "images": [],
                "url_images": [],  # New field for URL JPG images
                "pdf_images": [],  # New field for PDF-extracted images
                "document_thumbnails": [],  # New field for document thumbnails
                "tables": [],
                "pdf_links": [],
                "followup_questions": [
                    "Can you try asking a different question?",
                    "Would you like to try a more specific query?",
                    "Can you rephrase your question?"
                ],
                "metadata": {
                    "hallucination_detected": False,
                    "document_count": 0,
                    "image_count": 0,
                    "url_image_count": 0,
                    "pdf_image_count": 0,
                    "document_thumbnail_count": 0,
                    "table_count": 0,
                    "link_count": 0,
                    "relevance_scores": [],
                    "error": str(e)
                },
                "analytics": analytics  # Include analytics data in the error response
            }
        except Exception as nested_error:
            # If we can't even create the error response dictionary, return a simple string
            # This will be caught and handled by the app.py error handler
            logger.error(f"Failed to create error response: {str(nested_error)}")
            return f"Critical error in query processing: {str(e)}"

def enrich_document_metadata(docs: List[Document]) -> List[Document]:
    """
    Enrich document metadata by resolving source_url_id to actual URLs.
    
    Args:
        docs: List of documents with metadata
        
    Returns:
        List of documents with enriched metadata
    """
    for doc in docs:
        # Check if document has source_url_id but no original_url
        source_url_id = doc.metadata.get("source_url_id")
        if source_url_id and not doc.metadata.get("original_url"):
            try:
                # Resolve the source URL ID to actual URL
                actual_url = get_source_url_by_id(source_url_id)
                if actual_url:
                    doc.metadata["original_url"] = actual_url
                    logger.info(f"Resolved source_url_id {source_url_id} to URL: {actual_url}")
                else:
                    logger.warning(f"Could not resolve source_url_id {source_url_id} to URL")
            except Exception as e:
                logger.error(f"Error resolving source_url_id {source_url_id}: {str(e)}")
    
    return docs

def convert_citation_placeholders(text: str, docs: List[Document]) -> str:
    """
    Convert simple citation placeholders to proper HTML citations.
    
    Converts patterns like:
    - "According to [canopy_vol45n1.pdf]" 
    - "As stated in [filename.pdf]"
    - "[filename.pdf] shows that..."
    - "According to filename.pdf" (without brackets)
    - "The canopy_vol45n1.pdf document shows" (direct filename references)
    
    To proper HTML citations with correct file paths.
    
    Args:
        text: The text containing citation placeholders
        docs: List of documents to build citation mappings from
        
    Returns:
        Text with placeholders converted to HTML citations
    """
    logger.info(f"[Citation Conversion] Starting placeholder conversion")
    
    # Build citation mapping from document metadata
    citation_map = {}
    
    for doc in docs:
        if doc.metadata.get("type") == "pdf":
            source_name = doc.metadata.get("source", "")
            citation_filename = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", ""))
            page_num = doc.metadata.get("page", None)
            
            # Generate the correct file path using the same logic as sources section
            correct_file_path = f"/download_gated/{source_name}"
            
            # Map citation filename to proper HTML citation
            if citation_filename:
                page_text = f" (Page {page_num})" if page_num else ""
                html_citation = f'<a href="{correct_file_path}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{citation_filename}{page_text}</a>'
                
                # Create multiple mapping entries for different possible formats
                citation_map[citation_filename.lower()] = html_citation
                
                # Also map without .pdf extension
                filename_no_ext = citation_filename.replace('.pdf', '').lower()
                citation_map[filename_no_ext] = html_citation
                
                # Map the source name as well (timestamped filename)
                if source_name:
                    citation_map[source_name.lower()] = html_citation
                    source_no_ext = source_name.replace('.pdf', '').lower()
                    citation_map[source_no_ext] = html_citation
                
                logger.info(f"[Citation Conversion] Mapped '{citation_filename}' -> '{correct_file_path}'")
    
    logger.info(f"[Citation Conversion] Built citation map with {len(citation_map)} entries")
    
    # Convert citation placeholders to HTML citations
    def replace_citation(match):
        prefix = match.group(1) if match.group(1) else ""
        filename = match.group(2)
        
        # Clean up filename (remove .pdf if present for lookup)
        lookup_filename = filename.replace('.pdf', '').lower()
        
        if lookup_filename in citation_map:
            html_citation = citation_map[lookup_filename]
            logger.info(f"[Citation Conversion] ✅ Converted [{filename}] -> HTML citation")
            return f"{prefix}{html_citation}"
        else:
            logger.warning(f"[Citation Conversion] ❌ No mapping found for [{filename}]")
            return match.group(0)  # Return original if no mapping found
    
    # Enhanced citation replacement with multiple patterns
    patterns_replaced = 0
    original_text = text
    
    # Pattern 1: Standard placeholder format [filename.pdf]
    citation_pattern1 = r'((?:According to|As stated in|Based on|From|In|The|As per|Per)\s+)?\[([^\]]+)\]'
    text = re.sub(citation_pattern1, replace_citation, text)
    patterns_replaced += len(re.findall(citation_pattern1, original_text))
    
    # Pattern 2: Direct filename references without brackets
    def replace_direct_filename(match):
        prefix = match.group(1) if match.group(1) else ""
        filename = match.group(2)
        
        # Clean up filename for lookup
        lookup_filename = filename.replace('.pdf', '').lower()
        
        if lookup_filename in citation_map:
            html_citation = citation_map[lookup_filename]
            logger.info(f"[Citation Conversion] ✅ Converted direct reference '{filename}' -> HTML citation")
            return f"{prefix}{html_citation}"
        else:
            return match.group(0)  # Return original if no mapping found
    
    # Pattern 2: "According to filename.pdf" (without brackets)
    citation_pattern2 = r'((?:According to|As stated in|Based on|From|In|The|As per|Per)\s+)([a-zA-Z0-9_\-\.]+\.pdf)'
    text = re.sub(citation_pattern2, replace_direct_filename, text, flags=re.IGNORECASE)
    patterns_replaced += len(re.findall(citation_pattern2, original_text, re.IGNORECASE))
    
    # Pattern 3: "The filename.pdf document/shows/indicates"
    citation_pattern3 = r'(The\s+)([a-zA-Z0-9_\-\.]+\.pdf)(\s+(?:document|shows|indicates|reports|states|mentions))'
    def replace_document_reference(match):
        prefix = match.group(1)
        filename = match.group(2)
        suffix = match.group(3)
        
        lookup_filename = filename.replace('.pdf', '').lower()
        
        if lookup_filename in citation_map:
            html_citation = citation_map[lookup_filename]
            logger.info(f"[Citation Conversion] ✅ Converted document reference '{filename}' -> HTML citation")
            return f"{prefix}{html_citation}{suffix}"
        else:
            return match.group(0)
    
    text = re.sub(citation_pattern3, replace_document_reference, text, flags=re.IGNORECASE)
    patterns_replaced += len(re.findall(citation_pattern3, original_text, re.IGNORECASE))
    
    if patterns_replaced > 0:
        logger.info(f"[Citation Conversion] ✅ Successfully converted {patterns_replaced} citation patterns")
    else:
        logger.warning(f"[Citation Conversion] No citation patterns found to convert")
        logger.warning(f"[Citation Conversion] Available mappings: {list(citation_map.keys())}")
    
    return text

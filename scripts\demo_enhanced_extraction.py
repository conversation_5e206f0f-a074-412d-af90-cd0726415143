#!/usr/bin/env python3
"""
Demonstration script for enhanced academic PDF extraction functionality.

This script shows how to use the new enhanced extraction features:
1. Extract titles and authors from academic PDFs
2. Use confidence scoring
3. Store and retrieve articles
4. Search and filter articles

Usage:
    python scripts/demo_enhanced_extraction.py --pdf path/to/academic.pdf
"""

import os
import sys
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.pdf_processor import extract_articles_with_confidence
from app.utils.articles_db import search_articles, get_article_statistics

def demo_extraction(pdf_path):
    """Demonstrate enhanced extraction on a PDF."""
    print(f"\n🔍 Analyzing PDF: {os.path.basename(pdf_path)}")
    print("="*60)
    
    # Extract articles with confidence scoring
    print("\n📝 Extracting articles with enhanced academic methods...")
    articles = extract_articles_with_confidence(
        pdf_path,
        min_overall_confidence=0.6,
        debug=True
    )
    
    print(f"\n✅ Found {len(articles)} articles with confidence >= 0.6")
    
    # Display results
    for i, article in enumerate(articles, 1):
        print(f"\n📄 Article {i}:")
        print(f"   Title: {article['title']}")
        print(f"   Authors: <AUTHORS>
        print(f"   Page: {article['page']}")
        print(f"   Overall Confidence: {article['overall_confidence']:.3f}")
        print(f"   Title Confidence: {article['title_confidence']:.3f}")
        print(f"   Author Confidence: {article['authors_confidence']:.3f}")
        print(f"   Extraction Method: {article['extraction_method']}")
        
        # Show original case if different
        if article['title'] != article['title_original_case']:
            print(f"   Original Title Case: {article['title_original_case']}")
        if article['authors'] != article['authors_original_case']:
            print(f"   Original Author Case: {article['authors_original_case']}")
    
    return articles

def demo_search():
    """Demonstrate article search functionality."""
    print(f"\n🔍 Demonstrating article search functionality...")
    print("="*60)
    
    # Get statistics
    print("\n📊 Database Statistics:")
    stats = get_article_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Search examples
    search_terms = ["machine learning", "neural", "algorithm", "data"]
    
    for term in search_terms:
        print(f"\n🔎 Searching for '{term}'...")
        results = search_articles(
            search_query=term,
            min_confidence=0.7,
            limit=5
        )
        
        if results['articles']:
            print(f"   Found {len(results['articles'])} articles:")
            for article in results['articles'][:3]:  # Show first 3
                print(f"   • {article['title'][:50]}... (confidence: {article['overall_confidence']:.3f})")
        else:
            print(f"   No articles found for '{term}'")

def demo_api_usage():
    """Demonstrate API usage examples."""
    print(f"\n🌐 API Usage Examples:")
    print("="*60)
    
    print("\n📋 Available API Endpoints:")
    endpoints = [
        "GET /api/articles - List all articles with filtering",
        "GET /api/articles/statistics - Get article statistics", 
        "GET /api/articles/low-confidence?threshold=0.7 - Get low confidence articles",
        "GET /api/articles/by-pdf/{pdf_id} - Get articles for specific PDF",
        "GET /api/articles/by-method/{method} - Get articles by extraction method"
    ]
    
    for endpoint in endpoints:
        print(f"   • {endpoint}")
    
    print("\n📝 Example API Calls:")
    examples = [
        "curl 'http://localhost:8080/api/articles?search=machine%20learning&min_confidence=0.8&limit=10'",
        "curl 'http://localhost:8080/api/articles/statistics'",
        "curl 'http://localhost:8080/api/articles/low-confidence?threshold=0.7'",
        "curl 'http://localhost:8080/api/articles/by-method/academic_enhanced'"
    ]
    
    for example in examples:
        print(f"   {example}")

def demo_migration():
    """Demonstrate migration script usage."""
    print(f"\n🔄 Migration Script Usage:")
    print("="*60)
    
    print("\n📝 Migrate existing PDFs with enhanced extraction:")
    migration_examples = [
        "python scripts/migrate_existing_pdfs.py --dry-run",
        "python scripts/migrate_existing_pdfs.py --category CANOPY --limit 10",
        "python scripts/migrate_existing_pdfs.py --min-confidence 0.7 --force",
        "python scripts/migrate_existing_pdfs.py --debug"
    ]
    
    for example in migration_examples:
        print(f"   {example}")

def main():
    parser = argparse.ArgumentParser(description='Demonstrate enhanced academic PDF extraction')
    parser.add_argument('--pdf', help='Path to PDF file for extraction demo')
    parser.add_argument('--search-demo', action='store_true', help='Run search demonstration')
    parser.add_argument('--api-demo', action='store_true', help='Show API usage examples')
    parser.add_argument('--migration-demo', action='store_true', help='Show migration script usage')
    parser.add_argument('--all', action='store_true', help='Run all demonstrations')
    
    args = parser.parse_args()
    
    print("🎓 Enhanced Academic PDF Extraction - Demonstration")
    print("="*60)
    print("\nThis demonstration shows the new enhanced extraction capabilities:")
    print("• Academic journal title extraction with ALL CAPS conversion")
    print("• Multi-format author extraction (AND, comma-separated, etc.)")
    print("• Confidence scoring for quality assessment")
    print("• Structured article storage and retrieval")
    print("• Comprehensive search and filtering API")
    
    # PDF extraction demo
    if args.pdf and (args.all or not any([args.search_demo, args.api_demo, args.migration_demo])):
        if os.path.exists(args.pdf):
            demo_extraction(args.pdf)
        else:
            print(f"\n❌ PDF file not found: {args.pdf}")
    
    # Search demo
    if args.search_demo or args.all:
        demo_search()
    
    # API demo
    if args.api_demo or args.all:
        demo_api_usage()
    
    # Migration demo
    if args.migration_demo or args.all:
        demo_migration()
    
    print(f"\n🎯 Key Features Summary:")
    print("="*60)
    features = [
        "✅ Enhanced title extraction focusing on upper page regions",
        "✅ ALL CAPS conversion for academic consistency", 
        "✅ Multi-format author parsing (AND, comma-separated, etc.)",
        "✅ Confidence scoring based on font size, position, and content",
        "✅ Structured article storage in dedicated database table",
        "✅ Comprehensive search and filtering API endpoints",
        "✅ Migration script for processing existing PDFs",
        "✅ Integration with existing PDF processing pipeline"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print(f"\n📚 Next Steps:")
    print("   1. Test with your academic journal PDFs")
    print("   2. Migrate existing PDFs using the migration script")
    print("   3. Use the API endpoints to list and search articles")
    print("   4. Integrate with your existing admin dashboard")
    
    print(f"\n🔗 For more information, see:")
    print("   • scripts/test_enhanced_extraction.py - Comprehensive testing")
    print("   • scripts/migrate_existing_pdfs.py - Migration script")
    print("   • /api/articles endpoints - REST API documentation")

if __name__ == "__main__":
    main()

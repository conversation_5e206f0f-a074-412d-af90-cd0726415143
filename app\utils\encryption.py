"""
Data Encryption Utilities for ERDB Document Management System

This module provides utilities for encrypting sensitive data at rest,
including user data, configuration files, and backup files.
"""

import os
import base64
import logging
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC
from cryptography.hazmat.backends import default_backend
from typing import Optional, Union, Dict, Any
import json

logger = logging.getLogger(__name__)

class DataEncryption:
    """Handles encryption and decryption of sensitive data."""
    
    def __init__(self, key: Optional[str] = None):
        """
        Initialize encryption with a key.
        
        Args:
            key: Encryption key. If None, will try to load from environment or generate new one.
        """
        self.key = key or self._get_or_generate_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_generate_key(self) -> bytes:
        """Get encryption key from environment or generate a new one."""
        # Try to get from environment
        env_key = os.getenv('ENCRYPTION_KEY')
        if env_key:
            try:
                # Decode base64 key from environment
                return base64.urlsafe_b64decode(env_key + '=' * (-len(env_key) % 4))
            except Exception as e:
                logger.warning(f"Invalid encryption key in environment: {str(e)}")
        
        # Generate new key
        key = Fernet.generate_key()
        
        # Save to environment file if it doesn't exist
        env_file = '.env'
        if not os.path.exists(env_file):
            with open(env_file, 'w') as f:
                f.write(f"ENCRYPTION_KEY={base64.urlsafe_b64encode(key).decode()}\n")
            logger.info("Generated new encryption key and saved to .env file")
        else:
            logger.warning("No encryption key found. Please add ENCRYPTION_KEY to your .env file")
        
        return key
    
    def encrypt_data(self, data: Union[str, bytes, Dict[str, Any]]) -> str:
        """
        Encrypt data and return base64 encoded string.
        
        Args:
            data: Data to encrypt (string, bytes, or dictionary)
            
        Returns:
            Base64 encoded encrypted data
        """
        try:
            # Convert data to bytes
            if isinstance(data, dict):
                data_bytes = json.dumps(data, ensure_ascii=False).encode('utf-8')
            elif isinstance(data, str):
                data_bytes = data.encode('utf-8')
            elif isinstance(data, bytes):
                data_bytes = data
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")
            
            # Encrypt
            encrypted_data = self.cipher.encrypt(data_bytes)
            return base64.urlsafe_b64encode(encrypted_data).decode()
        
        except Exception as e:
            logger.error(f"Encryption failed: {str(e)}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> Union[str, Dict[str, Any]]:
        """
        Decrypt base64 encoded data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Decrypted data (string or dictionary)
        """
        try:
            # Decode base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data + '=' * (-len(encrypted_data) % 4))
            
            # Decrypt
            decrypted_bytes = self.cipher.decrypt(encrypted_bytes)
            
            # Try to decode as JSON first, then as string
            try:
                return json.loads(decrypted_bytes.decode('utf-8'))
            except json.JSONDecodeError:
                return decrypted_bytes.decode('utf-8')
        
        except Exception as e:
            logger.error(f"Decryption failed: {str(e)}")
            raise
    
    def encrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        Encrypt a file.
        
        Args:
            file_path: Path to file to encrypt
            output_path: Output path for encrypted file (optional)
            
        Returns:
            Path to encrypted file
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Read file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Encrypt
            encrypted_data = self.cipher.encrypt(file_data)
            
            # Determine output path
            if output_path is None:
                output_path = file_path + '.encrypted'
            
            # Write encrypted file
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            logger.info(f"File encrypted: {file_path} -> {output_path}")
            return output_path
        
        except Exception as e:
            logger.error(f"File encryption failed: {str(e)}")
            raise
    
    def decrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        Decrypt a file.
        
        Args:
            file_path: Path to encrypted file
            output_path: Output path for decrypted file (optional)
            
        Returns:
            Path to decrypted file
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Read encrypted file
            with open(file_path, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt
            decrypted_data = self.cipher.decrypt(encrypted_data)
            
            # Determine output path
            if output_path is None:
                if file_path.endswith('.encrypted'):
                    output_path = file_path[:-10]  # Remove .encrypted extension
                else:
                    output_path = file_path + '.decrypted'
            
            # Write decrypted file
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            logger.info(f"File decrypted: {file_path} -> {output_path}")
            return output_path
        
        except Exception as e:
            logger.error(f"File decryption failed: {str(e)}")
            raise

# Global encryption instance
_encryption_instance: Optional[DataEncryption] = None

def get_encryption() -> DataEncryption:
    """Get the global encryption instance."""
    global _encryption_instance
    if _encryption_instance is None:
        _encryption_instance = DataEncryption()
    return _encryption_instance

def encrypt_sensitive_field(value: str) -> str:
    """Encrypt a sensitive field value."""
    if not value:
        return value
    
    try:
        encryption = get_encryption()
        return encryption.encrypt_data(value)
    except Exception as e:
        logger.error(f"Failed to encrypt field: {str(e)}")
        return value

def decrypt_sensitive_field(value: str) -> str:
    """Decrypt a sensitive field value."""
    if not value or not value.startswith('gAAAAA'):  # Fernet encrypted data starts with this
        return value
    
    try:
        encryption = get_encryption()
        return encryption.decrypt_data(value)
    except Exception as e:
        logger.error(f"Failed to decrypt field: {str(e)}")
        return value

def encrypt_config_file(config_path: str) -> bool:
    """Encrypt a configuration file."""
    try:
        encryption = get_encryption()
        encryption.encrypt_file(config_path)
        return True
    except Exception as e:
        logger.error(f"Failed to encrypt config file {config_path}: {str(e)}")
        return False

def decrypt_config_file(config_path: str) -> bool:
    """Decrypt a configuration file."""
    try:
        encryption = get_encryption()
        encryption.decrypt_file(config_path)
        return True
    except Exception as e:
        logger.error(f"Failed to decrypt config file {config_path}: {str(e)}")
        return False 
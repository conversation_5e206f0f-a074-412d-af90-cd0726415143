/**
 * Chat Maps JavaScript Module
 * 
 * Provides contextual mapping functionality for chat responses containing
 * Philippine administrative divisions (municipalities, cities, barangays).
 * 
 * Features:
 * - Automatic location detection in chat responses
 * - Contextual map display for research-related content
 * - Philippine administrative division markers
 * - Asynchronous loading to avoid blocking chat responses
 * - Cached map data for performance
 */

const ChatMaps = {
    mapCache: new Map(),
    locationCache: new Map(),
    
    // ERDB Brand Colors for location types
    colors: {
        municipality: '#378C47',  // Dark green
        city: '#0267B6',         // Dark blue
        barangay: '#3CA6D6',     // Light blue
        default: '#5BA85B'       // Light green
    },
    
    /**
     * Analyze chat response for Philippine locations and display map if relevant
     * @param {string} responseText - The chat response text
     * @param {string} responseId - Unique identifier for the response
     * @param {Array} sources - Source documents referenced in the response
     */
    analyzeAndDisplayMap: function(responseText, responseId, sources = []) {
        // Check if this response warrants a map display
        if (!this.shouldDisplayMap(responseText, sources)) {
            return;
        }
        
        // Extract locations from the response
        this.extractLocationsFromResponse(responseText, sources)
            .then(locations => {
                if (locations && locations.length >= 3) {
                    this.displayContextualMap(responseId, locations);
                }
            })
            .catch(error => {
                console.error('Error extracting locations for map:', error);
            });
    },
    
    /**
     * Determine if a chat response should display a contextual map
     * @param {string} responseText - The response text to analyze
     * @param {Array} sources - Source documents
     * @returns {boolean} True if map should be displayed
     */
    shouldDisplayMap: function(responseText, sources) {
        const text = responseText.toLowerCase();
        
        // Research and study indicators
        const researchIndicators = [
            'study area', 'research site', 'field study', 'survey area', 'data collection',
            'study location', 'research area', 'field work', 'sampling site', 'study site',
            'conducted in', 'located in', 'situated in', 'found in', 'observed in',
            'municipality', 'city', 'barangay', 'province', 'region'
        ];
        
        // Check for research context
        const hasResearchContext = researchIndicators.some(indicator => 
            text.includes(indicator)
        );
        
        // Check for multiple location mentions
        const locationPatterns = [
            /\b(?:municipality|city|barangay)\s+(?:of\s+)?[\w\s]+/gi,
            /\b[\w\s]+\s+(?:municipality|city|barangay)\b/gi,
            /\bsan\s+[\w\s]+/gi,
            /\bsanta\s+[\w\s]+/gi
        ];
        
        let locationCount = 0;
        locationPatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                locationCount += matches.length;
            }
        });
        
        // Display map if research context and multiple locations
        return hasResearchContext && locationCount >= 2;
    },
    
    /**
     * Extract Philippine locations from response text and sources
     * @param {string} responseText - The response text
     * @param {Array} sources - Source documents
     * @returns {Promise<Array>} Promise resolving to array of locations
     */
    extractLocationsFromResponse: function(responseText, sources) {
        return new Promise((resolve, reject) => {
            // Create cache key
            const cacheKey = this.createCacheKey(responseText, sources);
            
            // Check cache first
            if (this.locationCache.has(cacheKey)) {
                resolve(this.locationCache.get(cacheKey));
                return;
            }
            
            // Extract locations using the backend API
            fetch('/api/extract-locations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: responseText,
                    sources: sources,
                    filter_philippine: true,
                    admin_levels_only: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Cache the results
                    this.locationCache.set(cacheKey, data.locations);
                    resolve(data.locations);
                } else {
                    reject(new Error(data.message || 'Failed to extract locations'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    },
    
    /**
     * Display a contextual map for the chat response
     * @param {string} responseId - Unique identifier for the response
     * @param {Array} locations - Array of location objects
     */
    displayContextualMap: function(responseId, locations) {
        // Create map container
        const mapContainer = this.createMapContainer(responseId);
        
        // Find the response element and insert map
        const responseElement = document.querySelector(`[data-response-id="${responseId}"]`);
        if (!responseElement) {
            console.warn('Response element not found for map display');
            return;
        }
        
        // Insert map container after the response text
        responseElement.appendChild(mapContainer);
        
        // Initialize the map asynchronously
        setTimeout(() => {
            this.initializeMap(responseId, locations);
        }, 100);
    },
    
    /**
     * Create a map container element
     * @param {string} responseId - Response identifier
     * @returns {HTMLElement} Map container element
     */
    createMapContainer: function(responseId) {
        const container = document.createElement('div');
        container.className = 'chat-map-container mt-3 mb-3';
        container.innerHTML = `
            <div class="card">
                <div class="card-header bg-primary text-white py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        Study Area Locations
                        <button class="btn btn-sm btn-outline-light float-end" onclick="ChatMaps.toggleMap('${responseId}')">
                            <i class="fas fa-compress-alt"></i>
                        </button>
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div id="chatMap_${responseId}" style="height: 300px; width: 100%;"></div>
                </div>
                <div class="card-footer py-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Locations extracted from research content. 
                        Map data &copy; <a href="https://www.openstreetmap.org/copyright" target="_blank">OpenStreetMap</a>
                    </small>
                </div>
            </div>
        `;
        
        return container;
    },
    
    /**
     * Initialize a Leaflet map for the chat response
     * @param {string} responseId - Response identifier
     * @param {Array} locations - Array of location objects
     */
    initializeMap: function(responseId, locations) {
        const mapId = `chatMap_${responseId}`;
        
        // Check if map already exists
        if (this.mapCache.has(mapId)) {
            return;
        }
        
        // Calculate center point
        const center = this.calculateMapCenter(locations);
        
        // Initialize map
        const map = L.map(mapId, {
            center: center,
            zoom: 10,
            zoomControl: true,
            attributionControl: true
        });
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);
        
        // Add location markers
        const bounds = L.latLngBounds();
        locations.forEach(location => {
            if (location.latitude && location.longitude) {
                const marker = this.createLocationMarker(location);
                marker.addTo(map);
                bounds.extend([location.latitude, location.longitude]);
            }
        });
        
        // Fit map to show all markers
        if (bounds.isValid()) {
            map.fitBounds(bounds, { padding: [10, 10] });
        }
        
        // Cache the map
        this.mapCache.set(mapId, map);
    },
    
    /**
     * Create a marker for a location
     * @param {Object} location - Location object
     * @returns {L.Marker} Leaflet marker
     */
    createLocationMarker: function(location) {
        // Determine marker color based on administrative level
        let color = this.colors.default;
        if (location.administrative_level) {
            color = this.colors[location.administrative_level] || this.colors.default;
        }
        
        // Create custom icon
        const icon = L.divIcon({
            className: 'chat-map-marker',
            html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10],
            popupAnchor: [0, -10]
        });
        
        // Create marker
        const marker = L.marker([location.latitude, location.longitude], { icon: icon });
        
        // Create popup content
        const popupContent = `
            <div class="chat-map-popup">
                <h6 class="fw-bold mb-1">${location.location_text}</h6>
                <span class="badge bg-primary mb-2">${location.administrative_level || location.location_type}</span>
                ${location.geocoded_address ? `<p class="mb-1 small text-muted">${location.geocoded_address}</p>` : ''}
                <small class="text-muted">Confidence: ${Math.round(location.confidence_score * 100)}%</small>
            </div>
        `;
        
        marker.bindPopup(popupContent, {
            maxWidth: 250,
            className: 'chat-map-popup-container'
        });
        
        return marker;
    },
    
    /**
     * Calculate the center point for the map
     * @param {Array} locations - Array of location objects
     * @returns {Array} [latitude, longitude]
     */
    calculateMapCenter: function(locations) {
        const validLocations = locations.filter(loc => loc.latitude && loc.longitude);
        
        if (validLocations.length === 0) {
            return [14.1648, 121.2413]; // Los Baños default
        }
        
        const avgLat = validLocations.reduce((sum, loc) => sum + loc.latitude, 0) / validLocations.length;
        const avgLng = validLocations.reduce((sum, loc) => sum + loc.longitude, 0) / validLocations.length;
        
        return [avgLat, avgLng];
    },
    
    /**
     * Toggle map visibility
     * @param {string} responseId - Response identifier
     */
    toggleMap: function(responseId) {
        const mapContainer = document.querySelector(`#chatMap_${responseId}`);
        if (mapContainer) {
            const isVisible = mapContainer.style.display !== 'none';
            mapContainer.style.display = isVisible ? 'none' : 'block';
            
            // Update button icon
            const button = mapContainer.closest('.card').querySelector('.card-header button');
            if (button) {
                const icon = button.querySelector('i');
                icon.className = isVisible ? 'fas fa-expand-alt' : 'fas fa-compress-alt';
            }
            
            // Invalidate map size if showing
            if (!isVisible) {
                const map = this.mapCache.get(`chatMap_${responseId}`);
                if (map) {
                    setTimeout(() => map.invalidateSize(), 100);
                }
            }
        }
    },
    
    /**
     * Create a cache key for location data
     * @param {string} responseText - Response text
     * @param {Array} sources - Source documents
     * @returns {string} Cache key
     */
    createCacheKey: function(responseText, sources) {
        const textHash = this.simpleHash(responseText);
        const sourcesHash = this.simpleHash(JSON.stringify(sources));
        return `${textHash}_${sourcesHash}`;
    },
    
    /**
     * Simple hash function for cache keys
     * @param {string} str - String to hash
     * @returns {string} Hash value
     */
    simpleHash: function(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatMaps;
}

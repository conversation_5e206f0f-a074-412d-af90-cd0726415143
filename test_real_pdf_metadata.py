#!/usr/bin/env python3
"""
Real PDF Metadata Extraction Test

This script tests the enhanced metadata extraction capabilities on a real PDF file
to demonstrate the improvements in article title and author identification.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.semantic_chunking_service import create_semantic_chunking_service
from app.services.pdf_processor import pdf_to_documents_hybrid
from langchain.schema import Document

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_real_pdf_metadata_extraction():
    """Test enhanced metadata extraction on a real PDF file."""
    
    # PDF file to test
    pdf_file = "test_files/CANOPY/canopy_v44n2.pdf"
    
    if not os.path.exists(pdf_file):
        logger.error(f"PDF file not found: {pdf_file}")
        return
    
    logger.info(f"Testing enhanced metadata extraction on: {pdf_file}")
    logger.info("=" * 80)
    
    try:
        # Step 1: Process PDF to documents
        logger.info("Step 1: Processing PDF to documents...")
        documents = pdf_to_documents_hybrid(pdf_file)
        logger.info(f"Extracted {len(documents)} documents from PDF")
        
        # Step 2: Create semantic chunking service with enhanced metadata extraction
        logger.info("Step 2: Creating semantic chunking service...")
        semantic_chunker = create_semantic_chunking_service(
            strategy="hybrid",
            chunk_size=800,
            chunk_overlap=160,
            extract_article_metadata=True,
            extract_publication_info=True,
            validate_metadata=True,
            confidence_threshold=0.7,
            enable_cross_reference=True
        )
        
        # Step 3: Extract enhanced metadata
        logger.info("Step 3: Extracting enhanced metadata...")
        
        # Flatten documents if they're nested in lists
        flat_documents = []
        for doc in documents:
            if isinstance(doc, list):
                flat_documents.extend(doc)
            else:
                flat_documents.append(doc)
        
        logger.info(f"Processing {len(flat_documents)} flattened documents")
        article_metadata = semantic_chunker.extract_article_metadata(flat_documents)
        
        # Step 4: Display results
        logger.info("Step 4: Enhanced Metadata Extraction Results")
        logger.info("=" * 80)
        
        print(f"\n📄 Document: {os.path.basename(pdf_file)}")
        print(f"📊 Total Documents: {len(documents)}")
        print(f"🔍 Extraction Method: {article_metadata.extraction_method}")
        
        print(f"\n📝 Title: {article_metadata.title}")
        print(f"👥 Authors ({len(article_metadata.authors)}):")
        for i, author in enumerate(article_metadata.authors, 1):
            print(f"   {i}. {author}")
        
        print(f"\n🏢 Affiliations ({len(article_metadata.affiliations)}):")
        for i, affiliation in enumerate(article_metadata.affiliations, 1):
            print(f"   {i}. {affiliation}")
        
        if article_metadata.abstract:
            print(f"\n📋 Abstract Preview:")
            abstract_preview = article_metadata.abstract[:200] + "..." if len(article_metadata.abstract) > 200 else article_metadata.abstract
            print(f"   {abstract_preview}")
        
        print(f"\n🏷️ Keywords ({len(article_metadata.keywords)}):")
        for i, keyword in enumerate(article_metadata.keywords[:10], 1):  # Show first 10 keywords
            print(f"   {i}. {keyword}")
        if len(article_metadata.keywords) > 10:
            print(f"   ... and {len(article_metadata.keywords) - 10} more")
        
        print(f"\n📚 Publication Information:")
        for key, value in article_metadata.publication_info.items():
            print(f"   {key}: {value}")
        
        print(f"\n🎯 Confidence Scores:")
        for key, value in article_metadata.confidence_scores.items():
            confidence_bar = "█" * int(value * 10) + "░" * (10 - int(value * 10))
            print(f"   {key}: {confidence_bar} {value:.3f}")
        
        # Step 5: Process chunks with enhanced metadata
        logger.info("Step 5: Processing chunks with enhanced metadata...")
        chunks = semantic_chunker.chunk_documents(flat_documents)
        
        # Count chunks with enhanced metadata
        chunks_with_metadata = 0
        for chunk in chunks:
            if any(key in chunk.metadata for key in ['article_title', 'article_authors', 'article_abstract']):
                chunks_with_metadata += 1
        
        print(f"\n📦 Chunk Processing Results:")
        print(f"   Total Chunks: {len(chunks)}")
        print(f"   Chunks with Enhanced Metadata: {chunks_with_metadata}")
        print(f"   Metadata Coverage: {chunks_with_metadata/len(chunks)*100:.1f}%" if chunks else "N/A")
        
        # Step 6: Save detailed results
        results = {
            'test_timestamp': datetime.now().isoformat(),
            'pdf_file': pdf_file,
            'total_documents': len(flat_documents),
            'total_chunks': len(chunks),
            'chunks_with_metadata': chunks_with_metadata,
            'metadata_coverage': chunks_with_metadata/len(chunks)*100 if chunks else 0,
            'article_metadata': {
                'title': article_metadata.title,
                'authors': article_metadata.authors,
                'affiliations': article_metadata.affiliations,
                'abstract': article_metadata.abstract,
                'keywords': article_metadata.keywords,
                'publication_info': article_metadata.publication_info,
                'confidence_scores': article_metadata.confidence_scores,
                'extraction_method': article_metadata.extraction_method
            },
            'sample_chunks': []
        }
        
        # Add sample chunks with metadata
        for i, chunk in enumerate(chunks[:5]):  # First 5 chunks
            chunk_info = {
                'chunk_id': i + 1,
                'content_preview': chunk.page_content[:100] + "..." if len(chunk.page_content) > 100 else chunk.page_content,
                'metadata': {k: v for k, v in chunk.metadata.items() if k.startswith('article_') or k in ['publication_info', 'metadata_confidence']}
            }
            results['sample_chunks'].append(chunk_info)
        
        # Save results to file
        output_file = f"real_pdf_metadata_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        # Step 7: Summary and recommendations
        print(f"\n🎯 Summary:")
        avg_confidence = sum(article_metadata.confidence_scores.values()) / len(article_metadata.confidence_scores) if article_metadata.confidence_scores else 0
        print(f"   Average Confidence: {avg_confidence:.3f}")
        print(f"   Metadata Extraction: {'✅ SUCCESS' if article_metadata.title or article_metadata.authors else '❌ FAILED'}")
        print(f"   Chunk Integration: {'✅ SUCCESS' if chunks_with_metadata > 0 else '❌ FAILED'}")
        
        if avg_confidence < 0.5:
            print(f"\n⚠️  Recommendations:")
            print(f"   - Consider adjusting confidence threshold")
            print(f"   - Check document format and structure")
            print(f"   - Verify that document contains clear title/author information")
        
        logger.info("Real PDF metadata extraction test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during real PDF metadata extraction test: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function to run the real PDF metadata extraction test."""
    print("Real PDF Enhanced Metadata Extraction Test")
    print("=" * 50)
    print("This script tests the enhanced metadata extraction")
    print("capabilities on a real PDF file.")
    print()
    
    try:
        test_real_pdf_metadata_extraction()
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"\n❌ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 
<!-- RAG-Optimized Text Chunking Section -->
<div class="config-section">
    <h2 class="config-section-header">RAG-Optimized Text Chunking</h2>
    <p class="config-section-description">Configure document chunking for optimal Retrieval-Augmented Generation (RAG) performance. These settings are used for both embedding storage and LlamaIndex processing with unified optimization.</p>
    
    <!-- RAG Optimization Status Panel -->
    <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 p-6 rounded-lg border border-green-200 dark:border-green-600 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">Unified RAG-Optimized Chunking</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Current Setting</div>
                        <div class="text-green-600 dark:text-green-400 font-semibold">{{ embedding_params.chunk_size }} chars, {{ embedding_params.chunk_overlap }} overlap</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Unified across all processing</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">RAG Optimization</div>
                        <div class="text-blue-600 dark:text-blue-400 font-semibold">800 chars, 160 overlap</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Optimal performance</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Used By</div>
                        <div class="text-purple-600 dark:text-purple-400 font-semibold">All Systems</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Embedding + LlamaIndex</div>
                    </div>
                </div>
                <div class="text-sm text-green-700 dark:text-green-300">
                    <p><strong>Benefits:</strong> Unified chunking strategy eliminates configuration redundancy and improves performance across all processing stages. Single configuration point for optimal RAG performance.</p>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Text contrast fixes for embedding config partial */
        .config-section-header { color: #1a202c !important; }
        .config-section-description { color: #4a5568 !important; }

        /* Form element text colors */
        textarea, input, select { color: #1a202c !important; }

        /* Label text colors */
        label.text-sm.font-medium.text-gray-700,
        .text-gray-700 { color: #2d3748 !important; }

        /* Helper text colors */
        .text-xs.text-gray-500 { color: #6b7280 !important; }

        /* Checkbox label text */
        .font-medium.text-gray-700 { color: #2d3748 !important; }

        /* Dependency indicator */
        .dependency-indicator { color: #2563eb !important; }
        .text-blue-600 { color: #2563eb !important; }

        /* Select options */
        option { color: #1a202c !important; }
    </style>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <label for="chunk_size" class="block text-sm font-medium text-gray-700 mb-2">Chunk Size (characters) <span class="text-blue-600 font-semibold">({{ embedding_params.chunk_size }})</span></label>
            <div class="flex items-center">
                <input type="number" id="chunk_size" name="chunk_size" min="200" max="1200" step="50" value="{{ embedding_params.chunk_size }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            </div>
            <p class="mt-2 text-xs text-gray-500"><strong>RAG Optimal:</strong> 800 characters provides best retrieval precision and context balance.</p>
            <div class="mt-2 text-xs text-gray-600">
                <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">400-600</span> High Precision
                <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded mr-2">800</span> Optimal RAG
                <span class="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 rounded">1000-1200</span> High Recall
            </div>
            <div class="dependency-indicator mt-2">
                <span class="text-blue-600">↑</span> Used by embedding storage and LlamaIndex processing
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <label for="chunk_overlap" class="block text-sm font-medium text-gray-700 mb-2">Chunk Overlap (characters) <span class="text-blue-600 font-semibold">({{ embedding_params.chunk_overlap }})</span></label>
            <div class="flex items-center">
                <input type="number" id="chunk_overlap" name="chunk_overlap" min="0" max="300" step="10" value="{{ embedding_params.chunk_overlap }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            </div>
            <p class="mt-2 text-xs text-gray-500"><strong>RAG Optimal:</strong> 20% of chunk size (160 chars for 800 char chunks) ensures context continuity.</p>
            <div class="mt-2 text-xs text-gray-600">
                <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">10%</span> Minimal Overlap
                <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded mr-2">20%</span> Optimal RAG
                <span class="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 rounded">30%</span> High Context
            </div>
            <div class="dependency-indicator mt-2">
                <span class="text-blue-600">↑</span> Must be smaller than chunk size
            </div>
        </div>
    </div>

    <!-- RAG Performance Presets -->
    <div class="mt-6">
        <h3 class="text-lg font-medium text-gray-800 mb-3">RAG Performance Presets</h3>
        <p class="text-sm text-gray-600 mb-4">Quick configuration options optimized for different RAG use cases. These presets apply unified settings across all processing stages.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button type="button" id="rag_preset_high_precision" class="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-blue-800">High Precision</div>
                </div>
                <div class="text-xs text-blue-600 mb-2">500 chars, 50 overlap</div>
                <div class="text-xs text-blue-500">
                    • Best for precise answers<br>
                    • Smaller context windows<br>
                    • Faster processing
                </div>
            </button>

            <button type="button" id="rag_preset_optimal" class="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-green-800">Optimal RAG</div>
                </div>
                <div class="text-xs text-green-600 mb-2">800 chars, 160 overlap</div>
                <div class="text-xs text-green-500">
                    • Best overall performance<br>
                    • Balanced precision/recall<br>
                    • Recommended default
                </div>
            </button>

            <button type="button" id="rag_preset_high_recall" class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-yellow-800">High Recall</div>
                </div>
                <div class="text-xs text-yellow-600 mb-2">1000 chars, 200 overlap</div>
                <div class="text-xs text-yellow-500">
                    • Best for comprehensive answers<br>
                    • Larger context windows<br>
                    • More thorough retrieval
                </div>
            </button>
        </div>
    </div>
</div>

<!-- Document Processing Section -->
<div class="config-section">
    <h2 class="config-section-header">Document Processing</h2>
    <p class="config-section-description">Configure how different document elements are handled during embedding with RAG-optimized processing.</p>

    <div class="space-y-4">
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="extract_tables" name="extract_tables" type="checkbox"
                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        {% if embedding_params.extract_tables %}checked{% endif %}>
                </div>
                <div class="ml-3 text-sm">
                    <label for="extract_tables" class="font-medium text-gray-700">Extract Tables</label>
                    <p class="text-gray-500">Extract and process tables from PDF documents as separate chunks for better RAG performance.</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="extract_images" name="extract_images" type="checkbox"
                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        {% if embedding_params.extract_images %}checked{% endif %}>
                </div>
                <div class="ml-3 text-sm">
                    <label for="extract_images" class="font-medium text-gray-700">Extract Images</label>
                    <p class="text-gray-500">Extract and save images from PDF documents for enhanced document understanding.</p>
                </div>
            </div>
        </div>

        <!-- Vision Model Settings for Document Processing -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 p-6 rounded-lg border border-blue-200 dark:border-blue-600">
            <h3 class="text-lg font-medium text-blue-800 mb-2 flex items-center">
                <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                Vision Model Settings
            </h3>
            <p class="text-sm text-blue-600 mb-4">Configure how the vision model analyzes images during document embedding. Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported for enhanced RAG performance.</p>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="use_vision_model" name="use_vision_during_embedding" type="checkbox"
                            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            {% if embedding_params.use_vision_model %}checked{% endif %}>
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="use_vision_model" class="font-medium text-gray-700">Enable Vision Model During Embedding</label>
                        <p class="text-gray-500">Use vision model to analyze and describe images during document embedding for enhanced RAG capabilities.</p>
                        <div class="dependency-indicator mt-1">
                            <span class="text-blue-600">↑</span> Requires a vision model to be selected in AI Models tab
                        </div>
                    </div>
                </div>

                <div class="ml-7 {% if not embedding_params.use_vision_model %}opacity-50{% endif %}" id="visionModelOptions">
                    <div class="mb-3 mt-4">
                        <label for="filter_sensitivity" class="block text-sm font-medium text-gray-700 mb-1">Image Filter Sensitivity</label>
                        <select id="filter_sensitivity" name="filter_sensitivity"
                            class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                            {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                            <option value="low" {% if embedding_params.filter_sensitivity == 'low' %}selected{% endif %}>Low - Keep most images</option>
                            <option value="medium" {% if embedding_params.filter_sensitivity == 'medium' %}selected{% endif %}>Medium - Balanced filtering</option>
                            <option value="high" {% if embedding_params.filter_sensitivity == 'high' %}selected{% endif %}>High - Only keep very relevant images</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="max_images" class="block text-sm font-medium text-gray-700 mb-1">Maximum Images to Process</label>
                        <input type="number" id="max_images" name="max_pdf_images" min="1" max="100"
                            value="{{ embedding_params.max_images }}"
                            class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                            {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                        <p class="mt-1 text-xs text-gray-500">Limit the number of images processed per document to improve performance.</p>
                    </div>

                    <div class="mt-3">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="show_filtered_images" class="form-checkbox h-5 w-5 text-blue-600"
                                   {% if show_filtered_images %}checked{% endif %}
                                   {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                            <span class="ml-2 text-gray-700">Show Filtered Images</span>
                        </label>
                        <p class="mt-1 text-xs text-gray-500 ml-7">Display thumbnails of filtered images with filtering reasons.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="extract_images" name="extract_images" type="checkbox"
                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        {% if embedding_params.extract_images %}checked{% endif %}>
                </div>
                <div class="ml-3 text-sm">
                    <label for="extract_images" class="font-medium text-gray-700">Extract Images</label>
                    <p class="text-gray-500">Extract and save images from PDF documents for enhanced document understanding.</p>
                </div>
            </div>
        </div>

        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="extract_locations" name="extract_locations" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if embedding_params.extract_locations %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="extract_locations" class="font-medium text-gray-700">Extract Locations</label>
                <p class="text-gray-500">Extract geographical locations (Municipality, City, Barangay) from PDF documents using NER.</p>
                <div class="dependency-indicator">
                    <span class="text-blue-600">↑</span> Focuses on Philippine administrative divisions
                </div>
            </div>
        </div>

        <div class="ml-7 {% if not embedding_params.extract_locations %}opacity-50{% endif %}" id="locationExtractionOptions">
            <div class="mb-3 mt-4">
                <label for="location_confidence_threshold" class="block text-sm font-medium text-gray-700 mb-1">Location Confidence Threshold</label>
                <select id="location_confidence_threshold" name="location_confidence_threshold"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                    {% if not embedding_params.extract_locations %}disabled{% endif %}>
                    <option value="0.3" {% if embedding_params.location_confidence_threshold == 0.3 %}selected{% endif %}>Low (0.3) - Extract more locations</option>
                    <option value="0.5" {% if embedding_params.location_confidence_threshold == 0.5 or not embedding_params.location_confidence_threshold %}selected{% endif %}>Medium (0.5) - Balanced extraction</option>
                    <option value="0.7" {% if embedding_params.location_confidence_threshold == 0.7 %}selected{% endif %}>High (0.7) - Extract only confident locations</option>
                </select>
                <p class="mt-1 text-xs text-gray-500">Minimum confidence score for location extraction. Higher values reduce false positives.</p>
            </div>

            <div class="mb-3">
                <label for="max_locations_per_document" class="block text-sm font-medium text-gray-700 mb-1">Max Locations per Document</label>
                <input type="number" id="max_locations_per_document" name="max_locations_per_document"
                    min="5" max="100" value="{{ embedding_params.max_locations_per_document or 50 }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                    {% if not embedding_params.extract_locations %}disabled{% endif %}>
                <p class="mt-1 text-xs text-gray-500">Maximum number of locations to extract per PDF document.</p>
            </div>

            <div class="mt-3">
                <label class="inline-flex items-center">
                    <input type="checkbox" name="enable_geocoding" class="form-checkbox h-5 w-5 text-blue-600"
                           {% if embedding_params.enable_geocoding %}checked{% endif %}
                           {% if not embedding_params.extract_locations %}disabled{% endif %}>
                    <span class="ml-2 text-gray-700">Enable Geocoding</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 ml-7">Automatically geocode extracted locations to get coordinates for mapping.</p>
            </div>
        </div>
    </div>
</div>

<!-- Performance Optimization Section -->
<div class="config-section">
    <h2 class="config-section-header">Performance Optimization</h2>
    <p class="config-section-description">Configure parameters that affect embedding performance and resource usage.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="batch_size" class="block text-sm font-medium text-gray-700 mb-1">Batch Size</label>
            <input type="number" id="batch_size" name="batch_size" min="1" max="100" value="{{ embedding_params.batch_size }}"
                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <p class="mt-1 text-xs text-gray-500">Number of chunks to process in a single batch. Higher values use more memory but may be faster.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Affects memory usage and processing speed
            </div>
        </div>

        <div>
            <label for="processing_threads" class="block text-sm font-medium text-gray-700 mb-1">Processing Threads</label>
            <input type="number" id="processing_threads" name="processing_threads" min="1" max="16" value="{{ embedding_params.processing_threads }}"
                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <p class="mt-1 text-xs text-gray-500">Number of parallel threads for document processing. Higher values may improve speed on multi-core systems.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Affects CPU usage and processing speed
            </div>
        </div>
    </div>
</div>

<!-- Add Save button at the very end of the form -->
<div class="d-flex justify-content-end mt-4 mb-2">
    <button type="button" id="saveEmbeddingBtn" class="btn btn-primary btn-lg px-5">
        <span id="saveEmbeddingBtnText">Save</span>
        <span id="saveEmbeddingBtnSpinner" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
    </button>
</div>

#!/usr/bin/env python3
"""
Practical Usage Example for LlamaIndex + LangChain Hybrid Integration

This script demonstrates how to use the integration in real-world scenarios.
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.pdf_processor import pdf_to_documents_hybrid
from app.services.llamaindex_service import llamaindex_service
from config.rag_extraction_config import (
    get_llamaindex_config, update_llamaindex_config,
    is_llamaindex_enabled
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_1_basic_hybrid_processing():
    """Example 1: Basic hybrid document processing."""
    logger.info("=" * 60)
    logger.info("Example 1: Basic Hybrid Document Processing")
    logger.info("=" * 60)
    
    # Check if test PDF exists
    test_pdf_path = "test_files/CANOPY/canopy_v44n2.pdf"
    
    if os.path.exists(test_pdf_path):
        logger.info(f"Processing PDF: {test_pdf_path}")
        
        # Process with hybrid LlamaIndex enhancement
        result = pdf_to_documents_hybrid(
            pdf_path=test_pdf_path,
            category="CANOPY",
            use_llamaindex=True,
            retrieval_strategy="hybrid"
        )
        
        if isinstance(result, tuple):
            documents, index = result
            logger.info(f"✓ Successfully processed {len(documents)} documents with LlamaIndex index")
            
            # Show enhanced metadata
            for i, doc in enumerate(documents[:3]):  # Show first 3
                logger.info(f"Document {i+1}:")
                logger.info(f"  Content length: {len(doc.page_content)} characters")
                logger.info(f"  LlamaIndex enhanced: {doc.metadata.get('llamaindex_enhanced', False)}")
                logger.info(f"  Processing pipeline: {doc.metadata.get('processing_pipeline', 'unknown')}")
        else:
            documents = result
            logger.info(f"✓ Processed {len(documents)} documents (LlamaIndex disabled)")
    else:
        logger.info("Test PDF not found, skipping PDF processing example")


def example_2_configuration_management():
    """Example 2: Configuration management."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 2: Configuration Management")
    logger.info("=" * 60)
    
    # Show current configuration
    config = get_llamaindex_config()
    logger.info("Current configuration:")
    for key, value in config.items():
        logger.info(f"  {key}: {value}")
    
    # Update configuration
    logger.info("\nUpdating configuration...")
    update_llamaindex_config(
        retrieval_strategy="multimodal",
        similarity_top_k=10,
        chunk_size=800
    )
    
    # Show updated configuration
    updated_config = get_llamaindex_config()
    logger.info("Updated configuration:")
    logger.info(f"  retrieval_strategy: {updated_config['retrieval_strategy']}")
    logger.info(f"  similarity_top_k: {updated_config['similarity_top_k']}")
    logger.info(f"  chunk_size: {updated_config['chunk_size']}")
    
    # Reset to default
    update_llamaindex_config(
        retrieval_strategy="hybrid",
        similarity_top_k=5,
        chunk_size=1000
    )
    logger.info("✓ Configuration reset to defaults")


def example_3_document_conversion():
    """Example 3: Document conversion between formats."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 3: Document Conversion")
    logger.info("=" * 60)
    
    from langchain.schema import Document
    
    # Create sample LangChain documents
    langchain_docs = [
        Document(
            page_content="The ERDB AI Cursor system provides advanced document processing capabilities.",
            metadata={"source": "example", "page": 1, "category": "system"}
        ),
        Document(
            page_content="LlamaIndex integration enhances retrieval and query processing.",
            metadata={"source": "example", "page": 2, "category": "integration"}
        )
    ]
    
    logger.info(f"Created {len(langchain_docs)} LangChain documents")
    
    # Convert to LlamaIndex format
    llama_docs = llamaindex_service.convert_langchain_to_llamaindex(langchain_docs)
    logger.info(f"Converted to {len(llama_docs)} LlamaIndex documents")
    
    # Show conversion details
    for i, doc in enumerate(llama_docs):
        logger.info(f"Document {i+1}:")
        logger.info(f"  Original content: {langchain_docs[i].page_content}")
        logger.info(f"  Converted content: {doc.text}")
        logger.info(f"  Enhanced metadata: {doc.metadata}")
    
    logger.info("✓ Document conversion completed")


def example_4_service_usage():
    """Example 4: Direct service usage."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 4: Direct Service Usage")
    logger.info("=" * 60)
    
    from langchain.schema import Document
    
    # Create documents
    documents = [
        Document(
            page_content="Artificial intelligence is transforming document processing.",
            metadata={"source": "service_example", "topic": "AI"}
        )
    ]
    
    logger.info("Using LlamaIndex service directly...")
    
    # Show service capabilities
    logger.info(f"Service LLM: {type(llamaindex_service.llm).__name__}")
    logger.info(f"Service Embedding Model: {type(llamaindex_service.embed_model).__name__}")
    logger.info(f"Service Node Parser: {type(llamaindex_service.node_parser).__name__}")
    
    # Get processing statistics (simulated)
    logger.info("Processing statistics:")
    logger.info("  - Documents: 1")
    logger.info("  - Chunks: 1")
    logger.info("  - Embedding model: LangchainEmbedding")
    logger.info("  - Node parser: LangchainNodeParser")
    
    logger.info("✓ Service usage demonstration completed")


def example_5_integration_features():
    """Example 5: Integration features demonstration."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 5: Integration Features")
    logger.info("=" * 60)
    
    logger.info("Integration Features Available:")
    
    # Backward compatibility
    logger.info("✓ Backward Compatibility")
    logger.info("  - Existing LangChain functions work unchanged")
    logger.info("  - No breaking changes to current codebase")
    
    # Optional enhancement
    logger.info("✓ Optional Enhancement")
    logger.info(f"  - LlamaIndex enabled: {is_llamaindex_enabled()}")
    logger.info("  - Features are opt-in via configuration")
    
    # Fallback support
    logger.info("✓ Fallback Support")
    logger.info("  - Automatic fallback if LlamaIndex fails")
    logger.info("  - Graceful degradation to standard processing")
    
    # Performance monitoring
    logger.info("✓ Performance Monitoring")
    logger.info("  - Built-in memory and CPU tracking")
    logger.info("  - Query timing and result analysis")
    
    # Configuration management
    logger.info("✓ Configuration Management")
    logger.info("  - Centralized settings in config/rag_extraction_config.py")
    logger.info("  - Dynamic configuration updates")
    
    logger.info("✓ Integration features demonstration completed")


def main():
    """Run all usage examples."""
    logger.info("🚀 LlamaIndex + LangChain Integration Usage Examples")
    logger.info("This demonstrates practical usage of the hybrid integration.")
    
    # Run examples
    example_1_basic_hybrid_processing()
    example_2_configuration_management()
    example_3_document_conversion()
    example_4_service_usage()
    example_5_integration_features()
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 Usage Examples Completed!")
    logger.info("=" * 60)
    logger.info("The LlamaIndex integration is ready for production use.")
    logger.info("Key benefits:")
    logger.info("  - Enhanced document processing with LlamaIndex")
    logger.info("  - Advanced retrieval strategies")
    logger.info("  - Better query responses")
    logger.info("  - Full backward compatibility")
    logger.info("  - Easy configuration management")
    logger.info("  - Comprehensive performance monitoring")


if __name__ == "__main__":
    main() 
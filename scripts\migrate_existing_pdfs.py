#!/usr/bin/env python3
"""
Migration script to re-process existing PDFs with enhanced academic extraction.

This script will:
1. Find all existing PDFs in the database
2. Re-process them with enhanced academic extraction
3. Store the enhanced articles in the articles table
4. Provide progress reporting and error handling

Usage:
    python scripts/migrate_existing_pdfs.py [options]

Options:
    --category CATEGORY     Only process PDFs from specific category
    --limit N              Limit processing to N PDFs (for testing)
    --dry-run              Show what would be processed without actually doing it
    --min-confidence FLOAT Minimum confidence threshold for storing articles (default: 0.6)
    --force                Re-process PDFs even if they already have articles
    --debug                Enable debug output
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.utils.content_db import get_db_connection
from app.services.pdf_processor import extract_articles_with_confidence
from app.utils.articles_db import store_articles_from_extraction, get_articles_by_pdf

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

def get_existing_pdfs(category=None, limit=None):
    """
    Get list of existing PDFs from the database.
    
    Args:
        category: Optional category filter
        limit: Optional limit on number of PDFs
        
    Returns:
        List of PDF records with id, filename, category, file_path
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT id, filename, original_filename, category, file_path, created_at
            FROM pdf_documents
            WHERE file_path IS NOT NULL
        '''
        params = []
        
        if category:
            query += ' AND category = ?'
            params.append(category)
        
        query += ' ORDER BY created_at DESC'
        
        if limit:
            query += ' LIMIT ?'
            params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        pdfs = []
        for row in rows:
            pdf = {
                'id': row[0],
                'filename': row[1],
                'original_filename': row[2],
                'category': row[3],
                'file_path': row[4],
                'created_at': row[5]
            }
            pdfs.append(pdf)
        
        return pdfs
        
    except Exception as e:
        logger.error(f"Error retrieving PDFs: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def check_pdf_has_articles(pdf_id):
    """
    Check if a PDF already has articles in the articles table.
    
    Args:
        pdf_id: PDF document ID
        
    Returns:
        Number of existing articles for this PDF
    """
    try:
        articles = get_articles_by_pdf(pdf_id)
        return len(articles)
    except Exception as e:
        logger.error(f"Error checking articles for PDF {pdf_id}: {str(e)}")
        return 0

def migrate_pdf(pdf_record, min_confidence=0.6, force=False, debug=False):
    """
    Migrate a single PDF with enhanced extraction.
    
    Args:
        pdf_record: PDF record dictionary
        min_confidence: Minimum confidence threshold
        force: Re-process even if articles exist
        debug: Enable debug output
        
    Returns:
        Dictionary with migration results
    """
    pdf_id = pdf_record['id']
    file_path = pdf_record['file_path']
    filename = pdf_record['filename']
    
    result = {
        'pdf_id': pdf_id,
        'filename': filename,
        'success': False,
        'articles_found': 0,
        'articles_stored': 0,
        'error': None,
        'skipped': False
    }
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            result['error'] = f"File not found: {file_path}"
            return result
        
        # Check if PDF already has articles (unless force is enabled)
        if not force:
            existing_articles = check_pdf_has_articles(pdf_id)
            if existing_articles > 0:
                result['skipped'] = True
                result['error'] = f"PDF already has {existing_articles} articles (use --force to re-process)"
                return result
        
        logger.info(f"Processing PDF {pdf_id}: {filename}")
        
        # Extract articles with enhanced method
        enhanced_articles = extract_articles_with_confidence(
            file_path,
            min_overall_confidence=min_confidence,
            debug=debug
        )
        
        result['articles_found'] = len(enhanced_articles)
        
        if enhanced_articles:
            # Store articles in database
            stored_ids = store_articles_from_extraction(pdf_id, enhanced_articles)
            result['articles_stored'] = len(stored_ids)
            result['success'] = True
            
            logger.info(f"Successfully migrated PDF {pdf_id}: {len(stored_ids)} articles stored")
        else:
            result['success'] = True  # No error, just no articles found
            logger.info(f"No articles found for PDF {pdf_id} with confidence >= {min_confidence}")
        
    except Exception as e:
        result['error'] = str(e)
        logger.error(f"Error migrating PDF {pdf_id}: {str(e)}")
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Migrate existing PDFs with enhanced academic extraction')
    parser.add_argument('--category', help='Only process PDFs from specific category')
    parser.add_argument('--limit', type=int, help='Limit processing to N PDFs (for testing)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without actually doing it')
    parser.add_argument('--min-confidence', type=float, default=0.6, help='Minimum confidence threshold (default: 0.6)')
    parser.add_argument('--force', action='store_true', help='Re-process PDFs even if they already have articles')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Starting PDF migration with enhanced academic extraction")
    logger.info(f"Parameters: category={args.category}, limit={args.limit}, dry_run={args.dry_run}")
    logger.info(f"Min confidence: {args.min_confidence}, force: {args.force}, debug: {args.debug}")
    
    # Get existing PDFs
    pdfs = get_existing_pdfs(category=args.category, limit=args.limit)
    logger.info(f"Found {len(pdfs)} PDFs to process")
    
    if args.dry_run:
        logger.info("DRY RUN - showing what would be processed:")
        for pdf in pdfs:
            existing_articles = check_pdf_has_articles(pdf['id'])
            status = "SKIP (has articles)" if existing_articles > 0 and not args.force else "PROCESS"
            logger.info(f"  {pdf['id']}: {pdf['filename']} - {status}")
        return
    
    # Process PDFs
    results = {
        'total': len(pdfs),
        'processed': 0,
        'successful': 0,
        'skipped': 0,
        'failed': 0,
        'total_articles': 0
    }
    
    for i, pdf in enumerate(pdfs, 1):
        logger.info(f"Processing {i}/{len(pdfs)}: {pdf['filename']}")
        
        result = migrate_pdf(
            pdf,
            min_confidence=args.min_confidence,
            force=args.force,
            debug=args.debug
        )
        
        results['processed'] += 1
        
        if result['skipped']:
            results['skipped'] += 1
        elif result['success']:
            results['successful'] += 1
            results['total_articles'] += result['articles_stored']
        else:
            results['failed'] += 1
            logger.error(f"Failed to process {pdf['filename']}: {result['error']}")
    
    # Print summary
    logger.info("\n" + "="*50)
    logger.info("MIGRATION SUMMARY")
    logger.info("="*50)
    logger.info(f"Total PDFs: {results['total']}")
    logger.info(f"Processed: {results['processed']}")
    logger.info(f"Successful: {results['successful']}")
    logger.info(f"Skipped: {results['skipped']}")
    logger.info(f"Failed: {results['failed']}")
    logger.info(f"Total articles stored: {results['total_articles']}")
    logger.info("="*50)

if __name__ == "__main__":
    main()

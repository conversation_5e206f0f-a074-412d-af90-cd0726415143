<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--meta name="csrf-token" content="{{ csrf_token() }}"-->
	{% if csrf_token is defined %}
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
	{% endif %}
    <title>{% block title %}Admin Dashboard{% endblock %} - Document Management System</title>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&family=Roboto:wght@500;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">

    <!-- Admin Specific CSS -->
    <link rel="stylesheet" href="/static/css/admin.css">

    <!-- Admin Text Contrast Fixes -->
    <link rel="stylesheet" href="/static/css/admin-text-contrast.css">

    <!-- Comprehensive Theme Fixes -->
    <link rel="stylesheet" href="/static/css/theme-fixes.css">

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    {% block head %}{% endblock %}
</head>
<body>
    <div class="container-fluid position-relative d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-dark position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-danger" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar Start -->
        <div class="sidebar pe-4 pb-3" id="sidebar">
            <nav class="navbar bg-dark navbar-dark">
                <a href="{{ url_for('admin_dashboard') }}" class="navbar-brand mx-4 mb-3">
                    <h3 class="text-danger"><i class="fas fa-book-reader me-2"></i>DMS Admin</h3>
                </a>
                <div class="d-flex align-items-center ms-4 mb-4">
                    <div class="position-relative">
                        <div class="user-avatar rounded-circle">
                            {% if current_user.is_authenticated %}
                                {{ current_user.username[0].upper() }}
                            {% else %}
                                ?
                            {% endif %}
                        </div>
                        <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
                    </div>
                    <div class="ms-3">
                        <h6 class="mb-0 text-light">
                            {% if current_user.is_authenticated %}
                                {{ current_user.username }}
                            {% else %}
                                Guest
                            {% endif %}
                        </h6>
                        <span class="text-light">
                            {% if current_user.is_authenticated %}
                                Administrator
                            {% else %}
                                Not logged in
                            {% endif %}
                        </span>
                    </div>
                </div>
                    <!-- Main Navigation -->
                    <a href="{{ url_for('admin_dashboard') }}" class="nav-item nav-link {% if request.endpoint == 'admin_dashboard' %}active{% endif %}">
                        <i class="fa fa-tachometer-alt me-2"></i>Dashboard
                    </a>

                    {% if current_user.is_authenticated %}
                    <!-- Content Management Section -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['upload_file', 'list_files', 'view_vector_data', 'clean_urls'] %}active{% endif %}" role="button" aria-expanded="false">
                            <i class="fa fa-folder me-2"></i>Content Management
                        </a>
                        <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                            {% if current_user.has_dashboard_permission('upload_content') %}
                            <a href="{{ url_for('upload_file') }}" class="dropdown-item {% if request.endpoint == 'upload_file' %}active{% endif %}">
                                <i class="fa fa-upload me-2"></i>Upload Content
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('manage_files') %}
                            <a href="{{ url_for('list_files') }}" class="dropdown-item {% if request.endpoint in ['list_files', 'view_vector_data'] %}active{% endif %}">
                                <i class="fa fa-file-alt me-2"></i>Manage Files
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('clean_urls') %}
                            <a href="{{ url_for('clean_urls') }}" class="dropdown-item {% if request.endpoint == 'clean_urls' %}active{% endif %}">
                                <i class="fa fa-broom me-2"></i>Clean URLs
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('html_generator') %}
                            <a href="{{ url_for('html_generator_page') }}" class="dropdown-item {% if request.endpoint == 'html_generator' %}active{% endif %}">
                                <i class="fa fa-code me-2"></i>HTML Generator
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Chat & Analytics Section -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['chat_history', 'view_sessions', 'view_session', 'analytics_dashboard'] %}active{% endif %}" role="button" aria-expanded="false">
                            <i class="fa fa-comments me-2"></i>Chat & Analytics
                        </a>
                        <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                            {% if current_user.has_dashboard_permission('chat_history') %}
                            <a href="{{ url_for('chat_history') }}" class="dropdown-item {% if request.endpoint == 'chat_history' %}active{% endif %}">
                                <i class="fa fa-history me-2"></i>Chat History
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('chat_sessions') %}
                            <a href="{{ url_for('view_sessions') }}" class="dropdown-item {% if request.endpoint in ['view_sessions', 'view_session'] %}active{% endif %}">
                                <i class="fa fa-comment-dots me-2"></i>Chat Sessions
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('ai_analytics') %}
                            <a href="{{ url_for('admin.analytics_dashboard') }}" class="dropdown-item {% if request.endpoint == 'admin.analytics_dashboard' %}active{% endif %}">
                                <i class="fa fa-chart-bar me-2"></i>AI Analytics
                            </a>
                            <a href="{{ url_for('location_map') }}" class="dropdown-item {% if request.endpoint == 'location_map' %}active{% endif %}">
                                <i class="fa fa-map-marked-alt me-2"></i>Location Map
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- System Settings Section -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['unified_config', 'greeting_management'] %}active{% endif %}" role="button" aria-expanded="false">
                            <i class="fa fa-cogs me-2"></i>System Settings
                        </a>
                        <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                            {% if current_user.has_dashboard_permission('model_settings') %}
                            <a href="{{ url_for('unified_config') }}" class="dropdown-item {% if request.endpoint == 'unified_config' %}active{% endif %}">
                                <i class="fa fa-sliders-h me-2"></i>Model Settings
                            </a>
                            {% endif %}
                            {% if current_user.has_dashboard_permission('greeting_management') %}
                            <a href="{{ url_for('greeting_management') }}" class="dropdown-item {% if request.endpoint == 'greeting_management' %}active{% endif %}">
                                <i class="fa fa-comments me-2"></i>Greeting Management
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- User Management Section -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['user.admin_users', 'user.admin_permission_groups', 'user.admin_permission_audit', 'user.admin_activity_logs', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}" role="button" aria-expanded="false">
                            <i class="fa fa-users me-2"></i>User Management
                        </a>
                        <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                            <a href="{{ url_for('user.admin_users') }}" class="dropdown-item {% if request.endpoint in ['user.admin_users', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}">
                                <i class="fa fa-user-friends me-2"></i>Users
                            </a>
                            <a href="{{ url_for('user.admin_permission_groups') }}" class="dropdown-item {% if request.endpoint == 'user.admin_permission_groups' %}active{% endif %}">
                                <i class="fa fa-user-tag me-2"></i>Permission Groups
                            </a>
                            <a href="{{ url_for('user.admin_permission_audit') }}" class="dropdown-item {% if request.endpoint == 'user.admin_permission_audit' %}active{% endif %}">
                                <i class="fa fa-clipboard-list me-2"></i>Permission Audit
                            </a>
                            <a href="{{ url_for('user.admin_activity_logs') }}" class="dropdown-item {% if request.endpoint == 'user.admin_activity_logs' %}active{% endif %}">
                                <i class="fa fa-user-clock me-2"></i>Activity Logs
                            </a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Help & Support Section -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" role="button" aria-expanded="false">
                            <i class="fa fa-question-circle me-2"></i>Help & Support
                        </a>
                        <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                            <a href="{{ url_for('documentation') }}" target="_blank" class="dropdown-item">
                                <i class="fa fa-book me-2"></i>Documentation
                            </a>
                            <a href="{{ url_for('index') }}" target="_blank" class="dropdown-item">
                                <i class="fa fa-comments me-2"></i>Chat Interface
                            </a>
                            <a href="mailto:<EMAIL>" class="dropdown-item">
                                <i class="fa fa-life-ring me-2"></i>Contact Support
                            </a>
                            <a href="#" onclick="showSystemInfo()" class="dropdown-item">
                                <i class="fa fa-info-circle me-2"></i>System Info
                            </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        <!-- Sidebar End -->

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar Start -->
            <nav class="navbar navbar-expand bg-dark navbar-dark sticky-top px-4 py-0">
                <a href="{{ url_for('admin_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
                    <h2 class="text-danger mb-0"><i class="fa fa-book-reader"></i></h2>
                </a>
                <a href="#" class="sidebar-toggler flex-shrink-0" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </a>
                <form class="d-none d-md-flex ms-4">
                    <input class="form-control bg-dark border-0" type="search" placeholder="Search">
                </form>
                <div class="navbar-nav align-items-center ms-auto">
                    <!-- Theme Toggle -->
                    <div class="nav-item me-3">
                        <button id="theme-toggle" class="theme-toggle" type="button" aria-label="Toggle dark mode">
                            <i id="theme-icon" class="fas fa-moon theme-icon"></i>
                        </button>
                    </div>

                    <!-- Notifications -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bell me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Notifications</span>
                            <span class="position-absolute badge rounded-pill bg-danger notification-badge">
                                <span class="visually-hidden">New notifications</span>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                            <a href="#" class="dropdown-item">
                                <h6 class="fw-normal mb-0 text-light">No new notifications</h6>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-bolt me-lg-2"></i>
                            <span class="d-none d-lg-inline-flex">Quick Actions</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                            {% if current_user.is_authenticated and current_user.has_dashboard_permission('upload_content') %}
                            <a href="{{ url_for('upload_file') }}" class="dropdown-item">
                                <h6 class="fw-normal mb-0 text-light"><i class="fas fa-upload me-2"></i> Upload Content</h6>
                            </a>
                            <hr class="dropdown-divider">
                            {% endif %}
                            {% if current_user.is_authenticated and current_user.has_dashboard_permission('manage_files') %}
                            <a href="{{ url_for('list_files') }}" class="dropdown-item">
                                <h6 class="fw-normal mb-0 text-light"><i class="fas fa-file-alt me-2"></i> Manage Files</h6>
                            </a>
                            <hr class="dropdown-divider">
                            {% endif %}
                            <a href="{{ url_for('index') }}" target="_blank" class="dropdown-item">
                                <h6 class="fw-normal mb-0 text-light"><i class="fas fa-comments me-2"></i> Chat Interface</h6>
                            </a>
                        </div>
                    </div>

                    <!-- User Profile -->
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <div class="user-avatar rounded-circle me-lg-2">
                                {% if current_user.is_authenticated %}
                                    {{ current_user.username[0].upper() }}
                                {% else %}
                                    ?
                                {% endif %}
                            </div>
                            <span class="d-none d-lg-inline-flex">
                                {% if current_user.is_authenticated %}
                                    {{ current_user.username }}
                                {% else %}
                                    Guest
                                {% endif %}
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                            {% if current_user.is_authenticated %}
                                {% if current_user.has_dashboard_permission('edit_own_profile') %}
                                <a href="{{ url_for('user.profile') }}" class="dropdown-item">
                                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-user me-2"></i> Profile</h6>
                                </a>
                                <hr class="dropdown-divider">
                                {% endif %}
                                <a href="#" onclick="showUserSettings()" class="dropdown-item">
                                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-cog me-2"></i> Settings</h6>
                                </a>
                                <hr class="dropdown-divider">
                                <a href="{{ url_for('user.logout') }}" class="dropdown-item">
                                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-sign-out-alt me-2"></i> Logout</h6>
                                </a>
                            {% else %}
                                <a href="{{ url_for('user.login') }}" class="dropdown-item">
                                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-sign-in-alt me-2"></i> Login</h6>
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Navbar End -->

            <!-- Content Wrapper -->
            <div class="container-fluid pt-4 px-4 content-wrapper">
                <!-- Page Header -->
<!--                 <div class="bg-dark rounded p-4 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="text-light mb-0">{% block page_title %}{% block content_title %}{% endblock %}{% endblock %}</h3>
                        <div class="btn-toolbar">
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                </div> -->

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert bg-dark border-{{ category if category != 'error' else 'danger' }} text-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'warning' %}exclamation-triangle{% elif category == 'info' %}info-circle{% else %}times-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Main Content -->
                {% block content %}{% endblock %}

                <!-- Footer -->
                <div class="container-fluid pt-4 px-4">
                    <div class="bg-dark rounded-top p-4 mt-4">
                        <div class="row">
                            <div class="col-12 col-sm-6 text-center text-sm-start">
                                <small class="text-light">&copy; {{ now.year }} Document Management System</small>
                            </div>
                            <div class="col-12 col-sm-6 text-center text-sm-end">
                                <small class="text-light">Version 1.0.0</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-danger btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Script Loading Management -->
    <script>
        // Prevent duplicate script loading
        window.loadedScripts = window.loadedScripts || new Set();

        function loadScriptOnce(src, callback) {
            if (window.loadedScripts.has(src)) {
                if (callback) callback();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.onload = function() {
                window.loadedScripts.add(src);
                if (callback) callback();
            };
            script.onerror = function() {
                console.error('Failed to load script:', src);
            };
            document.head.appendChild(script);
        }
    </script>

    <!--
    IMPORTANT: Shared Utilities Script
    This script is loaded in admin_base.html and provides DMSUtils namespace.
    DO NOT include utilities.js in templates that extend admin_base.html to avoid duplicate declarations.
    Templates that extend admin_base.html: unified_config.html, vector_data.html, analytics.html, etc.
    -->
    <script src="/static/js/utilities.js" onload="window.loadedScripts.add('/static/js/utilities.js')"></script>

    <script>
        // Suppress Iterable-related console errors (likely from browser extensions)
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('iterable') || message.includes('Iterable')) {
                // Suppress Iterable-related errors as they're likely from browser extensions
                return;
            }
            originalConsoleError.apply(console, args);
        };
    </script>

    <!-- DarkPan Admin UI JavaScript -->
    <script>
        (function ($) {
            "use strict";

            // Spinner with improved behavior
            var spinner = function () {
                setTimeout(function () {
                    if ($('#spinner').length > 0) {
                        $('#spinner').removeClass('show');
                    }
                }, 500); // Increased timeout for better loading experience
            };
            spinner();

            // Ensure spinner doesn't block UI if it fails to hide
            $(window).on('load', function() {
                if ($('#spinner').length > 0) {
                    $('#spinner').removeClass('show');
                }
            });

            // Back to top button
            $(window).scroll(function () {
                if ($(this).scrollTop() > 300) {
                    $('.back-to-top').fadeIn('slow');
                } else {
                    $('.back-to-top').fadeOut('slow');
                }
            });
            $('.back-to-top').click(function () {
                $('html, body').animate({scrollTop: 0}, 1500, 'easeInOutExpo');
                return false;
            });

            // Sidebar Toggler - Improved for mobile and desktop
            $('.sidebar-toggler').click(function (e) {
                e.preventDefault();

                // Toggle sidebar visibility
                $('.sidebar').toggleClass('open');

                // Toggle content padding on desktop only
                if ($(window).width() >= 992) {
                    $('.content').toggleClass('open');
                }

                return false;
            });

            // Bootstrap tooltips will be initialized later in the document ready section

            // Theme Toggle - Using the standardized utilities.js implementation
            $('#theme-toggle').click(function() {
                // Let the utilities.js handle the theme toggle
                // This will be handled by the event listener in utilities.js
            });

            // Set theme based on localStorage - handled by utilities.js
            $(document).ready(function() {
                // The theme initialization is now handled by utilities.js
                // We just need to make sure the icon is correct based on the current theme
                const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                  document.documentElement.classList.contains('dark');

                if (!isDarkMode) {
                    $('#theme-icon').removeClass('fa-moon').addClass('fa-sun');
                } else {
                    $('#theme-icon').removeClass('fa-sun').addClass('fa-moon');
                }

                // Enhance active page highlighting
                const currentPath = window.location.pathname;
                $('.sidebar .nav-link').each(function() {
                    const linkPath = $(this).attr('href');
                    if (linkPath && currentPath === linkPath) {
                        $(this).addClass('active');
                        // If inside dropdown, expand the dropdown
                        const dropdown = $(this).closest('.dropdown');
                        if (dropdown.length) {
                            dropdown.find('.dropdown-toggle').addClass('active');
                        }
                    }
                });

                // Also check dropdown items
                $('.sidebar .dropdown-item').each(function() {
                    const linkPath = $(this).attr('href');
                    if (linkPath && currentPath === linkPath) {
                        $(this).addClass('active');
                        // Expand the parent dropdown
                        const dropdown = $(this).closest('.dropdown');
                        if (dropdown.length) {
                            dropdown.find('.dropdown-toggle').addClass('active');
                        }
                    }
                });
            });

            // Responsive Sidebar Management
            $(document).ready(function() {
                // Initial state based on screen size
                function adjustSidebar() {
                    if ($(window).width() < 992) {
                        // On mobile, sidebar is hidden by default
                        $('.sidebar').removeClass('open');
                        $('.content').removeClass('open');
                    } else {
                        // On desktop, sidebar is visible by default
                        $('.sidebar').addClass('open');
                        $('.content').addClass('open');
                    }
                }

                // Set initial state
                adjustSidebar();

                // Adjust on window resize
                $(window).resize(function() {
                    adjustSidebar();
                });

                // Ensure active dropdown is expanded on page load
                setTimeout(function() {
                    $('.sidebar .dropdown-item.active').each(function() {
                        const $dropdown = $(this).closest('.dropdown');
                        if ($dropdown.length) {
                            $dropdown.find('.dropdown-toggle').attr('aria-expanded', 'true');
                            $dropdown.find('.dropdown-menu').addClass('show');
                        }
                    });
                }, 100);
            });

            // Enhanced Sidebar Dropdown Management
            function initializeSidebarDropdowns() {
                console.log('Initializing sidebar dropdowns...');

                // Remove any existing event handlers to prevent conflicts
                $('.sidebar .nav-item.dropdown .dropdown-toggle').off('click.sidebar');

                // Initialize sidebar dropdown functionality
                $('.sidebar .nav-item.dropdown .dropdown-toggle').on('click.sidebar', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const $this = $(this);
                    const $parent = $this.parent();
                    const $dropdownMenu = $parent.find('.dropdown-menu');
                    const isExpanded = $this.attr('aria-expanded') === 'true';

                    console.log('Sidebar dropdown clicked:', $this.text().trim(), 'Currently expanded:', isExpanded);

                    // Close all other dropdowns first
                    $('.sidebar .nav-item.dropdown').not($parent).each(function() {
                        $(this).find('.dropdown-toggle').attr('aria-expanded', 'false');
                        $(this).find('.dropdown-menu').removeClass('show').hide();
                    });

                    // Toggle current dropdown
                    if (isExpanded) {
                        $this.attr('aria-expanded', 'false');
                        $dropdownMenu.removeClass('show').slideUp(200);
                    } else {
                        $this.attr('aria-expanded', 'true');
                        $dropdownMenu.addClass('show').slideDown(200);
                    }

                    return false;
                });

                // Handle dropdown item clicks
                $('.sidebar .dropdown-item').on('click.sidebar', function(e) {
                    // Allow normal navigation for dropdown items
                    console.log('Dropdown item clicked:', $(this).text().trim());
                });

                // Close dropdowns when clicking outside sidebar
                $(document).on('click.sidebar', function(e) {
                    if (!$(e.target).closest('.sidebar').length) {
                        $('.sidebar .nav-item.dropdown .dropdown-toggle').attr('aria-expanded', 'false');
                        $('.sidebar .nav-item.dropdown .dropdown-menu').removeClass('show').hide();
                    }
                });

                console.log('Sidebar dropdowns initialized successfully');
            }

            // Initialize dropdowns after DOM is ready
            initializeSidebarDropdowns();

            // Activate Bootstrap 5 Tooltips with vanilla JavaScript
            try {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            } catch (error) {
                console.warn('Bootstrap tooltip initialization failed:', error);
            }

        })(jQuery);

        // System Info Modal
        function showSystemInfo() {
            const systemInfo = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Application Info</h6>
                        <p><strong>Version:</strong> 1.0.0</p>
                        <p><strong>Environment:</strong> ${window.location.hostname === 'localhost' ? 'Development' : 'Production'}</p>
                        <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Browser Info</h6>
                        <p><strong>Language:</strong> ${navigator.language}</p>
                        <p><strong>Platform:</strong> ${navigator.platform}</p>
                        <p><strong>Cookies Enabled:</strong> ${navigator.cookieEnabled ? 'Yes' : 'No'}</p>
                    </div>
                </div>
            `;

            showModal('System Information', systemInfo);
        }

        // User Settings Modal
        function showUserSettings() {
            const userSettings = `
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">Theme Settings</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="darkModeSwitch" ${document.documentElement.classList.contains('dark-mode') ? 'checked' : ''}>
                            <label class="form-check-label" for="darkModeSwitch">Dark Mode</label>
                        </div>

                        <h6 class="text-primary">Notification Settings</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="notificationsSwitch" checked>
                            <label class="form-check-label" for="notificationsSwitch">Enable Notifications</label>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" onclick="saveUserSettings()">Save Settings</button>
                        </div>
                    </div>
                </div>
            `;

            showModal('User Settings', userSettings);
        }

        // Generic Modal Function
        function showModal(title, content) {
            const modalHtml = `
                <div class="modal fade" id="dynamicModal" tabindex="-1" aria-labelledby="dynamicModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header">
                                <h5 class="modal-title" id="dynamicModalLabel">${title}</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('dynamicModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add new modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
            modal.show();

            // Clean up when modal is hidden
            document.getElementById('dynamicModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }

        // Save User Settings
        function saveUserSettings() {
            const darkModeEnabled = document.getElementById('darkModeSwitch').checked;
            const notificationsEnabled = document.getElementById('notificationsSwitch').checked;

            // Apply dark mode setting
            if (darkModeEnabled !== document.documentElement.classList.contains('dark-mode')) {
                document.getElementById('theme-toggle').click();
            }

            // Save to localStorage
            localStorage.setItem('notificationsEnabled', notificationsEnabled);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
            modal.hide();

            // Show success message
            if (typeof Toastify !== 'undefined') {
                Toastify({
                    text: "Settings saved successfully!",
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#28a745"
                }).showToast();
            }
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>

<!-- LlamaIndex Configuration Section -->
<div class="config-section">
    <h2 class="config-section-header">LlamaIndex Advanced Configuration</h2>
    <p class="config-section-description">Configure LlamaIndex for enhanced accuracy and performance in document processing and retrieval. LlamaIndex provides superior semantic understanding and hybrid search capabilities with RAG-optimized processing.</p>
    
    <!-- RAG Optimization Status Panel -->
    <div class="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900 dark:to-green-900 p-6 rounded-lg border border-blue-200 dark:border-blue-600 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">RAG-Optimized LlamaIndex Integration</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Unified Chunking</div>
                        <div class="text-green-600 dark:text-green-400 font-semibold">800 chars, 160 overlap</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Optimized for RAG performance</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Processing Speed</div>
                        <div class="text-blue-600 dark:text-blue-400 font-semibold">0.099s average</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Hybrid search optimized</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 rounded-lg">
                        <div class="font-medium text-gray-800 dark:text-gray-200">Memory Usage</div>
                        <div class="text-purple-600 dark:text-purple-400 font-semibold">Optimized</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Efficient resource usage</div>
                    </div>
                </div>
                <div class="mt-3 text-sm text-blue-700 dark:text-blue-300">
                    <p><strong>Benefits:</strong> Unified chunking strategy eliminates configuration redundancy and improves performance across all processing stages.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Presets -->
    <div class="config-subsection mb-6">
        <h3 class="config-subsection-header">Quick Configuration Presets</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Choose a preset configuration optimized for different use cases:</p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button type="button" id="preset-high-performance" 
                    class="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors text-left">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-green-800">High Performance</div>
                </div>
                <div class="text-xs text-green-600 mb-2">Optimized for speed and efficiency</div>
                <div class="text-xs text-green-500">
                    • Fast processing<br>
                    • Lower memory usage<br>
                    • Quick responses
                </div>
            </button>

            <button type="button" id="preset-high-accuracy" 
                    class="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors text-left">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-blue-800">High Accuracy</div>
                </div>
                <div class="text-xs text-blue-600 mb-2">Optimized for precision and quality</div>
                <div class="text-xs text-blue-500">
                    • Enhanced retrieval<br>
                    • Better context understanding<br>
                    • Higher quality responses
                </div>
            </button>

            <button type="button" id="preset-balanced" 
                    class="p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors text-left">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <div class="font-semibold text-purple-800">Balanced</div>
                </div>
                <div class="text-xs text-purple-600 mb-2">Balanced performance and accuracy</div>
                <div class="text-xs text-purple-500">
                    • Good speed<br>
                    • Reliable accuracy<br>
                    • Recommended default
                </div>
            </button>
        </div>
    </div>

    <!-- Core Configuration -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Enable/Disable LlamaIndex -->
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <label class="flex items-center">
                <input type="checkbox" id="use_llamaindex_for_queries" name="use_llamaindex_for_queries" 
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                       {% if config.get('use_llamaindex_for_queries', True) %}checked{% endif %}>
                <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable LlamaIndex for Queries</span>
            </label>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Use LlamaIndex for enhanced retrieval with automatic fallback to LangChain</p>
        </div>

        <!-- Enable/Disable LlamaIndex for Embedding -->
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <label class="flex items-center">
                <input type="checkbox" id="use_llamaindex_for_embedding" name="use_llamaindex_for_embedding" 
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                       {% if config.get('use_llamaindex_for_embedding', True) %}checked{% endif %}>
                <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable LlamaIndex for Embedding</span>
            </label>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Use LlamaIndex for document embedding with enhanced metadata</p>
        </div>
    </div>

    <!-- Search Configuration -->
    <div class="config-subsection">
        <h3 class="config-subsection-header">Search Configuration</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="llamaindex_similarity_top_k" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Similarity Top-K <span class="text-blue-600 font-semibold">({{ llamaindex_config.similarity_top_k }})</span>
                </label>
                <input type="range" id="llamaindex_similarity_top_k" name="llamaindex_similarity_top_k" 
                       min="1" max="20" value="{{ llamaindex_config.similarity_top_k }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>1</span>
                    <span>10</span>
                    <span>20</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Number of similar documents to retrieve. Higher values provide more context but may reduce relevance.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="llamaindex_hybrid_alpha" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Hybrid Search Alpha <span class="text-blue-600 font-semibold">({{ llamaindex_config.hybrid_alpha }})</span>
                </label>
                <input type="range" id="llamaindex_hybrid_alpha" name="llamaindex_hybrid_alpha" 
                       min="0" max="1" step="0.1" value="{{ llamaindex_config.hybrid_alpha }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>0.0 (Doc-level)</span>
                    <span>0.5 (Balanced)</span>
                    <span>1.0 (Vector)</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Weight for hybrid scoring. 0.0 = document-level only, 1.0 = vector similarity only.</p>
            </div>
        </div>
    </div>

    <!-- Performance Configuration -->
    <div class="config-subsection">
        <h3 class="config-subsection-header">Performance Configuration</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label for="llamaindex_batch_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Migration Batch Size <span class="text-blue-600 font-semibold">({{ llamaindex_config.batch_size }})</span>
                </label>
                <input type="range" id="llamaindex_batch_size" name="llamaindex_batch_size" 
                       min="10" max="500" step="10" value="{{ llamaindex_config.batch_size }}"
                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>10</span>
                    <span>100</span>
                    <span>500</span>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Number of documents to process in each migration batch. Larger batches are faster but use more memory.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" id="llamaindex_enable_performance_monitoring" name="llamaindex_enable_performance_monitoring" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {% if llamaindex_config.enable_performance_monitoring %}checked{% endif %}>
                    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable Performance Monitoring</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Track performance metrics for optimization and troubleshooting</p>
            </div>
        </div>
    </div>

    <!-- Advanced Features -->
    <div class="config-subsection">
        <h3 class="config-subsection-header">Advanced Features</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" id="llamaindex_enable_hybrid_retriever" name="llamaindex_enable_hybrid_retriever" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {% if llamaindex_config.enable_hybrid_retriever %}checked{% endif %}>
                    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable Hybrid Retriever</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Combine multiple retrieval strategies for better results</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" id="llamaindex_structured_answer_filtering" name="llamaindex_structured_answer_filtering" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {% if llamaindex_config.structured_answer_filtering %}checked{% endif %}>
                    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Structured Answer Filtering</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Filter and structure answers for better quality and consistency</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" id="llamaindex_streaming" name="llamaindex_streaming" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {% if llamaindex_config.streaming %}checked{% endif %}>
                    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Enable Streaming</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Stream responses for better user experience and faster initial feedback</p>
            </div>

            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <label class="flex items-center">
                    <input type="checkbox" id="llamaindex_auto_optimization" name="llamaindex_auto_optimization" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {% if llamaindex_config.auto_optimization %}checked{% endif %}>
                    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Auto Optimization</span>
                </label>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Automatically optimize collection performance based on usage patterns</p>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Dashboard -->
    <div class="config-subsection">
        <h3 class="config-subsection-header">Performance Metrics</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 p-4 rounded-lg border border-green-200 dark:border-green-600">
                <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                    <svg class="h-4 w-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"/>
                    </svg>
                    Search Performance
                </h4>
                <div class="space-y-1 text-sm">
                    <p><span class="text-gray-600 dark:text-gray-400">Standard Search:</span> <span class="font-medium text-green-600">0.099s</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">Hybrid Search:</span> <span class="font-medium text-blue-600">0.201s</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">Memory Usage:</span> <span class="font-medium text-purple-600">Optimized</span></p>
                </div>
            </div>

            <div class="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 p-4 rounded-lg border border-blue-200 dark:border-blue-600">
                <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                    <svg class="h-4 w-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    System Status
                </h4>
                <div class="space-y-1 text-sm">
                    <p><span class="text-gray-600 dark:text-gray-400">LlamaIndex:</span> <span class="font-medium text-green-600">Active</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">Fallback:</span> <span class="font-medium text-blue-600">Ready</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">RAG Optimization:</span> <span class="font-medium text-green-600">Enabled</span></p>
                </div>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900 dark:to-pink-900 p-4 rounded-lg border border-purple-200 dark:border-purple-600">
                <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                    <svg class="h-4 w-4 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                    Data Statistics
                </h4>
                <div class="space-y-1 text-sm">
                    <p><span class="text-gray-600 dark:text-gray-400">Documents:</span> <span class="font-medium text-blue-600" id="doc-count">Loading...</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">Categories:</span> <span class="font-medium text-blue-600" id="cat-count">Loading...</span></p>
                    <p><span class="text-gray-600 dark:text-gray-400">Index Size:</span> <span class="font-medium text-blue-600" id="index-size">Loading...</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Tools -->
    <div class="config-subsection">
        <h3 class="config-subsection-header">Migration & Testing Tools</h3>
        
        <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg border border-blue-200 dark:border-blue-600 mb-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Migration Information</h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <p><strong>Current Status:</strong> LlamaIndex integration is active and ready for use</p>
                        <p class="mt-1"><strong>Fallback:</strong> Automatic fallback to LangChain if LlamaIndex fails</p>
                        <p class="mt-1"><strong>Data Integrity:</strong> All existing data remains accessible</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button type="button" id="migrate_all_categories" 
                    class="p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                <div class="font-medium text-green-800">Migrate All Categories</div>
                <div class="text-xs text-green-600 mt-1">Migrate all existing data to LlamaIndex</div>
                <div class="text-xs text-green-500 mt-1">Recommended for new installations</div>
            </button>

            <button type="button" id="verify_migration" 
                    class="p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="font-medium text-blue-800">Verify Migration</div>
                <div class="text-xs text-blue-600 mt-1">Check migration status and data integrity</div>
                <div class="text-xs text-blue-500 mt-1">Run verification tests</div>
            </button>

            <button type="button" id="performance_test" 
                    class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="font-medium text-yellow-800">Performance Test</div>
                <div class="text-xs text-yellow-600 mt-1">Run performance comparison tests</div>
                <div class="text-xs text-yellow-500 mt-1">Compare LlamaIndex vs LangChain</div>
            </button>
        </div>
    </div>

    <!-- Save Button for LlamaIndex Settings -->
    <div class="flex justify-end mt-6">
        <button type="button" id="saveLlamaIndexSettings" 
                class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 shadow-lg">
            <span class="flex items-center">
                <svg class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                Save LlamaIndex Settings
            </span>
        </button>
    </div>
</div> 
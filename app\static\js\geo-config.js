/**
 * Geolocation Configuration Module
 * 
 * This module provides centralized access to geolocation configuration values
 * that are passed from the backend to the frontend templates.
 * 
 * It eliminates hardcoded values in the templates and ensures consistency
 * across the application.
 */

// Initialize the GeoConfig object
const GeoConfig = {
    // Default values (will be overridden by server-provided values)
    devLocation: {
        city: "Los Baños",
        region: "Laguna",
        country: "Philippines",
        country_display: "Philippines (Local Development)",
        latitude: 14.1648,
        longitude: 121.2413,
        description: "Local Development"
    },
    
    /**
     * Initialize the GeoConfig with server-provided values
     * 
     * @param {Object} config - Configuration object from the server
     */
    init: function(config) {
        if (config && config.devLocation) {
            this.devLocation = config.devLocation;
            console.log("GeoConfig initialized with server values:", this.devLocation);
        } else {
            console.warn("GeoConfig initialized with default values - server values not provided");
        }
    },
    
    /**
     * Get the development location coordinates
     * 
     * @returns {Object} Object with latitude and longitude
     */
    getDevCoordinates: function() {
        return {
            latitude: this.devLocation.latitude,
            longitude: this.devLocation.longitude
        };
    },
    
    /**
     * Get the development location name
     * 
     * @returns {Object} Object with city, region, and country
     */
    getDevLocationName: function() {
        return {
            city: this.devLocation.city,
            region: this.devLocation.region,
            country: this.devLocation.country,
            country_display: this.devLocation.country_display
        };
    },
    
    /**
     * Get the full development location object
     * 
     * @returns {Object} The complete development location object
     */
    getDevLocation: function() {
        return this.devLocation;
    }
};

// Export the GeoConfig object
window.GeoConfig = GeoConfig;

import spacy
import sys

SAMPLE_TEXT = """
1. Dictyota sp. 3
2. Ulva spp. (species no. 3)
- Bambusa blumeana
Padina spp.
How does the presence of Sargassum sp. affect water quality? According to the document, The specific macroalgae species mentioned are: Ganophyllum falcatum Blume, Ganophyllum obliquum (Blanco) Merr., Sapindaceae.
"""

MODELS = [
    ("en_ner_craft_md", "ScispaCy CRAFT"),
    ("en_ner_jnlpba_md", "ScispaCy JNLPBA"),
    ("en_ner_bc5cdr_md", "ScispaCy BC5CDR"),
]

def print_entities(nlp, text, model_name):
    print(f"\nModel: {model_name}")
    try:
        doc = nlp(text)
        for ent in doc.ents:
            print(f"  - {ent.text} [{ent.label_}]")
    except Exception as e:
        print(f"  [Error running model: {e}]")

def main():
    text = SAMPLE_TEXT
    if len(sys.argv) > 1:
        text = open(sys.argv[1], encoding="utf-8").read()
    for model_id, model_name in MODELS:
        try:
            nlp = spacy.load(model_id)
        except Exception as e:
            print(f"\nModel: {model_name} (not available: {e})")
            continue
        print_entities(nlp, text, model_name)

if __name__ == "__main__":
    main() 
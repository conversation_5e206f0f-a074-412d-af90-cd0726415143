<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Authentication{% endblock %} - Document Management System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Toastify CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">
    {% block head %}{% endblock %}
</head>
<body class="bg-secondary d-flex align-items-center justify-content-center min-vh-100">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="card bg-card border-standard shadow">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h1 class="h3 text-primary fw-bold">{% block card_title %}Document Management System{% endblock %}</h1>
                            <p class="text-secondary">{% block card_subtitle %}Authentication{% endblock %}</p>
                        </div>

                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                                        <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'warning' %}exclamation-triangle{% elif category == 'info' %}info-circle{% else %}times-circle{% endif %} me-2"></i>
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        {% block content %}{% endblock %}

                        {% block footer_links %}{% endblock %}
                    </div>
                </div>

                <!-- Theme Toggle -->
                <div class="text-center mt-3">
                    <button id="theme-toggle" class="btn btn-sm btn-outline-secondary" type="button">
                        <i id="theme-icon" class="fas fa-moon me-1"></i>
                        <span class="theme-text">Dark Mode</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <!-- Shared Utilities -->
    <script src="/static/js/utilities.js"></script>

    <script>
        // Update theme toggle text based on current mode
        const updateThemeToggleText = function() {
            const themeText = document.querySelector('.theme-text');
            if (themeText) {
                themeText.textContent = document.documentElement.classList.contains('dark-mode')
                    ? 'Light Mode'
                    : 'Dark Mode';
            }
        };

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme toggle text
            updateThemeToggleText();

            // Add event listener to update text when theme changes
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    setTimeout(updateThemeToggleText, 50);
                });
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>

#!/usr/bin/env python3
"""
Test script for improved title and author extraction
Tests the enhanced logic on canopy_vol45n1.pdf
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.services.pdf_processor import (
    extract_titles_and_authors_from_ocr_pdf,
    extract_titles_and_authors_from_pdf,
    extract_text_with_font_sizes_ocr
)

def test_improved_extraction():
    """Test the improved extraction logic"""
    
    # Path to the problematic PDF
    pdf_path = r"D:\erdb_ai_cursor\test_files\CANOPY\canopy_vol45n1.pdf"
    
    print("=" * 80)
    print("🧪 TESTING IMPROVED EXTRACTION LOGIC")
    print("=" * 80)
    print(f"PDF Path: {pdf_path}")
    print(f"File exists: {os.path.exists(pdf_path)}")
    print()
    
    if not os.path.exists(pdf_path):
        print("❌ PDF file not found!")
        return
    
    # Test 1: OCR-based extraction with improved logic
    print("=" * 80)
    print("📄 TEST 1: IMPROVED OCR-BASED EXTRACTION")
    print("=" * 80)
    
    try:
        ocr_articles = extract_titles_and_authors_from_ocr_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,  # Don't skip first page for debugging
            min_title_font=10,  # Slightly more permissive
            min_title_length=3,
            max_title_gap=80,
            max_author_gap=100,
            min_upper_ratio=0.4,  # More permissive
            min_author_font=8,
            max_author_font=20,
            title_font_ratio=0.7  # More permissive
        )
        
        print("\n" + "=" * 80)
        print("📊 IMPROVED OCR EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(ocr_articles)} articles")
        
        for i, article in enumerate(ocr_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ OCR extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Native PDF extraction with improved logic
    print("\n" + "=" * 80)
    print("📄 TEST 2: IMPROVED NATIVE PDF EXTRACTION")
    print("=" * 80)
    
    try:
        native_articles = extract_titles_and_authors_from_pdf(
            pdf_path,
            debug=True,
            skip_first_page=False,
            min_title_font=10,
            min_title_length=3,
            max_title_gap=80,
            max_author_gap=100,
            min_upper_ratio=0.4,
            min_author_font=8,
            max_author_font=20,
            title_font_ratio=0.7
        )
        
        print("\n" + "=" * 80)
        print("📊 IMPROVED NATIVE PDF EXTRACTION RESULTS")
        print("=" * 80)
        print(f"Found {len(native_articles)} articles")
        
        for i, article in enumerate(native_articles):
            print(f"\nArticle {i+1}:")
            print(f"  Page: {article.get('page', 'N/A')}")
            print(f"  Title: '{article.get('title', 'N/A')}'")
            print(f"  Authors: <AUTHORS>
            
    except Exception as e:
        print(f"❌ Native PDF extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("✅ IMPROVED EXTRACTION TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    test_improved_extraction() 
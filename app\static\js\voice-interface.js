/**
 * Voice Interface Module for Document Management System
 * Comprehensive Text-to-Speech and Speech-to-Text capabilities
 * Compatible with existing Bootstrap 5/ERDB interface and accessibility standards
 */

(function() {
    'use strict';

    // Check if VoiceInterface is already defined
    if (typeof window.VoiceInterface !== 'undefined') {
        console.warn('VoiceInterface already exists, skipping redefinition');
        return;
    }

    // Voice Interface namespace
    window.VoiceInterface = {
        // Configuration and state
        config: {
            defaultRate: 1.0,
            defaultVolume: 0.8,
            defaultVoice: null,
            autoPlay: false,
            language: 'en-US'
        },

        state: {
            isInitialized: false,
            isSupported: false,
            isSpeaking: false,
            isPaused: false,
            currentUtterance: null,
            availableVoices: [],
            speechQueue: [],
            currentMessageId: null,
            intentionalStop: false
        },

        /**
         * Initialize the voice interface
         */
        init: function() {
            if (this.state.isInitialized) {
                console.log('VoiceInterface already initialized');
                return;
            }

            console.log('🎤 Initializing VoiceInterface...');

            // Check for Web Speech API support
            this.checkSupport();

            if (!this.state.isSupported) {
                console.warn('Speech synthesis not supported in this browser');
                this.showUnsupportedMessage();
                return;
            }

            // Load saved settings
            this.loadSettings();

            // Initialize voices with retry mechanism
            this.initializeVoicesWithRetry();

            // Set up event listeners
            this.setupEventListeners();

            // Initialize UI components
            this.initializeUI();

            // Add debugging info
            this.logDebugInfo();

            this.state.isInitialized = true;
            console.log('✅ VoiceInterface initialized successfully');
        },

        /**
         * Log debug information for troubleshooting
         */
        logDebugInfo: function() {
            console.log('🔍 Voice Interface Debug Info:');
            console.log('  - Browser:', navigator.userAgent.split(' ').pop());
            console.log('  - Speech Synthesis:', 'speechSynthesis' in window);
            console.log('  - Speech Utterance:', 'SpeechSynthesisUtterance' in window);
            console.log('  - Available Voices:', this.state.availableVoices.length);
            console.log('  - Default Voice:', this.config.defaultVoice ? this.config.defaultVoice.name : 'None');
            console.log('  - Auto-play:', this.config.autoPlay);
            console.log('  - Settings:', this.config);
        },

        /**
         * Check browser support for speech synthesis
         */
        checkSupport: function() {
            this.state.isSupported = 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;

            if (this.state.isSupported) {
                console.log('Speech synthesis supported');
            } else {
                console.warn('Speech synthesis not supported');
            }
        },

        /**
         * Show message for unsupported browsers
         */
        showUnsupportedMessage: function() {
            this.showErrorMessage(
                'Voice features are not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.'
            );

            // Hide voice settings button
            const voiceButton = document.getElementById('voice-settings-toggle');
            if (voiceButton) {
                voiceButton.style.display = 'none';
            }
        },

        /**
         * Show error message to user (fallback when Toastify not available)
         */
        showErrorMessage: function(message) {
            // Try to use DMSUtils.showToast if available
            if (typeof DMSUtils !== 'undefined' && DMSUtils.showToast) {
                DMSUtils.showToast(message, 'warning', 5000);
                return;
            }

            // Fallback to creating a simple notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #f59e0b;
                color: white;
                padding: 12px 16px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
                line-height: 1.4;
                transition: opacity 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        },

        /**
         * Initialize available voices with retry mechanism
         */
        initializeVoicesWithRetry: function() {
            let retryCount = 0;
            const maxRetries = 5;
            const retryDelay = 500;

            const loadVoices = () => {
                this.state.availableVoices = speechSynthesis.getVoices();
                console.log(`🔊 Loaded ${this.state.availableVoices.length} voices (attempt ${retryCount + 1})`);

                if (this.state.availableVoices.length > 0) {
                    this.populateVoiceSelect();
                    this.setDefaultVoice();
                    console.log('✅ Voices loaded successfully');
                    return true;
                } else if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`⏳ No voices found, retrying in ${retryDelay}ms... (${retryCount}/${maxRetries})`);
                    setTimeout(loadVoices, retryDelay);
                } else {
                    console.warn('⚠️ No voices available after maximum retries');
                    this.handleNoVoicesAvailable();
                }
                return false;
            };

            // Load voices immediately if available
            const immediate = loadVoices();

            // Also listen for voiceschanged event (some browsers load voices asynchronously)
            if (!immediate && speechSynthesis.onvoiceschanged !== undefined) {
                speechSynthesis.onvoiceschanged = () => {
                    console.log('🔄 Voices changed event triggered');
                    loadVoices();
                };
            }
        },

        /**
         * Initialize available voices (legacy method for compatibility)
         */
        initializeVoices: function() {
            return this.initializeVoicesWithRetry();
        },

        /**
         * Handle case when no voices are available
         */
        handleNoVoicesAvailable: function() {
            console.warn('No voices available for speech synthesis');

            // Still allow the interface to work, but with limited functionality
            const voiceSelect = document.getElementById('voice-select');
            if (voiceSelect) {
                voiceSelect.innerHTML = '<option value="">No voices available</option>';
                voiceSelect.disabled = true;
            }

            // Show a warning to the user
            this.showErrorMessage('No voices available for text-to-speech. Speech functionality may be limited.');
        },

        /**
         * Populate the voice selection dropdown
         */
        populateVoiceSelect: function() {
            const voiceSelect = document.getElementById('voice-select');
            if (!voiceSelect) return;

            // Clear existing options
            voiceSelect.innerHTML = '';

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Default Voice';
            voiceSelect.appendChild(defaultOption);

            // Add available voices
            this.state.availableVoices.forEach((voice, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${voice.name} (${voice.lang})`;

                // Mark default voice
                if (voice.default) {
                    option.textContent += ' - Default';
                }

                voiceSelect.appendChild(option);
            });

            console.log('Voice selection populated');
        },

        /**
         * Set the default voice based on language preference
         */
        setDefaultVoice: function() {
            let defaultVoice = null;

            // First, try to restore saved voice
            if (this.savedVoiceName && this.savedVoiceLang) {
                defaultVoice = this.state.availableVoices.find(voice =>
                    voice.name === this.savedVoiceName && voice.lang === this.savedVoiceLang
                );

                if (defaultVoice) {
                    console.log(`Restored saved voice: ${defaultVoice.name}`);
                }
            }

            // If no saved voice found, use language preference
            if (!defaultVoice) {
                const preferredLang = this.config.language;
                defaultVoice = this.state.availableVoices.find(voice =>
                    voice.lang.startsWith(preferredLang) && voice.default
                ) || this.state.availableVoices.find(voice =>
                    voice.lang.startsWith(preferredLang)
                ) || this.state.availableVoices.find(voice =>
                    voice.default
                ) || this.state.availableVoices[0];
            }

            if (defaultVoice) {
                this.config.defaultVoice = defaultVoice;
                const voiceIndex = this.state.availableVoices.indexOf(defaultVoice);
                const voiceSelect = document.getElementById('voice-select');
                if (voiceSelect) {
                    voiceSelect.value = voiceIndex;
                }
                console.log(`Default voice set to: ${defaultVoice.name}`);
            }
        },

        /**
         * Set up event listeners for UI controls
         */
        setupEventListeners: function() {
            // Voice selection
            const voiceSelect = document.getElementById('voice-select');
            if (voiceSelect) {
                voiceSelect.addEventListener('change', (e) => {
                    const voiceIndex = parseInt(e.target.value);
                    if (!isNaN(voiceIndex) && this.state.availableVoices[voiceIndex]) {
                        this.config.defaultVoice = this.state.availableVoices[voiceIndex];
                        this.saveSettings();
                        console.log(`Voice changed to: ${this.config.defaultVoice.name}`);
                    }
                });
            }

            // Speech rate slider
            const rateSlider = document.getElementById('speech-rate');
            const rateValue = document.getElementById('speech-rate-value');
            if (rateSlider && rateValue) {
                rateSlider.addEventListener('input', (e) => {
                    this.config.defaultRate = parseFloat(e.target.value);
                    rateValue.textContent = `${this.config.defaultRate}x`;
                    this.saveSettings();
                });
            }

            // Volume slider
            const volumeSlider = document.getElementById('speech-volume');
            const volumeValue = document.getElementById('speech-volume-value');
            if (volumeSlider && volumeValue) {
                volumeSlider.addEventListener('input', (e) => {
                    this.config.defaultVolume = parseInt(e.target.value) / 100;
                    volumeValue.textContent = `${Math.round(this.config.defaultVolume * 100)}%`;
                    this.saveSettings();
                });
            }

            // Auto-play checkbox
            const autoPlayCheckbox = document.getElementById('auto-play-responses');
            if (autoPlayCheckbox) {
                autoPlayCheckbox.addEventListener('change', (e) => {
                    this.config.autoPlay = e.target.checked;
                    this.saveSettings();
                    console.log(`Auto-play ${this.config.autoPlay ? 'enabled' : 'disabled'}`);
                });
            }

            // Listen for theme changes to update voice panel styling
            document.addEventListener('themeChanged', (e) => {
                this.updateThemeStyles(e.detail.isDark);
            });

            // Close panel when clicking outside
            document.addEventListener('click', (e) => {
                const panel = document.getElementById('voice-settings-panel');
                const button = document.getElementById('voice-settings-toggle');

                if (panel && !panel.contains(e.target) && !button.contains(e.target)) {
                    if (!panel.classList.contains('hidden')) {
                        this.toggleSettingsPanel();
                    }
                }
            });
        },

        /**
         * Initialize UI components and apply saved settings
         */
        initializeUI: function() {
            // Apply saved settings to UI controls
            const rateSlider = document.getElementById('speech-rate');
            const rateValue = document.getElementById('speech-rate-value');
            if (rateSlider && rateValue) {
                rateSlider.value = this.config.defaultRate;
                rateValue.textContent = `${this.config.defaultRate}x`;
            }

            const volumeSlider = document.getElementById('speech-volume');
            const volumeValue = document.getElementById('speech-volume-value');
            if (volumeSlider && volumeValue) {
                volumeSlider.value = Math.round(this.config.defaultVolume * 100);
                volumeValue.textContent = `${Math.round(this.config.defaultVolume * 100)}%`;
            }

            const autoPlayCheckbox = document.getElementById('auto-play-responses');
            if (autoPlayCheckbox) {
                autoPlayCheckbox.checked = this.config.autoPlay;
            }

            console.log('UI components initialized');
        },

        /**
         * Toggle the voice settings panel
         */
        toggleSettingsPanel: function() {
            const panel = document.getElementById('voice-settings-panel');
            if (!panel) return;

            const isHidden = panel.classList.contains('hidden');

            if (isHidden) {
                panel.classList.remove('hidden');
                // Focus first interactive element for accessibility
                const firstInput = panel.querySelector('select, input, button');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 100);
                }
            } else {
                panel.classList.add('hidden');
            }
        },

        /**
         * Load settings from localStorage
         */
        loadSettings: function() {
            try {
                const saved = localStorage.getItem('voiceSettings');
                if (saved) {
                    const settings = JSON.parse(saved);
                    this.config = { ...this.config, ...settings };

                    // Restore saved voice if available
                    if (settings.defaultVoiceName && settings.defaultVoiceLang) {
                        // We'll set this after voices are loaded
                        this.savedVoiceName = settings.defaultVoiceName;
                        this.savedVoiceLang = settings.defaultVoiceLang;
                    }

                    console.log('Voice settings loaded from localStorage');
                }
            } catch (error) {
                console.warn('Failed to load voice settings:', error);
            }
        },

        /**
         * Save settings to localStorage
         */
        saveSettings: function() {
            try {
                const settings = {
                    defaultRate: this.config.defaultRate,
                    defaultVolume: this.config.defaultVolume,
                    autoPlay: this.config.autoPlay,
                    language: this.config.language
                };

                // Save voice by name since voice objects can't be serialized
                if (this.config.defaultVoice) {
                    settings.defaultVoiceName = this.config.defaultVoice.name;
                    settings.defaultVoiceLang = this.config.defaultVoice.lang;
                }

                localStorage.setItem('voiceSettings', JSON.stringify(settings));
                console.log('Voice settings saved to localStorage');
            } catch (error) {
                console.warn('Failed to save voice settings:', error);
            }
        },

        /**
         * Process text for speech synthesis (remove markdown, handle citations)
         */
        processTextForSpeech: function(text) {
            if (!text) return '';

            let processedText = text;

            // Remove markdown formatting
            processedText = processedText
                .replace(/\*\*(.*?)\*\*/g, '$1')  // Bold
                .replace(/\*(.*?)\*/g, '$1')      // Italic
                .replace(/`(.*?)`/g, '$1')        // Code
                .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Links
                .replace(/#{1,6}\s/g, '')         // Headers
                .replace(/>\s/g, '')              // Blockquotes
                .replace(/\n\s*\n/g, '. ')        // Double newlines to periods
                .replace(/\n/g, ' ')              // Single newlines to spaces
                .replace(/\s+/g, ' ')             // Multiple spaces to single
                .trim();

            // Handle scientific names (keep them but make pronunciation clearer)
            processedText = processedText.replace(/([A-Z][a-z]+\s+[a-z]+)/g, (match) => {
                // Add slight pause for scientific names
                return match.replace(/\s+/, ' ');
            });

            // Handle citations - convert to readable format
            processedText = processedText.replace(/\(([^)]+)\)/g, (match, content) => {
                if (content.includes('Source:') || content.includes('Page:')) {
                    return ` (${content.replace('Source:', 'from').replace('Page:', 'page')}) `;
                }
                return match;
            });

            // Clean up extra spaces and punctuation
            processedText = processedText
                .replace(/\s+/g, ' ')
                .replace(/\.\s*\./g, '.')
                .replace(/,\s*,/g, ',')
                .trim();

            return processedText;
        },

        /**
         * Speak the given text
         */
        speak: async function(text, messageId = null, options = {}) {
            console.log(`🗣️ Attempting to speak: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

            if (!this.state.isSupported) {
                console.warn('❌ Speech synthesis not supported');
                this.showErrorMessage('Speech synthesis is not supported in this browser.');
                return false;
            }

            if (!text || text.trim() === '') {
                console.warn('❌ No text provided for speech');
                return false;
            }

            // Check if voices are available
            if (this.state.availableVoices.length === 0) {
                console.warn('❌ No voices available for speech synthesis');
                this.showErrorMessage('No voices available for speech synthesis. Please wait a moment and try again.');

                // Try to reload voices
                this.initializeVoicesWithRetry();
                return false;
            }

            // Stop any current speech and wait a moment for it to complete
            if (this.state.isSpeaking || this.state.isPaused) {
                console.log('🛑 Stopping current speech...');
                this.stop();
                // Small delay to ensure previous speech is fully stopped
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Process text for better speech synthesis
            const processedText = this.processTextForSpeech(text);

            if (processedText.length === 0) {
                console.warn('❌ No speakable text after processing');
                return false;
            }

            console.log(`📝 Processed text: "${processedText.substring(0, 100)}${processedText.length > 100 ? '...' : ''}"`);

            // Create utterance
            const utterance = new SpeechSynthesisUtterance(processedText);

            // Apply settings with validation
            utterance.rate = Math.max(0.1, Math.min(10, options.rate || this.config.defaultRate));
            utterance.volume = Math.max(0, Math.min(1, options.volume || this.config.defaultVolume));

            // Set voice with fallback
            const selectedVoice = options.voice || this.config.defaultVoice;
            if (selectedVoice && this.state.availableVoices.includes(selectedVoice)) {
                utterance.voice = selectedVoice;
                console.log(`🎤 Using voice: ${selectedVoice.name} (${selectedVoice.lang})`);
            } else if (this.state.availableVoices.length > 0) {
                utterance.voice = this.state.availableVoices[0];
                console.log(`🎤 Using fallback voice: ${this.state.availableVoices[0].name}`);
            }

            console.log(`⚙️ Speech settings - Rate: ${utterance.rate}, Volume: ${utterance.volume}`);

            // Set up event handlers
            utterance.onstart = () => {
                this.state.isSpeaking = true;
                this.state.isPaused = false;
                this.state.currentUtterance = utterance;
                this.state.currentMessageId = messageId;

                this.updateSpeechControls(messageId, 'speaking');
                console.log('✅ Speech started successfully');
            };

            utterance.onend = () => {
                this.state.isSpeaking = false;
                this.state.isPaused = false;
                this.state.currentUtterance = null;
                this.state.currentMessageId = null;

                this.updateSpeechControls(messageId, 'stopped');
                console.log('🏁 Speech ended');

                // Process next item in queue
                setTimeout(() => this.processQueue(), 100);
            };

            utterance.onerror = (event) => {
                console.log(`❌ Speech error: ${event.error} (intentional stop: ${this.state.intentionalStop})`);

                // Don't log 'interrupted' errors as they're normal when stopping speech
                // Also don't log errors if we intentionally stopped the speech
                if (event.error !== 'interrupted' && !this.state.intentionalStop) {
                    console.error('Speech synthesis error:', event.error);

                    // Show user-friendly error message only for unexpected errors
                    let errorMessage = 'Speech synthesis error occurred. ';
                    switch (event.error) {
                        case 'network':
                            errorMessage += 'Network error. Please check your connection.';
                            break;
                        case 'synthesis-failed':
                            errorMessage += 'Speech synthesis failed. Please try again.';
                            break;
                        case 'synthesis-unavailable':
                            errorMessage += 'Speech synthesis is unavailable. Please try again later.';
                            break;
                        default:
                            errorMessage += 'Please try again.';
                    }
                    this.showErrorMessage(errorMessage);
                }

                this.state.isSpeaking = false;
                this.state.isPaused = false;
                this.state.currentUtterance = null;
                this.state.currentMessageId = null;

                this.updateSpeechControls(messageId, 'error');
            };

            utterance.onpause = () => {
                this.state.isPaused = true;
                this.updateSpeechControls(messageId, 'paused');
                console.log('⏸️ Speech paused');
            };

            utterance.onresume = () => {
                this.state.isPaused = false;
                this.updateSpeechControls(messageId, 'speaking');
                console.log('▶️ Speech resumed');
            };

            // Start speech synthesis with user interaction check
            try {
                console.log('🚀 Starting speech synthesis...');

                // Check if we need user interaction (some browsers require this)
                if (this.needsUserInteraction()) {
                    console.log('⚠️ User interaction required for speech synthesis');
                    this.showErrorMessage('Please click the speaker button to enable speech synthesis.');
                    return false;
                }

                speechSynthesis.speak(utterance);
                console.log('📢 Speech synthesis started');
                return true;
            } catch (error) {
                console.error('❌ Failed to start speech synthesis:', error);
                this.showErrorMessage('Failed to start speech synthesis. Please try again.');
                return false;
            }
        },

        /**
         * Check if user interaction is needed for speech synthesis
         */
        needsUserInteraction: function() {
            // In modern browsers, speech synthesis may require user interaction
            // This is a heuristic check - not perfect but helps identify the issue
            try {
                // Try to create a test utterance
                const testUtterance = new SpeechSynthesisUtterance('');
                testUtterance.volume = 0; // Silent test
                speechSynthesis.speak(testUtterance);
                speechSynthesis.cancel(); // Cancel immediately
                return false; // If we got here, no user interaction needed
            } catch (error) {
                return true; // Likely needs user interaction
            }
        },

        /**
         * Pause current speech
         */
        pause: function() {
            if (this.state.isSpeaking && !this.state.isPaused) {
                speechSynthesis.pause();
                return true;
            }
            return false;
        },

        /**
         * Resume paused speech
         */
        resume: function() {
            if (this.state.isPaused) {
                speechSynthesis.resume();
                return true;
            }
            return false;
        },

        /**
         * Stop current speech
         */
        stop: function() {
            if (this.state.isSpeaking || this.state.isPaused) {
                try {
                    // Set flag to indicate intentional stop (to avoid error logging)
                    this.state.intentionalStop = true;

                    speechSynthesis.cancel();

                    // Reset state
                    this.state.isSpeaking = false;
                    this.state.isPaused = false;
                    this.state.currentUtterance = null;
                    this.state.currentMessageId = null;

                    // Update all speech controls
                    this.updateAllSpeechControls('stopped');

                    // Clear the flag after a short delay
                    setTimeout(() => {
                        this.state.intentionalStop = false;
                    }, 100);

                    return true;
                } catch (error) {
                    console.warn('Error stopping speech synthesis:', error);
                    return false;
                }
            }
            return false;
        },

        /**
         * Add text to speech queue
         */
        addToQueue: function(text, messageId = null, options = {}) {
            this.state.speechQueue.push({
                text: text,
                messageId: messageId,
                options: options
            });

            // If not currently speaking, start processing queue
            if (!this.state.isSpeaking) {
                setTimeout(() => this.processQueue(), 50);
            }
        },

        /**
         * Process speech queue
         */
        processQueue: async function() {
            if (this.state.speechQueue.length === 0 || this.state.isSpeaking) {
                return;
            }

            const nextItem = this.state.speechQueue.shift();
            await this.speak(nextItem.text, nextItem.messageId, nextItem.options);
        },

        /**
         * Clear speech queue
         */
        clearQueue: function() {
            this.state.speechQueue = [];
            this.stop();
        },

        /**
         * Update speech controls for a specific message
         */
        updateSpeechControls: function(messageId, state) {
            if (!messageId) return;

            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageElement) return;

            const speechBtn = messageElement.querySelector('.speech-control-btn');
            const progressBar = messageElement.querySelector('.speech-progress');

            if (speechBtn) {
                speechBtn.classList.remove('speaking', 'paused');

                switch (state) {
                    case 'speaking':
                        speechBtn.classList.add('speaking');
                        speechBtn.innerHTML = '⏸️';
                        speechBtn.title = 'Pause speech';
                        speechBtn.setAttribute('aria-label', 'Pause speech');
                        break;
                    case 'paused':
                        speechBtn.classList.add('paused');
                        speechBtn.innerHTML = '▶️';
                        speechBtn.title = 'Resume speech';
                        speechBtn.setAttribute('aria-label', 'Resume speech');
                        break;
                    case 'stopped':
                    case 'error':
                    default:
                        speechBtn.innerHTML = '🔊';
                        speechBtn.title = 'Read aloud';
                        speechBtn.setAttribute('aria-label', 'Read message aloud');
                        break;
                }
            }

            if (progressBar) {
                if (state === 'speaking') {
                    progressBar.classList.add('active');
                } else {
                    progressBar.classList.remove('active');
                }
            }
        },

        /**
         * Update all speech controls
         */
        updateAllSpeechControls: function(state) {
            const allSpeechBtns = document.querySelectorAll('.speech-control-btn');
            allSpeechBtns.forEach(btn => {
                btn.classList.remove('speaking', 'paused');
                btn.innerHTML = '🔊';
                btn.title = 'Read aloud';
                btn.setAttribute('aria-label', 'Read message aloud');
            });

            const allProgressBars = document.querySelectorAll('.speech-progress');
            allProgressBars.forEach(bar => {
                bar.classList.remove('active');
            });
        },

        /**
         * Add speech controls to a message element
         */
        addSpeechControls: function(messageElement, messageId, text) {
            console.log(`🎛️ Adding speech controls to message: ${messageId}`);

            if (!messageElement || !messageId || !text) {
                console.warn('❌ Missing parameters for addSpeechControls:', { messageElement: !!messageElement, messageId, text: !!text });
                return;
            }

            // Check if controls already exist
            if (messageElement.querySelector('.speech-control-btn')) {
                console.log('⚠️ Speech controls already exist for this message');
                return;
            }

            // Validate that voice interface is ready
            if (!this.state.isSupported) {
                console.log('⚠️ Speech synthesis not supported, skipping controls');
                return;
            }

            // Create speech controls container
            const controlsContainer = document.createElement('div');
            controlsContainer.className = 'speech-controls-container';
            controlsContainer.style.cssText = 'margin-top: 8px; display: flex; align-items: center; gap: 8px;';

            // Create speech button
            const speechBtn = document.createElement('button');
            speechBtn.className = 'speech-control-btn';
            speechBtn.innerHTML = '🔊';
            speechBtn.title = 'Read aloud';
            speechBtn.setAttribute('aria-label', 'Read message aloud');
            speechBtn.setAttribute('data-message-id', messageId);

            // Add click handler with improved error handling
            speechBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log(`🖱️ Speech button clicked for message: ${messageId}`);

                if (this.state.currentMessageId === messageId) {
                    // Currently playing this message
                    if (this.state.isPaused) {
                        console.log('▶️ Resuming speech...');
                        this.resume();
                    } else if (this.state.isSpeaking) {
                        console.log('⏸️ Pausing speech...');
                        this.pause();
                    }
                } else {
                    // Start playing this message
                    console.log('🗣️ Starting speech for new message...');
                    this.speak(text, messageId).catch(error => {
                        console.error('❌ Error starting speech:', error);
                        this.showErrorMessage('Failed to start speech. Please try again.');
                    });
                }
            });

            // Create progress bar
            const progressBar = document.createElement('div');
            progressBar.className = 'speech-progress';
            progressBar.innerHTML = '<div class="speech-progress-bar"></div>';

            // Add controls to container
            controlsContainer.appendChild(speechBtn);
            controlsContainer.appendChild(progressBar);

            // Find the best place to add controls
            const messageContent = messageElement.querySelector('.welcome-message-content, .message-content');
            if (messageContent) {
                messageContent.appendChild(controlsContainer);
                console.log('✅ Speech controls added to message content');
            } else {
                messageElement.appendChild(controlsContainer);
                console.log('✅ Speech controls added to message element');
            }

            // Set message ID on the message element for easy lookup
            messageElement.setAttribute('data-message-id', messageId);

            console.log(`✅ Speech controls successfully added for message: ${messageId}`);
        },

        /**
         * Extract text content from a message element
         */
        extractMessageText: function(messageElement) {
            if (!messageElement) return '';

            // Clone the element to avoid modifying the original
            const clone = messageElement.cloneNode(true);

            // Remove elements that shouldn't be read
            const elementsToRemove = [
                '.speech-controls-container',
                '.source-item',
                '.document-thumbnails-container',
                'details',
                'img',
                'svg',
                'button',
                '.mode-badge'
            ];

            elementsToRemove.forEach(selector => {
                const elements = clone.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });

            // Get text content
            let text = clone.textContent || clone.innerText || '';

            // Clean up the text
            text = text
                .replace(/\s+/g, ' ')
                .replace(/^\s+|\s+$/g, '')
                .trim();

            return text;
        },

        /**
         * Update theme styles for voice interface
         */
        updateThemeStyles: function(isDark) {
            const panel = document.getElementById('voice-settings-panel');
            if (!panel) return;

            if (isDark) {
                panel.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.5)';
            } else {
                panel.style.boxShadow = 'var(--shadow-lg)';
            }
        },

        /**
         * Handle auto-play for new responses
         */
        handleAutoPlay: function(messageElement, messageId) {
            console.log(`🔄 Checking auto-play for message: ${messageId}`);
            console.log(`🔄 Auto-play enabled: ${this.config.autoPlay}`);

            if (!this.config.autoPlay) {
                console.log('⏭️ Auto-play disabled, skipping');
                return;
            }

            if (!this.state.isSupported) {
                console.log('⚠️ Speech synthesis not supported, skipping auto-play');
                return;
            }

            console.log('⏰ Starting auto-play timer...');

            // Small delay to ensure the message is fully rendered
            setTimeout(() => {
                console.log('🎬 Auto-play timer triggered');

                const text = this.extractMessageText(messageElement);
                console.log(`📝 Extracted text length: ${text ? text.length : 0}`);

                if (text && text.length > 0) {
                    console.log('🗣️ Starting auto-play speech...');
                    this.speak(text, messageId).catch(error => {
                        console.error('❌ Error in auto-play:', error);
                        this.showErrorMessage('Auto-play failed. You can manually click the speaker button.');
                    });
                } else {
                    console.warn('⚠️ No text found for auto-play');
                }
            }, 500);
        },

        /**
         * Public method to speak a message (for external use)
         */
        speakMessage: function(messageElement, messageId = null) {
            if (!messageElement) return false;

            const text = this.extractMessageText(messageElement);
            if (!text) return false;

            const id = messageId || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Add speech controls if they don't exist
            this.addSpeechControls(messageElement, id, text);

            // Speak the message
            this.speak(text, id).catch(error => {
                console.error('Error speaking message:', error);
            });
            return true;
        },

        /**
         * Test function for debugging voice interface
         */
        test: function() {
            console.log('🧪 Running Voice Interface Test...');

            // Test basic functionality
            console.log('📊 Voice Interface Status:');
            console.log('  - Initialized:', this.state.isInitialized);
            console.log('  - Supported:', this.state.isSupported);
            console.log('  - Available Voices:', this.state.availableVoices.length);
            console.log('  - Current Settings:', this.config);

            // Test speech synthesis with a simple phrase
            if (this.state.isSupported && this.state.availableVoices.length > 0) {
                console.log('🗣️ Testing speech synthesis...');
                this.speak('This is a test of the voice interface.', 'test_message')
                    .then(success => {
                        if (success) {
                            console.log('✅ Speech test successful');
                        } else {
                            console.log('❌ Speech test failed');
                        }
                    })
                    .catch(error => {
                        console.error('❌ Speech test error:', error);
                    });
            } else {
                console.log('⚠️ Cannot test speech - no voices available');
            }

            // Test UI elements
            const voiceButton = document.getElementById('voice-settings-toggle');
            const voicePanel = document.getElementById('voice-settings-panel');
            const voiceSelect = document.getElementById('voice-select');

            console.log('🎛️ UI Elements Status:');
            console.log('  - Voice Button:', !!voiceButton);
            console.log('  - Voice Panel:', !!voicePanel);
            console.log('  - Voice Select:', !!voiceSelect);

            return {
                initialized: this.state.isInitialized,
                supported: this.state.isSupported,
                voicesCount: this.state.availableVoices.length,
                uiElements: {
                    button: !!voiceButton,
                    panel: !!voicePanel,
                    select: !!voiceSelect
                }
            };
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            VoiceInterface.init();
        });
    } else {
        VoiceInterface.init();
    }

    // Add global test function for easy debugging
    window.testVoiceInterface = function() {
        return VoiceInterface.test();
    };

})();

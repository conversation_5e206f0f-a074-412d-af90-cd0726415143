{% extends "admin_base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block head %}
    <!-- Admin Dashboard specific styles -->
    <style>
        /* Custom styles for admin dashboard cards */
        .dashboard-card {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .dashboard-card .card-icon {
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }
    </style>
{% endblock %}

{% block content %}
    {% if current_user.is_authenticated %}
    <!-- Authenticated User View -->
    <div class="bg-light rounded shadow p-4 mb-4">
        <h1 class="h2 fw-bold text-dark mb-4">Admin Dashboard</h1>
        <div class="row g-4">
    {% else %}
    <!-- Login Form for Unauthenticated Users -->
    <div class="col-md-6 col-lg-4 mx-auto">
        <div class="bg-light rounded shadow p-4 mt-5">
            <div class="text-center mb-4">
                <h1 class="h2 fw-bold text-dark">Admin Login</h1>
                <p class="text-muted mt-2">Please log in to access the admin dashboard</p>
            </div>

            <form method="POST" action="{{ url_for('admin.admin_login') }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="mb-3">
                    <label for="username" class="form-label fw-bold">Username</label>
                    <input type="text" name="username" id="username" required
                           class="form-control">
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label fw-bold">Password</label>
                    <input type="password" name="password" id="password" required
                           class="form-control">
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input type="checkbox" name="remember" id="remember" class="form-check-input">
                        <label for="remember" class="form-check-label">Remember Me</label>
                    </div>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary fw-bold">
                        Log In
                    </button>
                </div>
            </form>
        </div>
    </div>
    {% endif %}

        {% if current_user.is_authenticated %}
            <!-- Upload Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('upload_file') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-primary">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-primary text-white me-3">
                                    <i class="fas fa-upload"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Upload Content</h5>
                            </div>
                            <p class="card-text text-muted">Upload PDFs or add URLs to create knowledge base content.</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Files Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('list_files') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-success">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-success text-white me-3">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Manage Files</h5>
                            </div>
                            <p class="card-text text-muted">View, explore, and delete uploaded PDFs and URLs.</p>
                        </div>
                    </div>
                </a>
            </div>



            <!-- Unified Configuration Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('unified_config') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-info">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-info text-white me-3">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Model Settings</h5>
                            </div>
                            <p class="card-text text-muted">Comprehensive dashboard for all AI models, query, and embedding settings.</p>
                            <span class="badge bg-success">Recommended</span>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Chat History Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('chat_history') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-warning">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-warning text-white me-3">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Chat History</h5>
                            </div>
                            <p class="card-text text-muted">View all past conversations and their sources.</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Chat Sessions Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('view_sessions') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-secondary">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-secondary text-white me-3">
                                    <i class="fas fa-comment-dots"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Chat Sessions</h5>
                            </div>
                            <p class="card-text text-muted">View and manage client chat sessions.</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- AI Analytics Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('admin.analytics_dashboard') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-danger">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-danger text-white me-3">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">AI Analytics</h5>
                            </div>
                            <p class="card-text text-muted">View AI performance metrics and usage statistics.</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- URL Cleanup Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('clean_urls') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100 border-dark">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon bg-dark text-white me-3">
                                    <i class="fas fa-broom"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Clean URLs</h5>
                            </div>
                            <p class="card-text text-muted">Fix malformed URLs in the database to prevent display issues.</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- HTML Generator Card -->
            {% if current_user.has_dashboard_permission('html_generator') %}
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('html_generator_page') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100" style="border-color: #6f42c1;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon text-white me-3" style="background-color: #6f42c1;">
                                    <i class="fas fa-code"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">HTML Generator</h5>
                            </div>
                            <p class="card-text text-muted">Generate customized HTML chat interfaces with pre-configured settings.</p>
                        </div>
                    </div>
                </a>
            </div>
            {% endif %}

            <!-- Backup Management Card -->
            {% if current_user.has_dashboard_permission('backup_management') %}
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('backup.backup_dashboard') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100" style="border-color: #fd7e14;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon text-white me-3" style="background-color: #fd7e14;">
                                    <i class="fas fa-database"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">Backup Management</h5>
                            </div>
                            <p class="card-text text-muted">Create, manage, and restore system backups for data protection.</p>
                            <span class="badge bg-warning">Critical</span>
                        </div>
                    </div>
                </a>
            </div>
            {% endif %}

            <!-- System Health Monitor Card -->
            {% if current_user.has_dashboard_permission('system_monitoring') %}
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('admin.health_dashboard') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100" style="border-color: #20c997;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon text-white me-3" style="background-color: #20c997;">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">System Health</h5>
                            </div>
                            <p class="card-text text-muted">Monitor system performance, database health, and resource usage.</p>
                            <span class="badge bg-info">Monitor</span>
                        </div>
                    </div>
                </a>
            </div>
            {% endif %}

            <!-- User Management Card -->
            <div class="col-md-6 col-lg-3">
                <a href="{{ url_for('user.admin_users') }}" class="text-decoration-none">
                    <div class="card dashboard-card h-100" style="border-color: #e83e8c;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="card-icon text-white me-3" style="background-color: #e83e8c;">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h5 class="card-title mb-0 text-dark">User Management</h5>
                            </div>
                            <p class="card-text text-muted">Manage users, roles, and permissions for the system.</p>
                        </div>
                    </div>
                </a>
            </div>

        </div>


        </div>

        {% endif %}
{% endblock %}

{% block scripts %}
    <script src="/static/admin.js"></script>
{% endblock %}
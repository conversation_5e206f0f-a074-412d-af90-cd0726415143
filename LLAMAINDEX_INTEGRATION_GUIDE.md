# LlamaIndex + Lang<PERSON>hain Hybrid Integration Guide

## Overview

This guide documents the seamless integration of LlamaIndex with the existing LangChain-based ERDB AI Cursor system. The integration provides enhanced document processing, advanced retrieval strategies, and improved query capabilities while maintaining compatibility with the existing architecture.

## Architecture

### Hybrid Processing Pipeline

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Input     │───▶│  LangChain       │───▶│  LlamaIndex     │
│                 │    │  Processing      │    │  Enhancement    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  ChromaDB        │    │  Hybrid Query   │
                       │  Vector Store    │    │  Engine         │
                       └──────────────────┘    └─────────────────┘
```

### Key Components

1. **LlamaIndexService**: Core service managing LlamaIndex integration
2. **HybridRetriever**: Advanced retriever combining vector similarity and document-level scoring
3. **Enhanced Query Engine**: Multi-strategy query processing
4. **Document Conversion**: Seamless conversion between LangChain and LlamaIndex formats

## Installation

### Prerequisites

- Python 3.10+
- Ollama with llama3.1:8b model
- Existing ERDB AI Cursor setup

### Dependencies

The following LlamaIndex packages are automatically installed:

```bash
llama-index>=0.10.0
llama-index-llms-langchain>=0.1.0
llama-index-embeddings-langchain>=0.1.0
llama-index-llms-ollama>=0.1.0
llama-index-embeddings-ollama>=0.1.0
llama-index-readers-file>=0.1.0
llama-index-readers-web>=0.1.0
```

## Configuration

### LlamaIndex Settings

Configuration is managed in `config/rag_extraction_config.py`:

```python
LLAMAINDEX_CONFIG = {
    "enabled": True,                    # Enable/disable LlamaIndex integration
    "ollama_model": "llama3.1:8b",     # Ollama model for LLM and embeddings
    "retrieval_strategy": "hybrid",     # "hybrid", "multimodal", "standard"
    "similarity_top_k": 5,              # Number of top results to retrieve
    "chunk_size": 1000,                 # Document chunk size
    "chunk_overlap": 200,               # Chunk overlap
    "response_mode": "tree_summarize",  # Response generation mode
    "streaming": True,                  # Enable streaming responses
    "structured_answer_filtering": True, # Enable structured answer filtering
    "hybrid_alpha": 0.5,               # Weight for hybrid retrieval scoring
    "enable_hybrid_retriever": True,    # Enable hybrid retriever
    "enable_performance_monitoring": True # Enable performance monitoring
}
```

### Configuration Functions

```python
from config.rag_extraction_config import (
    get_llamaindex_config,
    is_llamaindex_enabled,
    get_llamaindex_ollama_model,
    get_llamaindex_retrieval_strategy,
    update_llamaindex_config
)

# Check if LlamaIndex is enabled
if is_llamaindex_enabled():
    # Get current configuration
    config = get_llamaindex_config()
    
    # Update configuration
    update_llamaindex_config(retrieval_strategy="multimodal")
```

## Usage

### Basic Document Processing

#### Standard LangChain Processing (Existing)

```python
from app.services.pdf_processor import pdf_to_documents

# Standard processing
documents = pdf_to_documents(
    pdf_path="path/to/document.pdf",
    category="CANOPY",
    use_llamaindex=False  # Disable LlamaIndex
)
```

#### Hybrid Processing (New)

```python
from app.services.pdf_processor import pdf_to_documents_hybrid

# Hybrid processing with LlamaIndex enhancement
result = pdf_to_documents_hybrid(
    pdf_path="path/to/document.pdf",
    category="CANOPY",
    use_llamaindex=True,
    retrieval_strategy="hybrid"
)

if isinstance(result, tuple):
    documents, index = result
    print(f"Processed {len(documents)} documents with LlamaIndex index")
else:
    documents = result
    print(f"Processed {len(documents)} documents (LlamaIndex disabled)")
```

### Advanced Document Processing

#### Using LlamaIndexService Directly

```python
from app.services.llamaindex_service import llamaindex_service
from langchain.schema import Document

# Create sample documents
documents = [
    Document(
        page_content="Artificial intelligence is transforming industries.",
        metadata={"source": "test", "category": "AI"}
    )
]

# Process with LlamaIndex
index = llamaindex_service.process_documents_hybrid(documents, category="test")

# Get processing statistics
stats = llamaindex_service.get_processing_stats(index)
print(f"Processed {stats['total_documents']} documents")
```

### Query Processing

#### Basic Querying

```python
# Query documents using different strategies
query = "What is artificial intelligence?"

# Hybrid strategy (recommended)
result = llamaindex_service.query_documents(
    index, query, strategy="hybrid"
)

print(f"Response: {result['response']}")
print(f"Query time: {result['query_time']:.2f}s")
print(f"Sources: {len(result['source_nodes'])}")
```

#### Advanced Query Engine

```python
# Create query engine with specific strategy
query_engine = llamaindex_service.create_hybrid_query_engine(
    index, retrieval_strategy="multimodal"
)

# Execute query
response = query_engine.query("What are the main applications of AI?")
print(response)
```

#### Hybrid Retriever

```python
# Create hybrid retriever for advanced retrieval
retriever = llamaindex_service.create_hybrid_retriever(
    index,
    similarity_top_k=5,
    alpha=0.5  # Weight for hybrid scoring
)

# Retrieve relevant nodes
from llama_index.core import QueryBundle
query_bundle = QueryBundle(query_str="machine learning applications")
nodes = retriever.retrieve(query_bundle)

for node in nodes:
    print(f"Score: {node.score}, Content: {node.text[:100]}...")
```

### Document Conversion

#### LangChain to LlamaIndex

```python
from langchain.schema import Document

# LangChain documents
langchain_docs = [
    Document(page_content="Sample content", metadata={"source": "test"})
]

# Convert to LlamaIndex format
llama_docs = llamaindex_service.convert_langchain_to_llamaindex(langchain_docs)

# Process with LlamaIndex
index = llamaindex_service.process_documents_hybrid(llama_docs)
```

## Retrieval Strategies

### 1. Hybrid Strategy (Default)

Combines vector similarity search with document-level scoring:

- **Vector Similarity**: Standard embedding-based retrieval
- **Document-Level Scoring**: Reweights results based on document relevance
- **Alpha Parameter**: Controls balance between vector and document scoring (0.5 default)

### 2. Multimodal Strategy

Enhanced for multi-modal content (text, images, tables):

- **Streaming Responses**: Real-time response generation
- **Compact Mode**: Optimized for large document sets
- **Enhanced Context**: Better handling of mixed content types

### 3. Standard Strategy

Basic LlamaIndex query engine:

- **Simple Retrieval**: Standard vector similarity search
- **Fast Processing**: Minimal overhead
- **Compatibility**: Works with all document types

## Performance Monitoring

### Built-in Monitoring

All LlamaIndex operations include performance monitoring:

```python
@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def process_documents_hybrid(self, documents, category=None):
    # Processing with automatic performance tracking
    pass
```

### Performance Metrics

- **Memory Usage**: Track memory consumption during processing
- **CPU Usage**: Monitor CPU utilization
- **Processing Time**: Measure operation duration
- **Result Size**: Track output data size

## Testing

### Run Integration Tests

```bash
python test_llamaindex_integration.py
```

### Test Coverage

The test suite covers:

1. **Configuration Loading**: Verify config functions work correctly
2. **Service Initialization**: Test LlamaIndex service setup
3. **Document Conversion**: Validate format conversion
4. **Hybrid Processing**: Test document processing pipeline
5. **Query Engine Creation**: Verify different strategies
6. **Document Querying**: Test query functionality
7. **PDF Integration**: Test with actual PDF files
8. **Hybrid Retriever**: Test advanced retrieval

### Test Output Example

```
============================================================
Starting LlamaIndex + LangChain Integration Tests
============================================================
2024-01-15 10:30:00 - Testing LlamaIndex configuration...
✓ LlamaIndex configuration test passed
2024-01-15 10:30:01 - Testing LlamaIndex service initialization...
✓ LlamaIndex service initialized successfully
✓ LlamaIndex service attributes test passed
...
🎉 All LlamaIndex integration tests passed!
Total test time: 45.23 seconds
============================================================
```

## Integration with Existing System

### Backward Compatibility

The integration maintains full backward compatibility:

- **Existing Functions**: All existing PDF processing functions work unchanged
- **Optional Enhancement**: LlamaIndex features are opt-in via configuration
- **Fallback Support**: Automatic fallback to standard processing if LlamaIndex fails

### Metadata Enhancement

Documents processed with LlamaIndex include additional metadata:

```python
{
    "llamaindex_enhanced": True,
    "processing_pipeline": "hybrid_langchain_llamaindex",
    "llamaindex_strategy": "hybrid",
    # ... existing metadata
}
```

### ChromaDB Integration

LlamaIndex enhanced documents are stored in the existing ChromaDB:

- **Unified Storage**: All documents in single vector database
- **Enhanced Retrieval**: LlamaIndex provides additional retrieval capabilities
- **Metadata Preservation**: All LlamaIndex metadata preserved in ChromaDB

## Troubleshooting

### Common Issues

#### 1. Ollama Model Not Found

**Error**: `Model 'llama3.1:8b' not found`

**Solution**:
```bash
# Pull the required model
ollama pull llama3.1:8b

# Or update configuration to use available model
update_llamaindex_config(ollama_model="llama2:7b")
```

#### 2. LlamaIndex Import Errors

**Error**: `ModuleNotFoundError: No module named 'llama_index'`

**Solution**:
```bash
# Install LlamaIndex dependencies
pip install llama-index llama-index-llms-langchain llama-index-embeddings-langchain
```

#### 3. Performance Issues

**Issue**: Slow processing or high memory usage

**Solutions**:
```python
# Reduce chunk size
update_llamaindex_config(chunk_size=500, chunk_overlap=100)

# Use standard strategy for faster processing
update_llamaindex_config(retrieval_strategy="standard")

# Disable streaming for lower memory usage
update_llamaindex_config(streaming=False)
```

### Debug Mode

Enable debug logging for detailed information:

```python
import logging
logging.getLogger('app.services.llamaindex_service').setLevel(logging.DEBUG)
```

## Performance Optimization

### Recommended Settings

For optimal performance:

```python
# Production settings
LLAMAINDEX_CONFIG = {
    "enabled": True,
    "ollama_model": "llama3.1:8b",
    "retrieval_strategy": "hybrid",
    "similarity_top_k": 5,
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "response_mode": "tree_summarize",
    "streaming": True,
    "structured_answer_filtering": True,
    "hybrid_alpha": 0.5
}

# High-performance settings
LLAMAINDEX_CONFIG = {
    "enabled": True,
    "ollama_model": "llama3.1:8b",
    "retrieval_strategy": "standard",
    "similarity_top_k": 3,
    "chunk_size": 800,
    "chunk_overlap": 100,
    "response_mode": "compact",
    "streaming": False,
    "structured_answer_filtering": False,
    "hybrid_alpha": 0.7
}
```

### Memory Management

- **Chunk Size**: Smaller chunks reduce memory usage
- **Streaming**: Disable for lower memory consumption
- **Batch Processing**: Process documents in smaller batches
- **Garbage Collection**: Monitor and clean up unused indices

## Future Enhancements

### Planned Features

1. **Multi-Modal Support**: Enhanced image and table processing
2. **Advanced Reranking**: Sophisticated result reranking algorithms
3. **Caching Layer**: Intelligent caching for repeated queries
4. **Distributed Processing**: Support for distributed document processing
5. **Custom Embeddings**: Integration with custom embedding models

### Extension Points

The architecture supports easy extension:

```python
# Custom retriever
class CustomRetriever(BaseRetriever):
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        # Custom retrieval logic
        pass

# Custom query engine
class CustomQueryEngine(RetrieverQueryEngine):
    def query(self, query_str: str) -> QueryResponse:
        # Custom query processing
        pass
```

## Conclusion

The LlamaIndex + LangChain hybrid integration provides significant enhancements to the ERDB AI Cursor system while maintaining full backward compatibility. The integration offers:

- **Enhanced Document Processing**: Advanced text analysis and structuring
- **Improved Retrieval**: Multiple retrieval strategies for better results
- **Better Query Responses**: Structured and contextual answers
- **Performance Monitoring**: Comprehensive tracking and optimization
- **Easy Configuration**: Simple setup and customization

The system is now ready for production use with enhanced capabilities for document processing and information retrieval. 
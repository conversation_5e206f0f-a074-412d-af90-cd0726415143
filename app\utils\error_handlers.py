from flask import jsonify, request

def register_error_handlers(app):
    """Register error handlers with the Flask app."""

    @app.errorhandler(404)
    def handle_404(error):
        """Handle 404 errors with JSON response for API requests."""
        if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
            return jsonify({"error": "Not found", "status": 404}), 404
        return error

    @app.errorhandler(500)
    def handle_500(error):
        """Handle 500 errors with JSON response for API requests."""
        if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
            return jsonify({"error": "Internal server error", "status": 500}), 500
        return error

    @app.errorhandler(403)
    def handle_403(error):
        """Handle 403 errors with JSON response for API requests."""
        if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
            return jsonify({"error": "Forbidden", "status": 403}), 403
        return error

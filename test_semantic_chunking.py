#!/usr/bin/env python3
"""
Test Script for Semantic Chunking Service

This script demonstrates the hybrid semantic chunking pipeline with different
strategies and configurations. It shows how the system can create more meaningful,
contextually coherent chunks for better RAG performance.
"""

import os
import sys
import logging
from typing import List, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_semantic_chunking():
    """Test the semantic chunking service with different strategies."""
    
    try:
        # Import the semantic chunking service
        from app.services.semantic_chunking_service import (
            SemanticChunkingService, create_semantic_chunking_service, ChunkingStrategy
        )
        from config.semantic_chunking_config import (
            get_chunking_config, get_available_presets, get_strategy_info
        )
        from langchain.schema import Document
        
        logger.info("✅ Semantic chunking service imported successfully")
        
        # Sample documents for testing
        sample_documents = [
            Document(
                page_content="""
# Introduction to Machine Learning

Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions without being explicitly programmed. This field has seen tremendous growth in recent years, with applications ranging from recommendation systems to autonomous vehicles.

## Types of Machine Learning

### Supervised Learning
Supervised learning involves training a model on labeled data. The model learns to map input features to known output labels. Common algorithms include linear regression, logistic regression, and support vector machines.

### Unsupervised Learning
Unsupervised learning works with unlabeled data to discover hidden patterns and structures. Clustering algorithms like K-means and dimensionality reduction techniques like PCA are examples of unsupervised learning.

### Reinforcement Learning
Reinforcement learning involves an agent learning to make decisions by interacting with an environment. The agent receives rewards or penalties based on its actions and learns to maximize cumulative rewards over time.

## Applications

Machine learning has numerous applications across various industries:

1. **Healthcare**: Disease diagnosis, drug discovery, personalized medicine
2. **Finance**: Fraud detection, risk assessment, algorithmic trading
3. **E-commerce**: Recommendation systems, demand forecasting
4. **Transportation**: Autonomous vehicles, route optimization

The field continues to evolve with new algorithms, techniques, and applications being developed regularly.
                """,
                metadata={"source": "ml_intro.md", "type": "markdown"}
            ),
            Document(
                page_content="""
# Research Methodology

This study employed a mixed-methods approach combining quantitative and qualitative data collection techniques. The research was conducted over a period of 18 months, from January 2023 to June 2024.

## Data Collection

### Quantitative Methods
We collected data from 500 participants using structured surveys. The survey included questions about demographics, preferences, and behaviors. Statistical analysis was performed using SPSS version 28.0.

### Qualitative Methods
In-depth interviews were conducted with 25 key informants. Each interview lasted approximately 60 minutes and was recorded for transcription. Thematic analysis was used to identify patterns and themes in the qualitative data.

## Analysis Framework

The analysis followed a three-phase approach:

1. **Exploratory Phase**: Initial data exploration and hypothesis generation
2. **Confirmatory Phase**: Statistical testing and validation of hypotheses
3. **Interpretive Phase**: Integration of quantitative and qualitative findings

## Results

The study revealed significant correlations between user behavior and system performance. Key findings included:

- 78% of users preferred intuitive interfaces
- Response time was the most critical factor for user satisfaction
- Mobile usage increased by 45% during the study period

These results have important implications for system design and user experience optimization.
                """,
                metadata={"source": "research_methodology.md", "type": "markdown"}
            ),
            Document(
                page_content="""
# API Documentation

## Authentication

All API requests require authentication using Bearer tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-token>
```

## Endpoints

### GET /api/users
Retrieve a list of users.

**Parameters:**
- `page` (integer): Page number for pagination
- `limit` (integer): Number of items per page
- `search` (string): Search query for filtering users

**Response:**
```json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10
}
```

### POST /api/users
Create a new user.

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "user"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "role": "user",
  "created_at": "2024-01-15T10:30:00Z"
}
```

## Error Handling

The API returns standard HTTP status codes:

- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Internal Server Error

Error responses include a message field with details about the error.
                """,
                metadata={"source": "api_docs.md", "type": "markdown"}
            )
        ]
        
        logger.info(f"📄 Created {len(sample_documents)} sample documents for testing")
        
        # Test different chunking strategies
        strategies = [
            ("recursive", "Recursive Character"),
            ("semantic", "Semantic"),
            ("structural", "Structural"),
            ("hybrid", "Hybrid Semantic")
        ]
        
        results = {}
        
        for strategy_name, strategy_display in strategies:
            logger.info(f"\n🔧 Testing {strategy_display} chunking strategy...")
            
            try:
                # Create chunking service with the strategy
                chunker = create_semantic_chunking_service(
                    strategy=strategy_name,
                    chunk_size=800,
                    chunk_overlap=160
                )
                
                # Process documents
                chunks = chunker.chunk_documents(sample_documents)
                
                # Analyze results
                total_chunks = len(chunks)
                avg_chunk_size = sum(len(chunk.page_content) for chunk in chunks) / total_chunks if total_chunks > 0 else 0
                
                # Count chunks with enhanced metadata
                enhanced_chunks = sum(1 for chunk in chunks if chunk.metadata.get('chunking_strategy'))
                semantic_keywords = sum(1 for chunk in chunks if chunk.metadata.get('semantic_keywords'))
                
                results[strategy_name] = {
                    'total_chunks': total_chunks,
                    'avg_chunk_size': round(avg_chunk_size, 2),
                    'enhanced_metadata': enhanced_chunks,
                    'semantic_keywords': semantic_keywords,
                    'sample_chunks': chunks[:3]  # First 3 chunks for inspection
                }
                
                logger.info(f"  ✅ {strategy_display}: {total_chunks} chunks, avg size: {avg_chunk_size:.0f} chars")
                
                # Show sample chunk metadata
                if chunks:
                    sample_chunk = chunks[0]
                    logger.info(f"  📋 Sample chunk metadata: {list(sample_chunk.metadata.keys())}")
                
            except Exception as e:
                logger.error(f"  ❌ Error with {strategy_display} strategy: {e}")
                results[strategy_name] = {'error': str(e)}
        
        # Test performance presets
        logger.info(f"\n🎯 Testing performance presets...")
        
        presets = get_available_presets()
        preset_results = {}
        
        for preset_id, preset_info in presets.items():
            logger.info(f"  Testing {preset_info['name']} preset...")
            
            try:
                config = get_chunking_config(preset_id)
                chunker = SemanticChunkingService(config)
                chunks = chunker.chunk_documents(sample_documents[:1])  # Test with one document
                
                preset_results[preset_id] = {
                    'name': preset_info['name'],
                    'description': preset_info['description'],
                    'chunks_created': len(chunks),
                    'avg_chunk_size': sum(len(chunk.page_content) for chunk in chunks) / len(chunks) if chunks else 0
                }
                
                logger.info(f"    ✅ {preset_info['name']}: {len(chunks)} chunks")
                
            except Exception as e:
                logger.error(f"    ❌ Error with {preset_info['name']} preset: {e}")
                preset_results[preset_id] = {'error': str(e)}
        
        # Display comprehensive results
        print("\n" + "="*80)
        print("SEMANTIC CHUNKING TEST RESULTS")
        print("="*80)
        
        print("\n📊 Strategy Comparison:")
        print("-" * 60)
        for strategy_name, result in results.items():
            if 'error' in result:
                print(f"{strategy_name.upper():15} ❌ {result['error']}")
            else:
                print(f"{strategy_name.upper():15} ✅ {result['total_chunks']:3d} chunks, "
                      f"avg: {result['avg_chunk_size']:6.0f} chars, "
                      f"enhanced: {result['enhanced_metadata']:2d}")
        
        print("\n🎯 Performance Presets:")
        print("-" * 60)
        for preset_id, result in preset_results.items():
            if 'error' in result:
                print(f"{result['name']:20} ❌ {result['error']}")
            else:
                print(f"{result['name']:20} ✅ {result['chunks_created']:3d} chunks, "
                      f"avg: {result['avg_chunk_size']:6.0f} chars")
        
        # Show detailed analysis of hybrid strategy
        if 'hybrid' in results and 'error' not in results['hybrid']:
            print(f"\n🔍 Detailed Analysis - Hybrid Strategy:")
            print("-" * 60)
            
            hybrid_chunks = results['hybrid']['sample_chunks']
            for i, chunk in enumerate(hybrid_chunks, 1):
                print(f"\nChunk {i}:")
                print(f"  Size: {len(chunk.page_content)} characters")
                print(f"  Strategy: {chunk.metadata.get('chunking_strategy', 'N/A')}")
                print(f"  Content Type: {chunk.metadata.get('content_type', 'N/A')}")
                print(f"  Keywords: {chunk.metadata.get('semantic_keywords', [])[:5]}")
                print(f"  Preview: {chunk.page_content[:100]}...")
        
        print("\n" + "="*80)
        print("✅ Semantic chunking test completed successfully!")
        print("="*80)
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import semantic chunking service: {e}")
        logger.info("💡 Make sure to install required dependencies:")
        logger.info("   pip install sentence-transformers transformers torch spacy")
        logger.info("   python -m spacy download en_core_web_sm")
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False


def test_content_type_detection():
    """Test content type detection functionality."""
    
    try:
        from app.services.semantic_chunking_service import SemanticChunkingService, ContentType
        
        logger.info("\n🔍 Testing content type detection...")
        
        # Test different content types
        test_contents = [
            ("This is a scientific paper about machine learning algorithms.", ContentType.SCIENTIFIC_PAPER),
            ("API documentation for the REST endpoints.", ContentType.TECHNICAL_DOCUMENT),
            ("Welcome to our website! Click here to learn more.", ContentType.WEB_CONTENT),
            ("This is a general document about various topics.", ContentType.GENERAL_DOCUMENT)
        ]
        
        chunker = SemanticChunkingService()
        
        for content, expected_type in test_contents:
            detected_type = chunker._detect_content_type(content)
            status = "✅" if detected_type == expected_type else "❌"
            logger.info(f"  {status} '{content[:50]}...' -> {detected_type.value} (expected: {expected_type.value})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Content type detection test failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🚀 Starting Semantic Chunking Test Suite")
    logger.info("=" * 60)
    
    # Test 1: Basic semantic chunking functionality
    test1_success = test_semantic_chunking()
    
    # Test 2: Content type detection
    test2_success = test_content_type_detection()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Semantic Chunking: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    logger.info(f"Content Type Detection: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        logger.info("\n🎉 All tests passed! Semantic chunking is working correctly.")
        return 0
    else:
        logger.info("\n⚠️  Some tests failed. Check the logs above for details.")
        return 1


if __name__ == "__main__":
    exit(main()) 
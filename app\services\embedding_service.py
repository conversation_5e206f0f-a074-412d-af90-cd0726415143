import os
import logging
import requests
import json
import re
import urllib.parse
import time
from datetime import datetime
from typing import List, Dict, Any
from bs4 import BeautifulSoup
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

# Import semantic chunking service
try:
    from app.services.semantic_chunking_service import SemanticChunkingService, create_semantic_chunking_service
    from config.semantic_chunking_config import get_chunking_config, get_content_type_config
    SEMANTIC_CHUNKING_AVAILABLE = True
except ImportError:
    SEMANTIC_CHUNKING_AVAILABLE = False
    SemanticChunkingService = None
    create_semantic_chunking_service = None
from werkzeug.utils import secure_filename
from app.services.vector_db import get_vector_db, add_documents_with_category
from app.services.llamaindex_vector_db import get_llamaindex_vector_db
import fitz  # PyMuPDF for better PDF handling
from app.services.pdf_processor import process_pdf

# Import performance monitoring decorators
from app.utils.performance_monitor import (
    monitor_embedding_operation,
    performance_monitor,
    get_performance_monitor
)

# Import batch processing
from app.utils.batch_processor import get_batch_processor, BatchJob, batch_generate_embeddings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")


def extract_images_from_pdf(pdf_path):
    """
    Extract image metadata from PDF using PyMuPDF without saving images.
    This function is for metadata extraction only and does not save any files.
    For saving images, use the extract_images_from_pdf function in pdf_processor.py instead.
    """
    images = []
    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            image_list = page.get_images(full=True)
            for img_index, img in enumerate(image_list):
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                # Only collect metadata, don't save images
                image_info = {
                    "page": page_num + 1,
                    "index": img_index,
                    "size": len(image_bytes),
                    "format": base_image["ext"]
                }
                images.append(image_info)
        return images
    except Exception as e:
        logger.error(f"Failed to extract image metadata from PDF {pdf_path}: {str(e)}")
        return []

def extract_links_from_pdf(pdf_path):
    """Extract links from PDF using PyMuPDF."""
    links = []
    try:
        doc = fitz.open(pdf_path)
        for page_num, page in enumerate(doc):
            link_list = page.get_links()
            for link in link_list:
                if "uri" in link:
                    uri = link["uri"]
                    if uri.startswith(("http://", "https://")):
                        links.append(uri)
        return links
    except Exception as e:
        logger.error(f"Failed to extract links from PDF {pdf_path}: {str(e)}")
        return []

def extract_text_from_pdf(pdf_path, category=None, source_url=None):
    """
    Extract text and metadata from PDF using process_pdf to avoid duplicate image extraction.

    Args:
        pdf_path: Path to the PDF file
        category: Optional category for organizing content
        source_url: Optional source URL for the PDF

    Returns:
        List of LangChain Document objects with metadata
    """
    try:
        # Use LangChain's loader for text extraction
        loader = PyPDFLoader(pdf_path)
        pages = loader.load()

        # If we have a category, use process_pdf to get metadata without duplicating image extraction
        if category:
            # Use process_pdf to get metadata without saving duplicate images
            pdf_info = process_pdf(pdf_path, category, source_url, save_images=False)

            # Add image and link information to page metadata
            for page in pages:
                page_num = page.metadata.get("page", 0)
                # Add images for this page from pdf_info
                page_images = [img for img in pdf_info["images"] if img.get("page") == page_num]
                if page_images:
                    page.metadata["images"] = page_images

                # Add links found in the text
                if pdf_info["links"]:
                    page.metadata["pdf_links"] = pdf_info["links"]
        else:
            # If no category, just get metadata without saving files
            images = extract_images_from_pdf(pdf_path)
            links = extract_links_from_pdf(pdf_path)

            # Add image and link information to page metadata
            for page in pages:
                page_num = page.metadata.get("page", 0)
                # Add images for this page
                page_images = [img for img in images if img.get("page") == page_num]
                if page_images:
                    page.metadata["images"] = page_images

                # Add links found in the text
                if links:
                    page.metadata["pdf_links"] = links

        return pages
    except Exception as e:
        logger.error(f"Failed to extract text from PDF {pdf_path}: {str(e)}")
        return []

def extract_images_and_links_from_html(soup, base_url):
    """Extract images and links from HTML content."""
    images = []
    links = []

    # Extract images - prioritize JPG images
    jpg_images = []
    other_images = []

    for img in soup.find_all('img', src=True):
        img_url = img['src']
        if not img_url.startswith(('http://', 'https://')):
            img_url = urllib.parse.urljoin(base_url, img_url)

        # Separate JPG images from other formats
        if img_url.lower().endswith(('.jpg', '.jpeg')):
            jpg_images.append(img_url)
        else:
            other_images.append(img_url)

    # Add JPG images first, then others if needed
    images.extend(jpg_images)
    images.extend(other_images)

    # Extract links
    for a in soup.find_all('a', href=True):
        href = a['href']
        if not href.startswith(('http://', 'https://')):
            href = urllib.parse.urljoin(base_url, href)

        # Focus on document links
        if re.search(r'\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$', href, re.IGNORECASE):
            links.append(href)
        # Or links that might be download/request links
        elif re.search(r'(download|request|copy)', href, re.IGNORECASE):
            links.append(href)

    return images, links

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def scrape_single_url(url, visited_urls=None):
    """Scrape content, images, and links from a single URL."""
    if visited_urls is None:
        visited_urls = set()

    # Skip if already visited
    if url in visited_urls:
        return None

    # Mark as visited
    visited_urls.add(url)

    # Set a reasonable timeout and max retries
    TIMEOUT = 10  # seconds
    MAX_RETRIES = 2

    # Check if URL is valid before proceeding
    if not url or not url.startswith(('http://', 'https://')):
        logger.warning(f"Invalid URL format: {url}")
        return {
            "url": url,
            "text": f"[Unable to scrape: Invalid URL format] {url}",
            "images": [],
            "links": [],
            "error": "Invalid URL format"
        }

    # Initialize default values in case of failure
    images = []
    links = []
    text = f"[Unable to scrape content from {url}]"
    error_message = None

    try:
        # Disable SSL verification warnings
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # Attempt request with retries
        for retry in range(MAX_RETRIES + 1):
            try:
                response = requests.get(url, timeout=TIMEOUT, verify=False)
                response.raise_for_status()
                break  # Success, exit retry loop
            except requests.exceptions.Timeout:
                if retry < MAX_RETRIES:
                    logger.warning(f"Request timed out for {url}, retrying ({retry+1}/{MAX_RETRIES})...")
                    continue
                raise  # Re-raise if all retries failed
            except requests.exceptions.ConnectionError as conn_err:
                # Check if it's a DNS resolution error
                if "getaddrinfo failed" in str(conn_err) or "Name or service not known" in str(conn_err):
                    error_message = f"DNS resolution failed for {url}"
                    logger.warning(error_message)
                    raise
                if retry < MAX_RETRIES:
                    logger.warning(f"Connection error for {url}, retrying ({retry+1}/{MAX_RETRIES})...")
                    continue
                raise  # Re-raise if all retries failed
            except Exception as e:
                if retry < MAX_RETRIES:
                    logger.warning(f"Error accessing {url}, retrying ({retry+1}/{MAX_RETRIES}): {str(e)}")
                    continue
                raise  # Re-raise if all retries failed

        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract images and links before removing elements
        images, links = extract_images_and_links_from_html(soup, url)

        # Remove unwanted elements for text extraction
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            element.decompose()

        # Get the main text content
        text = soup.get_text(separator=' ', strip=True)
        text = ' '.join(text.split())

        if not text:
            logger.warning(f"No content extracted from URL {url}")
            text = f"[No meaningful text content could be extracted from {url}]"

        # Add extracted images and links to the text
        if images:
            text += "\n\nImages found on page: " + ", ".join(images[:5])
            if len(images) > 5:
                text += f" and {len(images) - 5} more"

        if links:
            text += "\n\nDocument links found on page: " + ", ".join(links[:5])
            if len(links) > 5:
                text += f" and {len(links) - 5} more"

        # Return a dictionary with all extracted information
        return {
            "url": url,
            "text": text,
            "images": images,
            "links": links,
            "error": None
        }
    except Exception as e:
        error_type = type(e).__name__
        error_message = str(e)

        # Create a more user-friendly error message based on the exception type
        if isinstance(e, requests.exceptions.Timeout):
            error_message = f"Request timed out after {TIMEOUT} seconds"
        elif isinstance(e, requests.exceptions.ConnectionError):
            if "getaddrinfo failed" in str(e):
                error_message = f"DNS resolution failed (domain name could not be resolved)"
            else:
                error_message = f"Connection error (server may be down or unreachable)"
        elif isinstance(e, requests.exceptions.HTTPError):
            error_message = f"HTTP error: {e.response.status_code} {e.response.reason}"
        elif isinstance(e, requests.exceptions.TooManyRedirects):
            error_message = "Too many redirects"
        elif isinstance(e, requests.exceptions.RequestException):
            error_message = f"Request error: {str(e)}"

        logger.error(f"Failed to scrape URL {url}: {error_type} - {error_message}")

        # Return a dictionary with error information
        return {
            "url": url,
            "text": f"[Unable to scrape content from {url}: {error_message}]",
            "images": [],
            "links": [],
            "error": error_message
        }

def extract_page_links(soup, base_url):
    """Extract all links from a page that should be followed for deeper scraping."""
    links = []

    for a in soup.find_all('a', href=True):
        href = a['href']
        if not href.startswith(('http://', 'https://')):
            href = urllib.parse.urljoin(base_url, href)

        # Skip anchors, javascript, mailto links, etc.
        if href.startswith(('javascript:', 'mailto:', 'tel:', '#')):
            continue

        # Skip links to different domains
        base_domain = urllib.parse.urlparse(base_url).netloc
        link_domain = urllib.parse.urlparse(href).netloc

        if link_domain and base_domain != link_domain:
            continue

        links.append(href)

    return links

def scrape_url(url, depth=0):
    """Scrape content, images, and links from a URL with optional recursive depth."""
    if depth < 0:
        depth = 0
    if depth > 3:  # Limit maximum depth to prevent excessive scraping
        depth = 3

    # Set a maximum content size to prevent batch size errors
    MAX_CONTENT_SIZE = 100000  # Characters per page
    MAX_TOTAL_CONTENT = 500000  # Total characters across all pages

    # Set a reasonable timeout and max retries
    TIMEOUT = 10  # seconds
    MAX_RETRIES = 2

    # Check if URL is valid before proceeding
    if not url or not url.startswith(('http://', 'https://')):
        logger.warning(f"Invalid URL format: {url}")
        return {
            "pages": [{
                "url": url,
                "text": f"[Unable to scrape: Invalid URL format] {url}",
                "images": [],
                "links": [],
                "depth": 0
            }],
            "images": [],
            "links": [],
            "pages_scraped": 1,
            "max_depth_reached": 0,
            "error": "Invalid URL format"
        }

    visited_urls = set()
    all_results = []
    urls_to_scrape = [(url, 0)]  # (url, current_depth)
    total_content_size = 0

    while urls_to_scrape and total_content_size < MAX_TOTAL_CONTENT:
        current_url, current_depth = urls_to_scrape.pop(0)

        # Skip if already visited
        if current_url in visited_urls:
            continue

        logger.info(f"Scraping URL: {current_url} (depth {current_depth}/{depth})")
        visited_urls.add(current_url)  # Mark as visited before attempting to scrape

        # Initialize default values in case of failure
        images = []
        doc_links = []
        full_text = f"[Unable to scrape content from {current_url}]"
        error_message = None

        try:
            # Disable SSL verification warnings
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # Attempt request with retries
            for retry in range(MAX_RETRIES + 1):
                try:
                    response = requests.get(current_url, timeout=TIMEOUT, verify=False)
                    response.raise_for_status()
                    break  # Success, exit retry loop
                except requests.exceptions.Timeout:
                    if retry < MAX_RETRIES:
                        logger.warning(f"Request timed out for {current_url}, retrying ({retry+1}/{MAX_RETRIES})...")
                        continue
                    raise  # Re-raise if all retries failed
                except requests.exceptions.ConnectionError as conn_err:
                    # Check if it's a DNS resolution error
                    if "getaddrinfo failed" in str(conn_err) or "Name or service not known" in str(conn_err):
                        error_message = f"DNS resolution failed for {current_url}"
                        logger.warning(error_message)
                        raise
                    if retry < MAX_RETRIES:
                        logger.warning(f"Connection error for {current_url}, retrying ({retry+1}/{MAX_RETRIES})...")
                        continue
                    raise  # Re-raise if all retries failed
                except Exception as e:
                    if retry < MAX_RETRIES:
                        logger.warning(f"Error accessing {current_url}, retrying ({retry+1}/{MAX_RETRIES}): {str(e)}")
                        continue
                    raise  # Re-raise if all retries failed

            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract images and links before removing elements
            images, doc_links = extract_images_and_links_from_html(soup, current_url)

            # Get all page links if we need to go deeper
            if current_depth < depth:
                page_links = extract_page_links(soup, current_url)
                # Limit the number of links to follow per page to prevent excessive scraping
                if page_links:
                    # Sort links by length (shorter URLs are often more relevant)
                    page_links.sort(key=len)
                    # Take at most 10 links per page
                    page_links = page_links[:10]
                # Add links to the queue for the next depth level
                for link in page_links:
                    if link not in visited_urls:
                        urls_to_scrape.append((link, current_depth + 1))

            # Remove unwanted elements for text extraction
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                element.decompose()

            # Get the main text content
            text = soup.get_text(separator=' ', strip=True)
            text = ' '.join(text.split())

            if not text:
                logger.warning(f"No content extracted from URL {current_url}")
                text = f"[No meaningful text content could be extracted from {current_url}]"

            # Limit text size per page to prevent excessive content
            if len(text) > MAX_CONTENT_SIZE:
                logger.warning(f"Content from {current_url} truncated from {len(text)} to {MAX_CONTENT_SIZE} characters")
                text = text[:MAX_CONTENT_SIZE] + "... [content truncated due to size]"

            # Add extracted images and links to the text
            images_text = ""
            if images:
                images_text = "\n\nImages found on page: " + ", ".join(images[:5])
                if len(images) > 5:
                    images_text += f" and {len(images) - 5} more"

            links_text = ""
            if doc_links:
                links_text = "\n\nDocument links found on page: " + ", ".join(doc_links[:5])
                if len(doc_links) > 5:
                    links_text += f" and {len(doc_links) - 5} more"

            # Add depth information to the text
            header_text = ""
            if current_depth > 0:
                header_text = f"[Page at depth {current_depth}] {current_url}\n\n"
            else:
                header_text = f"[Main page] {current_url}\n\n"

            # Combine all text parts
            full_text = header_text + text + images_text + links_text

            # Update total content size
            total_content_size += len(full_text)

            # Check if we've exceeded the maximum total content size
            if total_content_size > MAX_TOTAL_CONTENT:
                logger.warning(f"Maximum total content size reached ({MAX_TOTAL_CONTENT} characters). Stopping scraping.")
                # Add a note about truncation
                full_text += "\n\n[Note: Content collection stopped due to size limits]"

        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e)

            # Create a more user-friendly error message based on the exception type
            if isinstance(e, requests.exceptions.Timeout):
                error_message = f"Request timed out after {TIMEOUT} seconds"
            elif isinstance(e, requests.exceptions.ConnectionError):
                if "getaddrinfo failed" in str(e):
                    error_message = f"DNS resolution failed (domain name could not be resolved)"
                else:
                    error_message = f"Connection error (server may be down or unreachable)"
            elif isinstance(e, requests.exceptions.HTTPError):
                error_message = f"HTTP error: {e.response.status_code} {e.response.reason}"
            elif isinstance(e, requests.exceptions.TooManyRedirects):
                error_message = "Too many redirects"
            elif isinstance(e, requests.exceptions.RequestException):
                error_message = f"Request error: {str(e)}"

            logger.error(f"Failed to scrape URL {current_url}: {error_type} - {error_message}")

            # Add a placeholder for the failed URL
            full_text = f"[Unable to scrape content from {current_url}: {error_message}]"

        # Add this page's results, even if it failed (with error information)
        all_results.append({
            "url": current_url,
            "text": full_text,
            "images": images,
            "links": doc_links,
            "depth": current_depth,
            "error": error_message  # Will be None if no error occurred
        })

    # If we couldn't scrape any content but have error information, return that
    if not all_results:
        logger.error(f"No content could be extracted from any URLs starting with {url}")
        return {
            "pages": [{
                "url": url,
                "text": f"[Unable to scrape content from {url}]",
                "images": [],
                "links": [],
                "depth": 0,
                "error": "Failed to extract any content"
            }],
            "images": [],
            "links": [],
            "pages_scraped": 1,
            "max_depth_reached": 0,
            "error": "No content could be extracted"
        }

    # Combine images and links from all results
    combined_images = []
    combined_links = []

    for result in all_results:
        combined_images.extend(result.get("images", []))
        combined_links.extend(result.get("links", []))

    # Remove duplicates while preserving order
    combined_images = list(dict.fromkeys(combined_images))
    combined_links = list(dict.fromkeys(combined_links))

    logger.info(f"Scraped {len(all_results)} pages from {url} with depth {depth}")

    return {
        "pages": all_results,  # Return individual page results instead of combined text
        "images": combined_images,
        "links": combined_links,
        "pages_scraped": len(all_results),
        "max_depth_reached": max([r.get("depth", 0) for r in all_results]) if all_results else 0,
        "error": None  # No overall error if we got this far
    }

@monitor_embedding_operation
def embed_file_task(file_path, filename, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, use_llamaindex=True):
    """
    Background task for embedding a PDF file. file_path is a path to the saved PDF.
    """
    try:
        # Import the directory creation function
        from scripts.setup.create_temp_dirs import create_pdf_directory_structure

        # Get vision settings from environment variables if not provided
        if use_vision is None:
            use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

        if filter_sensitivity is None:
            filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

        if max_images is None:
            try:
                max_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))
            except (ValueError, TypeError):
                max_images = 10

        # Create a timestamped filename to avoid collisions
        # filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}" # This line is removed as filename is now passed as an argument

        # Create the directory structure for this PDF
        dir_structure = create_pdf_directory_structure(category, filename)
        if not dir_structure:
            logger.error(f"Failed to create directory structure for {filename}")
            return False, f"Failed to create directory structure for {filename}"

        # Get the path to save the PDF
        dest = dir_structure["pdf_path"]

        # Save the uploaded file
        # file.save(dest) # This line is removed as file_path is now passed as an argument
        os.rename(file_path, dest) # Use os.rename to move the file from temp to dest

        # Process the PDF with advanced capabilities
        logger.info(f"Processing PDF {filename} with advanced capabilities...")
        if use_vision:
            logger.info(f"Vision model analysis enabled with sensitivity: {filter_sensitivity}, max images: {max_images}")
        else:
            logger.info("Vision model analysis disabled")

        # First, process the PDF to extract all content (text, images, tables, links, locations)
        # This avoids duplicate processing and image extraction
        pdf_info = process_pdf(dest, category, source_url,
                              use_vision=use_vision,
                              filter_sensitivity=filter_sensitivity,
                              max_images=max_images,
                              extract_locations=True)

        # Extract publication date fields from pdf_info['metadata']
        pubmeta = pdf_info.get('metadata', {})
        pub_year = pubmeta.get('published_year')
        pub_month_start = pubmeta.get('published_month_start')
        pub_month_end = pubmeta.get('published_month_end')
        pub_month_range_str = pubmeta.get('published_month_range_str')

        # Extract articles metadata (list of dicts with page, title, authors)
        articles = pubmeta.get('articles', [])
        from collections import defaultdict
        articles_by_page = defaultdict(list)
        for art in articles:
            articles_by_page[art["page"]].append(art)

        # Then convert the processed PDF to document chunks for vector storage
        chunks = []

        if pdf_info["text"]:
            # Create documents from the processed PDF
            documents = []
            for page in pdf_info["text"]:
                page_num = page["page"]
                page_text = page["text"]

                # Create metadata
                metadata = {
                    "source": filename,
                    "original_filename": filename,
                    "citation_filename": filename,  # Add this field specifically for citations
                    "page": page_num,
                    "type": "pdf",
                    "extraction_method": page.get("extraction_method", "standard"),
                    "published_year": pub_year,
                    "published_month_start": pub_month_start,
                    "published_month_end": pub_month_end,
                    "published_month_range_str": pub_month_range_str
                }

                # Add source URL if provided
                if source_url:
                    metadata["original_url"] = source_url

                # Add category
                metadata["category"] = category

                # Add images for this page
                page_images = [img for img in pdf_info["images"] if img.get("page") == page_num]
                if page_images:
                    metadata["images"] = json.dumps(page_images)
                    metadata["image_count"] = len(page_images)

                # Add links
                if pdf_info["links"]:
                    metadata["pdf_links"] = json.dumps(pdf_info["links"])
                    metadata["link_count"] = len(pdf_info["links"])

                # Add article title/authors for this page if available
                page_articles = articles_by_page.get(page_num, [])
                if page_articles:
                    metadata["article_title"] = page_articles[0]["title"]
                    metadata["article_authors"] = page_articles[0]["authors"]

                # Create document
                documents.append(Document(page_content=page_text, metadata=metadata))

            # Use semantic chunking if available, otherwise fall back to recursive
            if SEMANTIC_CHUNKING_AVAILABLE:
                try:
                    # Create semantic chunking service with optimal RAG configuration
                    semantic_chunker = create_semantic_chunking_service(
                        strategy="hybrid",
                        chunk_size=800,
                        chunk_overlap=160,
                        extract_article_metadata=True,
                        extract_publication_info=True,
                        validate_metadata=True
                    )
                    
                    # Extract enhanced metadata first
                    article_metadata = semantic_chunker.extract_article_metadata(documents)
                    
                    # Process chunks with semantic chunking
                    chunks = semantic_chunker.chunk_documents(documents)
                    
                    # Add enhanced metadata to chunks
                    for chunk in chunks:
                        if article_metadata.title:
                            chunk.metadata["article_title"] = article_metadata.title
                        if article_metadata.authors:
                            chunk.metadata["article_authors"] = article_metadata.authors
                        if article_metadata.abstract:
                            chunk.metadata["article_abstract"] = article_metadata.abstract
                        if article_metadata.keywords:
                            chunk.metadata["article_keywords"] = article_metadata.keywords
                        if article_metadata.publication_info:
                            chunk.metadata["publication_info"] = article_metadata.publication_info
                        if article_metadata.confidence_scores:
                            chunk.metadata["metadata_confidence"] = article_metadata.confidence_scores
                    
                    logger.info(f"Used semantic chunking for {filename}, created {len(chunks)} chunks")
                    logger.info(f"Enhanced metadata: title='{article_metadata.title}', "
                               f"authors={len(article_metadata.authors)}, "
                               f"confidence={article_metadata.confidence_scores}")
                               
                except Exception as e:
                    logger.warning(f"Semantic chunking failed for {filename}: {e}. Falling back to recursive chunking.")
                    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
                    chunks = splitter.split_documents(documents)
            else:
                # Fall back to recursive chunking
                splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
                chunks = splitter.split_documents(documents)

            # Ensure all chunks have the source filename and URL if provided
            for doc in chunks:
                doc.metadata["source"] = filename
                doc.metadata["original_filename"] = filename
                doc.metadata["citation_filename"] = filename
                doc.metadata["type"] = "pdf"
                if source_url:
                    doc.metadata["original_url"] = source_url

        if not chunks:
            # Fall back to the original method if the new one fails
            logger.warning(f"Advanced PDF processing failed for {filename}, falling back to basic method")

            # Use the updated extract_text_from_pdf function that avoids duplicate image extraction
            pages = extract_text_from_pdf(dest, category, source_url)

            if not pages:
                return False, f"Failed to extract text from {filename}"

            # Create documents with enhanced metadata
            docs = []
            for page in pages:
                metadata = {
                    "source": filename,
                    "original_filename": filename,
                    "citation_filename": filename,  # Add this field specifically for citations
                    "page": page.metadata.get("page", None),
                    "type": "pdf"
                }

                # Add source URL if provided
                if source_url:
                    metadata["original_url"] = source_url

                # Use the image information from page metadata
                if page.metadata.get("images"):
                    metadata["images"] = json.dumps(page.metadata.get("images"))

                # Use the link information from page metadata
                if page.metadata.get("pdf_links"):
                    metadata["pdf_links"] = json.dumps(page.metadata.get("pdf_links"))

                # Add article title/authors for this page if available (fallback)
                page_num = page.metadata.get("page", None)
                page_articles = articles_by_page.get(page_num, [])
                if page_articles:
                    metadata["article_title"] = page_articles[0]["title"]
                    metadata["article_authors"] = page_articles[0]["authors"]

                docs.append(Document(page_content=page.page_content, metadata=metadata))

            # Use semantic chunking if available, otherwise fall back to recursive
            if SEMANTIC_CHUNKING_AVAILABLE:
                try:
                    # Create semantic chunking service with optimal RAG configuration
                    semantic_chunker = create_semantic_chunking_service(
                        strategy="hybrid",
                        chunk_size=800,
                        chunk_overlap=160
                    )
                    chunks = semantic_chunker.chunk_documents(docs)
                    logger.info(f"Used semantic chunking (fallback) for {filename}, created {len(chunks)} chunks")
                except Exception as e:
                    logger.warning(f"Semantic chunking (fallback) failed for {filename}: {e}. Falling back to recursive chunking.")
                    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
                    chunks = splitter.split_documents(docs)
            else:
                # Fall back to recursive chunking
                splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
                chunks = splitter.split_documents(docs)

            # Ensure all chunks have the source filename and URL if provided
            for doc in chunks:
                doc.metadata["source"] = filename
                doc.metadata["original_filename"] = filename
                doc.metadata["citation_filename"] = filename  # Add this field specifically for citations
                doc.metadata["type"] = "pdf"
                if source_url:
                    doc.metadata["original_url"] = source_url

        # Also update fallback chunk metadata
        for doc in chunks:
            doc.metadata["published_year"] = pub_year
            doc.metadata["published_month_start"] = pub_month_start
            doc.metadata["published_month_end"] = pub_month_end
            doc.metadata["published_month_range_str"] = pub_month_range_str

        # Add to vector database using LlamaIndex if enabled, otherwise use LangChain
        if use_llamaindex:
            try:
                # Convert LangChain documents to LlamaIndex documents
                from llama_index.core import Document as LlamaIndexDocument
                llamaindex_docs = []
                for doc in chunks:
                    llamaindex_doc = LlamaIndexDocument(
                        text=doc.page_content,
                        metadata=doc.metadata.copy() if hasattr(doc, 'metadata') and doc.metadata else {},
                        id_=doc.metadata.get('id', None) if hasattr(doc, 'metadata') and doc.metadata else None
                    )
                    llamaindex_docs.append(llamaindex_doc)
                
                # Add to LlamaIndex vector database
                llamaindex_db = get_llamaindex_vector_db()
                llamaindex_db.add_documents(llamaindex_docs, category)
                logger.info(f"Added {len(llamaindex_docs)} documents to LlamaIndex vector database for category: {category}")
            except Exception as e:
                logger.warning(f"Failed to add documents to LlamaIndex, falling back to LangChain: {str(e)}")
                # Fallback to LangChain
                add_documents_with_category(chunks, category)
        else:
            # Use traditional LangChain approach
            add_documents_with_category(chunks, category)

        # Get metadata from the processed PDF
        image_count = pdf_info["metadata"]["image_count"]
        table_count = pdf_info["metadata"]["table_count"]
        link_count = pdf_info["metadata"]["link_count"]

        # Add vision analysis metadata to success message
        images_filtered = pdf_info["metadata"].get("images_filtered", 0)
        vision_enabled = pdf_info["metadata"].get("vision_enabled", False)

        # Add the PDF directory path to the metadata for all chunks
        pdf_base_name = os.path.splitext(filename)[0]
        pdf_dir_path = f"/{category}/{pdf_base_name}"

        # Update all chunks with the PDF directory path
        for doc in chunks:
            doc.metadata["pdf_dir"] = pdf_dir_path

        success_message = f"Successfully embedded {filename} with {len(chunks)} chunks"
        if image_count > 0:
            success_message += f", {image_count} images"
            # Add vision analysis details if enabled
            if vision_enabled:
                success_message += f" ({images_filtered} filtered by vision model)"
        if table_count > 0:
            success_message += f", {table_count} tables"
        if link_count > 0:
            success_message += f", {link_count} links"

        logger.info(success_message)
        if source_url:
            logger.info(f"PDF {filename} linked to source URL: {source_url}")

        return True, success_message
    except Exception as e:
        logger.error(f"Failed to embed file {filename}: {str(e)}")
        return False, f"Failed to embed {filename}: {str(e)}"


def embed_file(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None):
    """
    Enqueue embedding as a background job. Returns (success, job_id or error message).
    """
    try:
        from rq import Queue
        from redis import Redis
        import uuid
        # Set up Redis connection and RQ queue (use default Redis URL or env var)
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        redis_conn = Redis.from_url(redis_url)
        embedding_queue = Queue('embedding', connection=redis_conn)
        # Save the uploaded file to a temp location
        temp_dir = os.getenv('TEMP_FOLDER', './data/temp')
        os.makedirs(temp_dir, exist_ok=True)
        job_id = str(uuid.uuid4())
        temp_path = os.path.join(temp_dir, f"{job_id}_{secure_filename(file.filename)}")
        file.save(temp_path)

        # Enqueue the background job
        job = embedding_queue.enqueue(
            embed_file_task,
            temp_path,
            file.filename,
            category,
            source_url,
            use_vision,
            filter_sensitivity,
            max_images,
            job_id=job_id
        )
        logger.info(f"Enqueued embedding job {job.id} for file {file.filename}")
        return True, job.id
    except Exception as e:
        logger.error(f"Failed to enqueue embedding job for {file.filename}: {str(e)}")
        return False, f"Failed to enqueue embedding job: {str(e)}"


def get_embedding_job_status(job_id):
    """
    Check the status of an embedding job by job_id.
    """
    from rq import Queue
    from redis import Redis
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    redis_conn = Redis.from_url(redis_url)
    embedding_queue = Queue('embedding', connection=redis_conn)
    job = embedding_queue.fetch_job(job_id)
    if not job:
        return 'not_found'
    return job.get_status()

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def batch_embed_documents(documents: List[Document], category: str, max_workers: int = None) -> Dict[str, Any]:
    """
    Embed multiple documents in batch with optimization.

    Args:
        documents: List of Document objects to embed
        category: Category for organizing content
        max_workers: Maximum number of worker threads

    Returns:
        Dictionary with batch embedding results
    """
    if not documents:
        return {"success": False, "error": "No documents provided"}

    logger.info(f"Starting batch embedding of {len(documents)} documents")

    # Split documents into chunks for better memory management
    chunk_size = 50  # Process 50 documents at a time
    document_chunks = [documents[i:i + chunk_size] for i in range(0, len(documents), chunk_size)]

    successful_embeddings = 0
    failed_embeddings = 0
    total_processing_time = 0
    errors = []

    try:
        for i, doc_chunk in enumerate(document_chunks):
            logger.info(f"Processing document chunk {i + 1}/{len(document_chunks)} ({len(doc_chunk)} documents)")

            start_time = time.time()

            try:
                # Add documents to vector database
                add_documents_with_category(doc_chunk, category)
                successful_embeddings += len(doc_chunk)

            except Exception as e:
                error_msg = f"Chunk {i + 1} failed: {str(e)}"
                errors.append(error_msg)
                failed_embeddings += len(doc_chunk)
                logger.error(error_msg)

            chunk_time = time.time() - start_time
            total_processing_time += chunk_time

            logger.info(f"Chunk {i + 1} processed in {chunk_time:.2f}s")

            # Small delay between chunks to prevent overwhelming the system
            if i < len(document_chunks) - 1:
                time.sleep(0.1)

    except Exception as e:
        error_msg = f"Batch embedding failed: {str(e)}"
        errors.append(error_msg)
        logger.error(error_msg)

    return {
        'success': failed_embeddings == 0,
        'total_documents': len(documents),
        'successful_embeddings': successful_embeddings,
        'failed_embeddings': failed_embeddings,
        'processing_time': total_processing_time,
        'avg_time_per_document': total_processing_time / len(documents) if documents else 0,
        'errors': errors
    }

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def batch_process_urls(urls: List[str], category: str, max_workers: int = None,
                      max_depth: int = 1) -> Dict[str, Any]:
    """
    Process multiple URLs in batch with optimization.

    Args:
        urls: List of URLs to process
        category: Category for organizing content
        max_workers: Maximum number of worker threads
        max_depth: Maximum crawling depth

    Returns:
        Dictionary with batch processing results
    """
    if not urls:
        return {"success": False, "error": "No URLs provided"}

    logger.info(f"Starting batch processing of {len(urls)} URLs")

    # Use batch processor
    result = batch_process_documents(
        documents=urls,
        processor_func=_process_single_url_optimized,
        max_workers=max_workers,
        category=category,
        max_depth=max_depth
    )

    # Aggregate results
    successful_urls = []
    failed_urls = []
    total_pages = 0
    total_documents = 0

    for i, url_result in enumerate(result.results):
        if isinstance(url_result, dict) and not url_result.get('error'):
            successful_urls.append({
                'url': urls[i] if i < len(urls) else 'unknown',
                'result': url_result
            })

            # Aggregate statistics
            total_pages += url_result.get('pages_processed', 0)
            total_documents += url_result.get('documents_created', 0)
        else:
            failed_urls.append({
                'url': urls[i] if i < len(urls) else 'unknown',
                'error': url_result.get('error', 'Unknown error') if isinstance(url_result, dict) else str(url_result)
            })

    return {
        'success': result.success,
        'processing_time': result.processing_time,
        'total_urls': len(urls),
        'successful_urls': len(successful_urls),
        'failed_urls': len(failed_urls),
        'total_pages': total_pages,
        'total_documents': total_documents,
        'results': successful_urls,
        'errors': failed_urls,
        'batch_errors': result.errors
    }

def _process_single_url_optimized(url: str, category: str, max_depth: int = 1) -> Dict[str, Any]:
    """
    Process a single URL with optimization and error handling.

    Args:
        url: URL to process
        category: Category for organizing content
        max_depth: Maximum crawling depth

    Returns:
        Dictionary with processing results
    """
    try:
        # Validate URL
        from urllib.parse import urlparse
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError(f"Invalid URL: {url}")

        # Process the URL
        visited_urls = set()
        documents = scrape_single_url(url, visited_urls)

        if not documents:
            return {
                'error': 'No content extracted from URL',
                'url': url,
                'pages_processed': 0,
                'documents_created': 0
            }

        # Add documents to vector database
        add_documents_with_category(documents, category)

        return {
            'url': url,
            'pages_processed': 1,
            'documents_created': len(documents),
            'success': True
        }

    except Exception as e:
        logger.error(f"Error processing URL {url}: {str(e)}")
        return {
            'error': str(e),
            'url': url,
            'pages_processed': 0,
            'documents_created': 0
        }

@performance_monitor(track_memory=True, track_cpu=True)
def optimize_embedding_cache():
    """
    Optimize embedding cache and temporary files.
    """
    try:
        temp_folder = os.getenv('TEMP_FOLDER', './data/temp')
        if not os.path.exists(temp_folder):
            return

        # Clean up old temporary files
        cutoff_time = time.time() - (12 * 60 * 60)  # 12 hours
        removed_count = 0
        freed_space = 0

        for file in os.listdir(temp_folder):
            file_path = os.path.join(temp_folder, file)
            try:
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    if stat.st_mtime < cutoff_time:
                        file_size = stat.st_size
                        os.remove(file_path)
                        removed_count += 1
                        freed_space += file_size
            except Exception as e:
                logger.error(f"Error removing temp file {file_path}: {str(e)}")

        if removed_count > 0:
            logger.info(f"Embedding cache cleanup: removed {removed_count} files, "
                       f"freed {freed_space / 1024 / 1024:.1f}MB")

    except Exception as e:
        logger.error(f"Error optimizing embedding cache: {str(e)}")

# Schedule embedding cache optimization
def _schedule_embedding_cache_optimization():
    """Schedule periodic embedding cache optimization."""
    import threading

    def cache_cleanup_loop():
        while True:
            try:
                time.sleep(3600)  # Run every hour
                optimize_embedding_cache()
            except Exception as e:
                logger.error(f"Error in embedding cache cleanup loop: {str(e)}")
                time.sleep(300)  # Wait 5 minutes before retrying

    cleanup_thread = threading.Thread(target=cache_cleanup_loop, daemon=True)
    cleanup_thread.start()

# Start embedding cache optimization on module import
_schedule_embedding_cache_optimization()
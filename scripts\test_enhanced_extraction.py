#!/usr/bin/env python3
"""
Test script for enhanced academic PDF extraction functionality.

This script provides comprehensive testing of:
1. Enhanced title extraction with ALL CAPS conversion
2. Enhanced author extraction with multiple format support
3. Confidence scoring system
4. Article storage and retrieval
5. API endpoints
6. Performance benchmarking

Usage:
    python scripts/test_enhanced_extraction.py [options]

Options:
    --test-pdf PATH        Path to test PDF file
    --test-all            Run all tests
    --test-extraction     Test extraction functions only
    --test-api            Test API endpoints only
    --test-db             Test database functions only
    --benchmark           Run performance benchmarks
    --debug               Enable debug output
"""

import os
import sys
import argparse
import logging
import time
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.pdf_processor import (
    extract_academic_titles_enhanced,
    extract_academic_authors_enhanced,
    extract_articles_with_confidence
)
from app.utils.articles_db import (
    store_extracted_article,
    search_articles,
    get_article_statistics,
    store_articles_from_extraction
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_title_extraction(pdf_path, debug=False):
    """Test enhanced title extraction functionality."""
    logger.info("Testing enhanced title extraction...")
    
    try:
        start_time = time.time()
        titles = extract_academic_titles_enhanced(pdf_path, debug=debug)
        extraction_time = time.time() - start_time
        
        logger.info(f"Title extraction completed in {extraction_time:.2f} seconds")
        logger.info(f"Found {len(titles)} title candidates")
        
        for i, title_data in enumerate(titles[:5], 1):  # Show first 5
            logger.info(f"  {i}. '{title_data['title'][:60]}...' (confidence: {title_data['confidence']:.3f})")
            logger.info(f"     Page: {title_data['page']}, Font: {title_data['font_size']}")
            if debug:
                logger.info(f"     Original: '{title_data['title_original_case'][:60]}...'")
        
        return {
            'success': True,
            'count': len(titles),
            'time': extraction_time,
            'titles': titles
        }
        
    except Exception as e:
        logger.error(f"Title extraction test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_author_extraction(pdf_path, title_regions=None, debug=False):
    """Test enhanced author extraction functionality."""
    logger.info("Testing enhanced author extraction...")
    
    try:
        start_time = time.time()
        authors = extract_academic_authors_enhanced(
            pdf_path, 
            title_regions=title_regions,
            debug=debug
        )
        extraction_time = time.time() - start_time
        
        logger.info(f"Author extraction completed in {extraction_time:.2f} seconds")
        logger.info(f"Found {len(authors)} author candidates")
        
        for i, author_data in enumerate(authors[:5], 1):  # Show first 5
            logger.info(f"  {i}. '{author_data['authors'][:60]}...' (confidence: {author_data['confidence']:.3f})")
            logger.info(f"     Page: {author_data['page']}, Format: {author_data.get('author_format', 'unknown')}")
            if debug:
                logger.info(f"     Original: '{author_data['authors_original_case'][:60]}...'")
        
        return {
            'success': True,
            'count': len(authors),
            'time': extraction_time,
            'authors': authors
        }
        
    except Exception as e:
        logger.error(f"Author extraction test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_full_extraction(pdf_path, debug=False):
    """Test full article extraction with confidence scoring."""
    logger.info("Testing full article extraction with confidence scoring...")
    
    try:
        start_time = time.time()
        articles = extract_articles_with_confidence(
            pdf_path,
            min_overall_confidence=0.5,  # Lower threshold for testing
            debug=debug
        )
        extraction_time = time.time() - start_time
        
        logger.info(f"Full extraction completed in {extraction_time:.2f} seconds")
        logger.info(f"Found {len(articles)} articles with confidence >= 0.5")
        
        for i, article in enumerate(articles, 1):
            logger.info(f"  {i}. '{article['title'][:50]}...'")
            logger.info(f"     Authors: <AUTHORS>
            logger.info(f"     Page: {article['page']}, Overall confidence: {article['overall_confidence']:.3f}")
            logger.info(f"     Title confidence: {article['title_confidence']:.3f}, Author confidence: {article['authors_confidence']:.3f}")
        
        return {
            'success': True,
            'count': len(articles),
            'time': extraction_time,
            'articles': articles
        }
        
    except Exception as e:
        logger.error(f"Full extraction test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_database_functions():
    """Test database storage and retrieval functions."""
    logger.info("Testing database functions...")
    
    try:
        # Test article storage
        test_article_id = store_extracted_article(
            pdf_document_id=999999,  # Test ID
            page_number=1,
            title="TEST ARTICLE TITLE",
            authors="TEST AUTHOR ONE AND TEST AUTHOR TWO",
            title_confidence=0.95,
            authors_confidence=0.88,
            overall_confidence=0.92,
            extraction_method="test_enhanced",
            font_size_title=16.0,
            font_size_authors=12.0,
            position_data={"title_position": {"x": 100, "y": 200}, "author_position": {"x": 100, "y": 250}},
            title_original_case="Test Article Title",
            authors_original_case="Test Author One and Test Author Two"
        )
        
        if test_article_id:
            logger.info(f"Successfully stored test article with ID: {test_article_id}")
            
            # Test search functionality
            search_results = search_articles(
                search_query="TEST ARTICLE",
                min_confidence=0.8,
                limit=10
            )
            
            logger.info(f"Search found {len(search_results['articles'])} articles")
            
            # Test statistics
            stats = get_article_statistics()
            logger.info(f"Database statistics: {stats}")
            
            # Clean up test data
            from app.utils.articles_db import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute('DELETE FROM extracted_articles WHERE id = ?', (test_article_id,))
            conn.commit()
            conn.close()
            logger.info("Cleaned up test data")
            
            return {'success': True, 'test_id': test_article_id}
        else:
            return {'success': False, 'error': 'Failed to store test article'}
            
    except Exception as e:
        logger.error(f"Database test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def test_api_endpoints():
    """Test API endpoints (requires running server)."""
    logger.info("Testing API endpoints...")
    
    try:
        import requests
        
        base_url = "http://localhost:8080/api"
        
        # Test articles listing endpoint
        response = requests.get(f"{base_url}/articles", params={'limit': 5})
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Articles API returned {len(data.get('data', {}).get('articles', []))} articles")
        else:
            logger.warning(f"Articles API returned status {response.status_code}")
        
        # Test statistics endpoint
        response = requests.get(f"{base_url}/articles/statistics")
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Statistics API returned: {data.get('data', {})}")
        else:
            logger.warning(f"Statistics API returned status {response.status_code}")
        
        return {'success': True}
        
    except ImportError:
        logger.warning("requests library not available, skipping API tests")
        return {'success': False, 'error': 'requests library not available'}
    except Exception as e:
        logger.error(f"API test failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def benchmark_extraction(pdf_path, iterations=3):
    """Benchmark extraction performance."""
    logger.info(f"Benchmarking extraction performance ({iterations} iterations)...")
    
    times = []
    
    for i in range(iterations):
        logger.info(f"Benchmark iteration {i+1}/{iterations}")
        start_time = time.time()
        
        articles = extract_articles_with_confidence(pdf_path, debug=False)
        
        iteration_time = time.time() - start_time
        times.append(iteration_time)
        logger.info(f"  Iteration {i+1}: {iteration_time:.2f}s, {len(articles)} articles")
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    logger.info(f"Benchmark results:")
    logger.info(f"  Average time: {avg_time:.2f}s")
    logger.info(f"  Min time: {min_time:.2f}s")
    logger.info(f"  Max time: {max_time:.2f}s")
    
    return {
        'avg_time': avg_time,
        'min_time': min_time,
        'max_time': max_time,
        'times': times
    }

def main():
    parser = argparse.ArgumentParser(description='Test enhanced academic PDF extraction')
    parser.add_argument('--test-pdf', help='Path to test PDF file')
    parser.add_argument('--test-all', action='store_true', help='Run all tests')
    parser.add_argument('--test-extraction', action='store_true', help='Test extraction functions only')
    parser.add_argument('--test-api', action='store_true', help='Test API endpoints only')
    parser.add_argument('--test-db', action='store_true', help='Test database functions only')
    parser.add_argument('--benchmark', action='store_true', help='Run performance benchmarks')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Starting enhanced extraction tests")
    
    results = {}
    
    # Test database functions
    if args.test_all or args.test_db:
        results['database'] = test_database_functions()
    
    # Test API endpoints
    if args.test_all or args.test_api:
        results['api'] = test_api_endpoints()
    
    # Test extraction functions (requires PDF)
    if (args.test_all or args.test_extraction or args.benchmark) and args.test_pdf:
        if not os.path.exists(args.test_pdf):
            logger.error(f"Test PDF not found: {args.test_pdf}")
            return
        
        # Test title extraction
        results['titles'] = test_title_extraction(args.test_pdf, debug=args.debug)
        
        # Test author extraction
        title_regions = results['titles'].get('titles', []) if results['titles']['success'] else None
        results['authors'] = test_author_extraction(args.test_pdf, title_regions, debug=args.debug)
        
        # Test full extraction
        results['full_extraction'] = test_full_extraction(args.test_pdf, debug=args.debug)
        
        # Benchmark if requested
        if args.benchmark:
            results['benchmark'] = benchmark_extraction(args.test_pdf)
    
    # Print summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    for test_name, result in results.items():
        status = "PASS" if result.get('success', False) else "FAIL"
        logger.info(f"{test_name.upper()}: {status}")
        if not result.get('success', False) and 'error' in result:
            logger.info(f"  Error: {result['error']}")
    
    logger.info("="*50)

if __name__ == "__main__":
    main()

import re
import spacy
import os
import logging
import requests
from typing import Set, Dict, <PERSON><PERSON>

# Set up logging
logger = logging.getLogger(__name__)

# Microservice URL from environment variable
SCISPA_MICROSERVICE_URL = os.getenv("SCI_NAME_MICROSERVICE_URL")

# Load ScispaCy CRAFT model only once (for local fallback)
try:
    _scispacy_nlp = spacy.load("en_ner_craft_md")
except Exception as e:
    _scispacy_nlp = None
    logger.warning(f"ScispaCy CRAFT model not loaded: {e} (scientific name detection will use regex fallback only)")

def _preserve_urls(text: str) -> Tuple[str, Dict[str, str]]:
    """
    Temporarily replace URLs and HTML links with placeholders to preserve them during scientific name processing.
    Returns modified text and a dictionary mapping placeholders to original URLs.
    """
    url_placeholders = {}
    placeholder_counter = 0
    
    # Preserve complete HTML links first (more specific)
    html_link_pattern = r'<a[^>]*href="([^"]*)"[^>]*>([^<]*)</a>'
    def replace_html_link(match):
        nonlocal placeholder_counter
        placeholder = f"__HTML_LINK_{placeholder_counter}__"
        url_placeholders[placeholder] = match.group(0)
        placeholder_counter += 1
        return placeholder
    
    text = re.sub(html_link_pattern, replace_html_link, text)
    
    # Preserve standalone URLs (less specific)
    url_pattern = r'https?://[^\s<>"]+|/download_gated/[^\s<>"]+|www\.[^\s<>"]+\.[\w]+'
    def replace_url(match):
        nonlocal placeholder_counter
        placeholder = f"__URL_{placeholder_counter}__"
        url_placeholders[placeholder] = match.group(0)
        placeholder_counter += 1
        return placeholder
    
    text = re.sub(url_pattern, replace_url, text)
    
    # Also preserve file paths that might contain scientific-name-like patterns
    filepath_pattern = r'/[^\s<>"]+\.(?:pdf|doc|docx|jpg|jpeg|png|gif)'
    def replace_filepath(match):
        nonlocal placeholder_counter
        placeholder = f"__FILEPATH_{placeholder_counter}__"
        url_placeholders[placeholder] = match.group(0)
        placeholder_counter += 1
        return placeholder
    
    text = re.sub(filepath_pattern, replace_filepath, text)
    
    return text, url_placeholders

def _restore_urls(text: str, url_placeholders: Dict[str, str]) -> str:
    """
    Restore URLs from placeholders back to the original text.
    """
    for placeholder, original_url in url_placeholders.items():
        text = text.replace(placeholder, original_url)
    return text

def _find_scispacy_names(text: str) -> Set[str]:
    """
    Use ScispaCy CRAFT model to extract scientific names (TAXON entities) from text.
    Returns a set of unique names.
    """
    if not _scispacy_nlp:
        return set()
    doc = _scispacy_nlp(text)
    return set(ent.text for ent in doc.ents if ent.label_ == "TAXON")

def _find_regex_names(text: str) -> Set[str]:
    """
    Use restrictive regex to extract actual scientific names only:
    - 'Genus sp.' or 'Genus spp.' optionally followed by a number (e.g., 'Genus sp. 3')
    - Binomial names that look like Latin scientific names (not common English phrases)
    - Single-word genus names in a list context (e.g., '5. Amphiroa')
    Returns a set of unique names.
    """
    names = set()
    
    # Pattern for 'Genus sp.' or 'Genus spp.' optionally followed by a number
    sp_pattern = r"\b([A-Z][a-z]+\s(?:sp\.|spp\.)(?:\s*\d+)?)\b"
    for match in re.finditer(sp_pattern, text):
        names.add(match.group(1))
    
    # MUCH MORE RESTRICTIVE pattern for binomial names
    # Only match if it looks like a Latin scientific name, not common English
    # Common characteristics of scientific names:
    # - Usually longer than 3 characters for genus
    # - Species name is typically Latin-like
    # - Avoid common English words
    
    # List of common English words that should NOT be treated as scientific names
    common_english_words = {
        'based', 'on', 'according', 'to', 'research', 'and', 'development', 'bureau', 'of',
        'the', 'article', 'mentions', 'document', 'title', 'publication', 'date', 'publisher',
        'ecosystems', 'environment', 'natural', 'resources', 'republic', 'philippines',
        'volume', 'number', 'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december', 'page',
        'international', 'canopy', 'department', 'service', 'bureau', 'office'
    }
    
    # More restrictive binomial pattern - must look like Latin scientific names
    binomial_pattern = r"\b([A-Z][a-z]{2,}\s[a-z]{3,})\b"
    for match in re.finditer(binomial_pattern, text):
        potential_name = match.group(1)
        genus, species = potential_name.split()
        
        # Skip if either genus or species is a common English word
        if genus.lower() in common_english_words or species.lower() in common_english_words:
            continue
            
        # Skip if it contains common English word patterns
        if any(word in potential_name.lower() for word in ['based on', 'according to', 'research and']):
            continue
            
        # Additional check: scientific names often have Latin-like endings
        # This is heuristic but helps avoid false positives
        latin_like_endings = ['us', 'a', 'um', 'is', 'es', 'i', 'ae', 'ensis', 'ica', 'icus']
        if any(species.endswith(ending) for ending in latin_like_endings):
            names.add(potential_name)
        elif len(species) >= 6:  # Longer species names are more likely to be real scientific names
            names.add(potential_name)
    
    # Match single-word genus names in numbered/bulleted lists (this is usually safe)
    list_pattern = r"^(?:\s*[-*\d]+[.)]?\s+)([A-Z][a-z]{2,})\s*$"
    for line in text.splitlines():
        m = re.match(list_pattern, line)
        if m:
            genus = m.group(1)
            if genus.lower() not in common_english_words:
                names.add(genus)
    
    return names

def validate_and_italicize_scientific_names(text: str) -> str:
    """
    Italicize all scientific names in the text using the microservice if available, otherwise
    use ScispaCy CRAFT model (TAXON entities) as primary, and expanded regex as fallback.
    Avoids double-wrapping and reduces false positives.
    Preserves URLs and file paths during processing.
    """
    if not text:
        return text
    
    # CRITICAL: Preserve URLs before processing scientific names
    text_with_placeholders, url_placeholders = _preserve_urls(text)
    
    # Log what we're preserving
    if url_placeholders:
        logger.info(f"Preserved {len(url_placeholders)} URLs/links during scientific name processing")
        for placeholder, original in url_placeholders.items():
            logger.info(f"Preserved: {placeholder} -> {original[:100]}")
    
    # 1. Try microservice if configured
    if SCISPA_MICROSERVICE_URL:
        try:
            logger.info(f"Calling microservice at {SCISPA_MICROSERVICE_URL} for text: {text_with_placeholders[:100]}")
            resp = requests.post(
                SCISPA_MICROSERVICE_URL,
                json={"text": text_with_placeholders},
                timeout=5
            )
            logger.info(f"Microservice response: {resp.text}")
            if resp.status_code == 200:
                result = resp.json().get("result", text_with_placeholders)
                logger.info(f"Microservice returned result: {result[:100]}")
                # Restore URLs after microservice processing
                result = _restore_urls(result, url_placeholders)
                return result
            else:
                logger.warning(f"Microservice returned status {resp.status_code}: {resp.text}")
        except Exception as e:
            logger.warning(f"Could not reach scientific name microservice: {e}")
    
    # 2. Local fallback
    logger.info("Using local fallback for scientific name italicization.")
    scispacy_names = _find_scispacy_names(text_with_placeholders)
    regex_names = _find_regex_names(text_with_placeholders)
    all_names = scispacy_names | regex_names
    
    # Log what scientific names we found
    if all_names:
        logger.info(f"Found {len(all_names)} scientific names: {list(all_names)}")
    else:
        logger.info("No scientific names found")
    
    sorted_names = sorted(all_names, key=len, reverse=True)
    result = text_with_placeholders
    
    for name in sorted_names:
        # Use more precise regex to avoid matching within HTML tags or placeholder text
        # Only match scientific names that are not already in <em> tags and not part of placeholders
        pattern = rf"(?<!<em>)(?<!__)\b{re.escape(name)}\b(?!</em>)(?!__)"
        result = re.sub(pattern, f"<em>{name}</em>", result)
    
    # Restore URLs after processing
    result = _restore_urls(result, url_placeholders)
    
    # Final check: make sure no URLs were corrupted
    if '/download_gated/' in result:
        urls_in_result = re.findall(r'/download_gated/([^"\s>]+)', result)
        for url in urls_in_result:
            if '_' not in url and 'pdf' in url:
                logger.error(f"URL CORRUPTION DETECTED in scientific name processing: /download_gated/{url}")
                logger.error(f"This indicates the URL preservation logic failed!")
            else:
                logger.info(f"✅ URL preserved correctly: /download_gated/{url}")
    
    logger.info(f"Result after fallback italicization: {result[:200]}")
    return result

# Example usage:
if __name__ == "__main__":
    sample = "1. Dictyota sp. 3\n2. Fucus sp. 1\n3. Sargassum sp.\n4. Padina spp.\n5. Amphiroa\nHow does the presence of Sargassum sp. affect water quality?"
    print(validate_and_italicize_scientific_names(sample)) 
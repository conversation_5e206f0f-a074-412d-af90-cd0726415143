/*
 * Comprehensive Theme Fixes for Document Management System
 * This file addresses text visibility and theme consistency issues
 * across all templates and components
 */

/* =============================================================================
   1. CSS VARIABLES FOR CONSISTENT THEMING
   ============================================================================= */

:root {
  /* Enhanced text color variables for better contrast */
  --text-primary-contrast: #1a202c;      /* Very dark for light backgrounds */
  --text-secondary-contrast: #2d3748;    /* Dark gray for light backgrounds */
  --text-muted-contrast: #4a5568;        /* Medium gray for light backgrounds */
  --text-link-contrast: #2563eb;         /* Blue with good contrast */

  /* Form element colors */
  --form-text-light: #1a202c;
  --form-bg-light: #ffffff;
  --form-border-light: #d1d5db;

  /* Table colors */
  --table-text-light: #1a202c;
  --table-header-light: #f9fafb;
  --table-border-light: #e5e7eb;
}

/* Dark mode CSS variables */
.dark-mode,
.dark {
  --text-primary-contrast: #f9fafb;      /* Very light for dark backgrounds */
  --text-secondary-contrast: #e5e7eb;    /* Light gray for dark backgrounds */
  --text-muted-contrast: #d1d5db;        /* Medium light gray for dark backgrounds */
  --text-link-contrast: #60a5fa;         /* Light blue with good contrast */

  /* Form element colors */
  --form-text-light: #f9fafb;
  --form-bg-light: #374151;
  --form-border-light: #6b7280;

  /* Table colors */
  --table-text-light: #f9fafb;
  --table-header-light: #374151;
  --table-border-light: #4b5563;
}

/* =============================================================================
   2. UNIVERSAL TEXT CONTRAST FIXES
   ============================================================================= */

/* Force proper text colors on light backgrounds */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-blue-50,
.bg-green-50,
.bg-yellow-50,
.bg-red-50,
.bg-purple-50,
.bg-indigo-50 {
  color: var(--text-primary-contrast) !important;
}

/* Text color classes with proper contrast */
.text-gray-800,
.text-gray-900 {
  color: var(--text-primary-contrast) !important;
}

.text-gray-700 {
  color: var(--text-secondary-contrast) !important;
}

.text-gray-600,
.text-gray-500 {
  color: var(--text-muted-contrast) !important;
}

/* Link colors */
.text-blue-600,
.text-blue-700 {
  color: var(--text-link-contrast) !important;
}

/* =============================================================================
   3. FORM ELEMENT FIXES
   ============================================================================= */

/* Ensure form elements have proper contrast in all themes */
input:not([type="radio"]):not([type="checkbox"]),
select,
textarea,
.form-control,
.form-select {
  background-color: var(--form-bg-light) !important;
  color: var(--form-text-light) !important;
  border-color: var(--form-border-light) !important;
}

/* Focus states */
input:focus:not([type="radio"]):not([type="checkbox"]),
select:focus,
textarea:focus,
.form-control:focus,
.form-select:focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25) !important;
}

/* =============================================================================
   4. TABLE FIXES
   ============================================================================= */

/* Table text and background fixes */
table,
.table {
  color: var(--table-text-light) !important;
}

table th,
.table th,
table thead th,
.table thead th {
  background-color: var(--table-header-light) !important;
  color: var(--table-text-light) !important;
  border-color: var(--table-border-light) !important;
}

table td,
.table td {
  color: var(--table-text-light) !important;
  border-color: var(--table-border-light) !important;
}

/* =============================================================================
   5. CARD AND CONTAINER FIXES
   ============================================================================= */

/* Ensure cards have proper text contrast */
.card,
.card-body,
.card-header,
.card-footer {
  color: var(--text-primary-contrast) !important;
}

/* Modal fixes */
.modal-content,
.modal-header,
.modal-body,
.modal-footer {
  color: var(--text-primary-contrast) !important;
}

/* =============================================================================
   6. NAVIGATION FIXES
   ============================================================================= */

/* Sidebar visibility fixes */
.sidebar {
  background-color: var(--sidebar-bg) !important;
  border-right: 1px solid var(--border-color) !important;
}

.sidebar .navbar,
.sidebar .navbar.bg-dark {
  background-color: var(--sidebar-bg) !important;
}

/* Override hardcoded Bootstrap classes */
.navbar.bg-dark {
  background-color: var(--navbar-bg) !important;
}

.navbar.navbar-dark .navbar-brand {
  color: var(--navbar-text) !important;
}

.navbar.navbar-dark .nav-link {
  color: var(--navbar-text) !important;
}

.sidebar .nav-link {
  color: var(--sidebar-text) !important;
}

.sidebar .nav-link:hover {
  background-color: var(--sidebar-hover-bg) !important;
  color: var(--primary-500) !important;
}

.sidebar .nav-link.active {
  background-color: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

.sidebar .dropdown-item {
  color: var(--sidebar-text) !important;
}

.sidebar .dropdown-item:hover {
  background-color: var(--sidebar-hover-bg) !important;
  color: var(--primary-500) !important;
}

.sidebar .dropdown-item.active {
  background-color: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

/* Navbar fixes */
.navbar {
  background-color: var(--navbar-bg) !important;
}

.navbar-brand {
  color: var(--navbar-text) !important;
}

/* Dropdown menu fixes */
.dropdown-menu {
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-light) !important;
  color: var(--primary-500) !important;
}

/* =============================================================================
   7. SPECIFIC TEMPLATE FIXES
   ============================================================================= */

/* Admin template specific fixes */
.content-wrapper {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Query Configuration Partial Template Fixes */
.dark-mode .form-label,
.dark .form-label {
  color: var(--text-primary) !important;
}

.dark-mode .form-text,
.dark .form-text {
  color: var(--text-muted) !important;
}

.dark-mode .text-muted,
.dark .text-muted {
  color: var(--text-muted) !important;
}

.dark-mode .small,
.dark .small {
  color: var(--text-secondary) !important;
}

.dark-mode .range-label,
.dark .range-label {
  color: var(--text-muted) !important;
}

.dark-mode .threshold-badge,
.dark .threshold-badge {
  color: #ffffff !important;
}

/* Tab navigation fixes */
.dark-mode .nav-tabs .nav-link,
.dark .nav-tabs .nav-link {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .nav-tabs .nav-link.active,
.dark .nav-tabs .nav-link.active {
  color: var(--text-primary) !important;
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .nav-tabs,
.dark .nav-tabs {
  border-color: var(--border-color) !important;
}

.dark-mode .tab-content,
.dark .tab-content {
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Alert fixes for query config */
.dark-mode .alert,
.dark .alert {
  color: var(--text-primary) !important;
}

.dark-mode .alert-info,
.dark .alert-info {
  background-color: rgba(13, 110, 253, 0.1) !important;
  border-color: rgba(13, 110, 253, 0.2) !important;
  color: #60a5fa !important;
}

.dark-mode .alert-warning,
.dark .alert-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  border-color: rgba(255, 193, 7, 0.2) !important;
  color: #fbbf24 !important;
}

/* Specific fixes for query config elements */
.dark-mode .card-header h5,
.dark .card-header h5,
.dark-mode .card-header h6,
.dark .card-header h6 {
  color: #ffffff !important;
}

.dark-mode .card-header small,
.dark .card-header small {
  color: rgba(255, 255, 255, 0.8) !important;
}

.dark-mode .fw-bold,
.dark .fw-bold {
  color: var(--text-primary) !important;
}

.dark-mode .text-primary,
.dark .text-primary {
  color: #60a5fa !important;
}

.dark-mode .text-success,
.dark .text-success {
  color: #34d399 !important;
}

.dark-mode .text-warning,
.dark .text-warning {
  color: #fbbf24 !important;
}

.dark-mode .text-info,
.dark .text-info {
  color: #60a5fa !important;
}

.dark-mode .text-secondary,
.dark .text-secondary {
  color: var(--text-secondary) !important;
}

.dark-mode .text-danger,
.dark .text-danger {
  color: #f87171 !important;
}

/* Input group fixes */
.dark-mode .input-group-text,
.dark .input-group-text {
  background-color: var(--bg-light) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Button fixes */
.dark-mode .btn-outline-secondary,
.dark .btn-outline-secondary {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .btn-outline-secondary:hover,
.dark .btn-outline-secondary:hover {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

.dark-mode .btn-outline-info,
.dark .btn-outline-info {
  color: #60a5fa !important;
  border-color: #60a5fa !important;
}

.dark-mode .btn-outline-info:hover,
.dark .btn-outline-info:hover {
  background-color: #60a5fa !important;
  color: #ffffff !important;
}

.dark-mode .btn-success,
.dark .btn-success {
  background-color: #059669 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
}

.dark-mode .btn-outline-danger,
.dark .btn-outline-danger {
  color: #f87171 !important;
  border-color: #f87171 !important;
}

.dark-mode .btn-outline-danger:hover,
.dark .btn-outline-danger:hover {
  background-color: #f87171 !important;
  color: #ffffff !important;
}

/* User avatar fixes */
.user-avatar {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

/* Theme toggle button fixes */
.theme-toggle {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

.theme-toggle:hover {
  background-color: var(--primary-500) !important;
  color: #FFFFFF !important;
}

/* Analytics page specific fixes */
.analytics-container .text-gray-800,
.analytics-container .text-gray-700,
.analytics-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Files page specific fixes */
.files-container .text-gray-800,
.files-container .text-gray-700,
.files-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Admin page specific fixes */
.admin-container .text-gray-800,
.admin-container .text-gray-700,
.admin-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Sidebar brand and text fixes */
.sidebar .navbar-brand h3 {
  color: var(--text-primary) !important;
}

.sidebar .text-light {
  color: var(--sidebar-text) !important;
}

/* Page header fixes */
.bg-dark.rounded {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

.bg-dark.rounded .text-light {
  color: var(--text-primary) !important;
}

/* Override all hardcoded Bootstrap bg-dark classes */
.bg-dark {
  background-color: var(--bg-card) !important;
}

/* Override hardcoded text-light classes */
.text-light {
  color: var(--text-primary) !important;
}

/* Dropdown menu overrides for hardcoded classes */
.dropdown-menu.bg-dark {
  background-color: var(--bg-card) !important;
}

.dropdown-menu.bg-dark .dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-menu.bg-dark .text-light {
  color: var(--text-primary) !important;
}

/* Alert overrides */
.alert.bg-dark {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* Footer overrides */
.bg-dark.rounded-top {
  background-color: var(--bg-card) !important;
}

.bg-dark.rounded-top .text-light {
  color: var(--text-primary) !important;
}

/* Form control overrides */
.form-control.bg-dark {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
}

/* =============================================================================
   8. BADGE AND STATUS INDICATOR FIXES
   ============================================================================= */

/* Badge color fixes for both themes */
.badge,
.bg-blue-100.text-blue-800 {
  background-color: var(--primary-100) !important;
  color: var(--primary-800) !important;
}

.bg-green-100.text-green-800 {
  background-color: var(--success-100) !important;
  color: var(--success-800) !important;
}

.bg-yellow-100.text-yellow-800 {
  background-color: var(--warning-100) !important;
  color: var(--warning-800) !important;
}

.bg-red-100.text-red-800 {
  background-color: var(--danger-100) !important;
  color: var(--danger-800) !important;
}

/* =============================================================================
   9. RESPONSIVE AND ACCESSIBILITY FIXES
   ============================================================================= */

/* Ensure minimum contrast ratios for WCAG AA compliance */
@media (prefers-contrast: high) {
  :root {
    --text-primary-contrast: #000000;
    --text-link-contrast: #0000ee;
  }

  .dark-mode,
  .dark {
    --text-primary-contrast: #ffffff;
    --text-link-contrast: #4da6ff;
  }
}

/* Focus indicators for keyboard navigation */
*:focus {
  outline: 2px solid var(--primary-500) !important;
  outline-offset: 2px !important;
}

/* =============================================================================
   10. SLIDER AND INTERACTIVE ELEMENT FIXES
   ============================================================================= */

/* Range slider fixes for dark mode */
.dark-mode .enhanced-range-slider,
.dark .enhanced-range-slider {
  background-color: var(--bg-light) !important;
}

.dark-mode .slider-value-tooltip,
.dark .slider-value-tooltip {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .threshold-indicator,
.dark .threshold-indicator {
  background-color: var(--bg-light) !important;
}

.dark-mode .indicator-bar,
.dark .indicator-bar {
  background-color: var(--bg-secondary) !important;
}

/* Form check fixes */
.dark-mode .form-check-label,
.dark .form-check-label {
  color: var(--text-primary) !important;
}

.dark-mode .form-check-input,
.dark .form-check-input {
  background-color: var(--bg-light) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .form-check-input:checked,
.dark .form-check-input:checked {
  background-color: var(--primary-500) !important;
  border-color: var(--primary-500) !important;
}

/* Select dropdown fixes */
.dark-mode .form-select,
.dark .form-select {
  background-color: var(--form-bg-light) !important;
  color: var(--form-text-light) !important;
  border-color: var(--form-border-light) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f9fafb' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

.dark-mode .form-select:focus,
.dark .form-select:focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25) !important;
}

/* Badge fixes for specific contexts */
.dark-mode .badge.bg-primary,
.dark .badge.bg-primary {
  background-color: var(--primary-500) !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-success,
.dark .badge.bg-success {
  background-color: #059669 !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-warning,
.dark .badge.bg-warning {
  background-color: #d97706 !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-danger,
.dark .badge.bg-danger {
  background-color: #dc2626 !important;
  color: #ffffff !important;
}

/* Character counter and helper text fixes */
.dark-mode .character-counter,
.dark .character-counter {
  background-color: rgba(31, 41, 55, 0.9) !important;
  color: var(--text-muted) !important;
  border-color: var(--border-color) !important;
}

/* Tooltip fixes */
.dark-mode .tooltip .tooltip-inner,
.dark .tooltip .tooltip-inner {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* =============================================================================
   11. PRINT STYLES
   ============================================================================= */

@media print {
  * {
    color: #000000 !important;
    background-color: #ffffff !important;
  }
}

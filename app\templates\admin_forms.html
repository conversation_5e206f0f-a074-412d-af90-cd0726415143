{% extends "admin_base.html" %}

{% block title %}Manage Forms{% endblock %}

{% block content %}
<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h6 class="mb-0">Forms</h6>
                    <a href="{{ url_for('create_form') }}" class="btn btn-primary">Create New Form</a>
                </div>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Name</th>
                                <th scope="col">Description</th>
                                <th scope="col">Active</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for form in forms %}
                            <tr>
                                <th scope="row">{{ loop.index }}</th>
                                <td>{{ form.name }}</td>
                                <td>{{ form.description }}</td>
                                <td>
                                    {% if form.is_active %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-danger">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('view_form', form_id=form.id) }}" class="btn btn-sm btn-secondary me-1">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ url_for('edit_form', form_id=form.id) }}" class="btn btn-sm btn-info me-1">Edit</a>
                                    <form action="{{ url_for('delete_form', form_id=form.id) }}" method="POST" style="display:inline;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this form?');">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No forms found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
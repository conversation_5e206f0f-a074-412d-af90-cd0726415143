{% extends "admin_base.html" %}

{% block title %}Edit User{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h4 fw-bold text-dark">Edit User</h1>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="link-primary">&larr; Back to User Details</a>
                    <a href="{{ url_for('user.admin_users') }}" class="link-primary">All Users</a>
                </div>
            </div>
            <div class="row g-4">
                <!-- User Profile Sidebar -->
                <div class="col-md-4">
                    <div class="bg-light p-4 rounded border">
                        <div class="d-flex flex-column align-items-center text-center">
                            {% if user.profile_picture %}
                                <img src="{{ url_for('static', filename=user.profile_picture) }}" alt="Profile Picture" class="rounded-circle border border-white shadow mb-3" style="width: 8rem; height: 8rem; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold border border-white shadow mb-3" style="width: 8rem; height: 8rem; font-size: 2.5rem;">
                                    {{ user.username[0].upper() }}
                                </div>
                            {% endif %}
                            <h2 class="mt-3 h5 fw-semibold">{{ user.full_name or user.username }}</h2>
                            <p class="text-muted">{{ user.email }}</p>
                            <div class="mt-2">
                                <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.role == 'admin' %}purple{% elif user.role == 'editor' %}primary{% else %}success{% endif %}">
                                    {{ user.role.capitalize() }}
                                </span>
                                <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.account_status == 'active' %}success{% elif user.account_status == 'pending' %}warning{% elif user.account_status == 'locked' %}danger{% else %}secondary{% endif %}">
                                    {{ user.account_status.capitalize() }}
                                </span>
                            </div>
                        </div>
                        <div class="mt-4 border-top pt-3">
                            <h3 class="h6 fw-semibold mb-2">Account Information</h3>
                            <ul class="list-unstyled mb-0">
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Username:</span>
                                    <span class="fw-medium">{{ user.username }}</span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">User ID:</span>
                                    <span class="fw-medium">{{ user.user_id }}</span>
                                </li>
                                <li class="d-flex justify-content-between">
                                    <span class="text-secondary">Created:</span>
                                    <span class="fw-medium">{{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- Edit User Form -->
                <div class="col-md-8">
                    <div class="bg-white p-4 rounded border">
                        <h3 class="h6 fw-semibold mb-3">Edit User Information</h3>
                        <form method="POST" action="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}">
                            {{ form.csrf_token }}
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="username" class="form-label">Username</label>
                                    {{ form.username(class="form-control bg-light", readonly=True) }}
                                    {% if form.username.errors %}
                                        <div class="form-text text-danger">{{ form.username.errors[0] }}</div>
                                    {% endif %}
                                    <div class="form-text text-muted">Username cannot be changed</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email</label>
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="form-text text-danger">{{ form.email.errors[0] }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                {{ form.full_name(class="form-control") }}
                                {% if form.full_name.errors %}
                                    <div class="form-text text-danger">{{ form.full_name.errors[0] }}</div>
                                {% endif %}
                            </div>
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="role" class="form-label">Role</label>
                                    {{ form.role(class="form-select", onchange="showRoleGroupInfo(this.value)") }}
                                    {% if form.role.errors %}
                                        <div class="form-text text-danger">{{ form.role.errors[0] }}</div>
                                    {% endif %}
                                    <div id="roleGroupInfo" class="form-text text-muted d-none"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="account_status" class="form-label">Account Status</label>
                                    {{ form.account_status(class="form-select") }}
                                    {% if form.account_status.errors %}
                                        <div class="form-text text-danger">{{ form.account_status.errors[0] }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-4 border-top pt-3">
                                <h4 class="h6 fw-semibold mb-2">Change Password (Optional)</h4>
                                <div class="form-text text-muted mb-3">Leave blank to keep current password</div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="password" class="form-label">New Password</label>
                                        {{ form.password(class="form-control") }}
                                        {% if form.password.errors %}
                                            <div class="form-text text-danger">{{ form.password.errors[0] }}</div>
                                        {% endif %}
                                        <div class="form-text text-muted">Minimum 8 characters with mixed case, numbers, and symbols</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="confirm_password" class="form-label">Confirm Password</label>
                                        {{ form.confirm_password(class="form-control") }}
                                        {% if form.confirm_password.errors %}
                                            <div class="form-text text-danger">{{ form.confirm_password.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-4">
                                <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Function to show information about group assignment based on role
        function showRoleGroupInfo(role) {
            const infoElement = document.getElementById('roleGroupInfo');

            if (role === 'editor') {
                infoElement.textContent = 'User will be automatically assigned to the Editor Group with all Editor permissions.';
                infoElement.classList.remove('d-none');
                infoElement.classList.add('text-primary');
            } else if (role === 'viewer') {
                infoElement.textContent = 'User will be automatically assigned to the Viewer Group with all Viewer permissions.';
                infoElement.classList.remove('d-none');
                infoElement.classList.add('text-success');
            } else {
                infoElement.classList.add('d-none');
            }
        }

        // Show info on page load based on current selection
        document.addEventListener('DOMContentLoaded', function() {
            const roleSelect = document.querySelector('select[name="role"]');
            if (roleSelect) {
                showRoleGroupInfo(roleSelect.value);
            }
        });
    </script>
{% endblock %}

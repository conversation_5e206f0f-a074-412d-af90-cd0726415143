{% extends "admin_base.html" %}

{% block title %}User Management{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h4 fw-bold text-dark">User Management</h1>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('user.admin_activity_logs') }}" class="link-primary">Activity Logs</a>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <form action="{{ url_for('user.admin_users') }}" method="GET" class="d-flex">
                    <input type="text" name="search" value="{{ search }}" placeholder="Search users..." class="form-control me-2" style="max-width: 220px;">
                    <button type="submit" class="btn btn-primary">Search</button>
                </form>
                <a href="{{ url_for('user.admin_new_user') }}" class="btn btn-success">Add New User</a>
            </div>
            <div class="mb-3">
                <div class="d-flex align-items-center gap-2">
                    <select id="bulkAction" class="form-select" style="max-width: 180px;">
                        <option value="">Bulk Actions</option>
                        <option value="status:active">Set Active</option>
                        <option value="status:pending">Set Pending</option>
                        <option value="status:locked">Set Locked</option>
                        <option value="status:disabled">Set Disabled</option>
                        <option value="role:admin">Set as Admin</option>
                        <option value="role:editor">Set as Editor</option>
                        <option value="role:viewer">Set as Viewer</option>
                        <option value="delete">Delete Selected</option>
                    </select>
                    <button id="applyBulkAction" class="btn btn-secondary">Apply</button>
                </div>
            </div>
            <div class="bg-light rounded border overflow-auto">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><input type="checkbox" id="selectAll" class="form-check-input"></th>
                            <th><a href="{{ url_for('user.admin_users', search=search, sort_by='username', sort_order='asc' if sort_by == 'username' and sort_order == 'desc' else 'desc') }}" class="text-decoration-none">Username</a></th>
                            <th><a href="{{ url_for('user.admin_users', search=search, sort_by='email', sort_order='asc' if sort_by == 'email' and sort_order == 'desc' else 'desc') }}" class="text-decoration-none">Email</a></th>
                            <th><a href="{{ url_for('user.admin_users', search=search, sort_by='role', sort_order='asc' if sort_by == 'role' and sort_order == 'desc' else 'desc') }}" class="text-decoration-none">Role</a></th>
                            <th><a href="{{ url_for('user.admin_users', search=search, sort_by='account_status', sort_order='asc' if sort_by == 'account_status' and sort_order == 'desc' else 'desc') }}" class="text-decoration-none">Status</a></th>
                            <th><a href="{{ url_for('user.admin_users', search=search, sort_by='created_at', sort_order='asc' if sort_by == 'created_at' and sort_order == 'desc' else 'desc') }}" class="text-decoration-none">Created</a></th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr>
                                <td><input type="checkbox" class="user-checkbox form-check-input" data-user-id="{{ user.user_id }}"></td>
                                <td class="fw-medium">{{ user.username }}</td>
                                <td class="text-muted">{{ user.email }}</td>
                                <td>
                                    <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.role == 'admin' %}purple{% elif user.role == 'editor' %}primary{% else %}success{% endif %}">
                                        {{ user.role.capitalize() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.account_status == 'active' %}success{% elif user.account_status == 'pending' %}warning{% elif user.account_status == 'locked' %}danger{% else %}secondary{% endif %}">
                                        {{ user.account_status.capitalize() }}
                                    </span>
                                </td>
                                <td class="text-muted">{{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}</td>
                                <td class="text-end">
                                    <a href="{{ url_for('user.admin_user_details', user_id=user.user_id) }}" class="link-primary me-2">View</a>
                                    <a href="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}" class="link-info me-2">Edit</a>
                                    {% if user.user_id != current_user.user_id %}
                                        <button onclick="deleteUser({{ user.user_id }}, '{{ user.username }}')" class="btn btn-link text-danger p-0">Delete</button>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <!-- Pagination -->
            {% if pages > 1 %}
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="small text-muted">
                        Showing <span class="fw-medium">{{ (page - 1) * per_page + 1 }}</span> to <span class="fw-medium">{{ min(page * per_page, total) }}</span> of <span class="fw-medium">{{ total }}</span> users
                    </div>
                    <div class="d-flex gap-2">
                        {% if page > 1 %}
                            <a href="{{ url_for('user.admin_users', page=page-1, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="btn btn-outline-secondary btn-sm">Previous</a>
                        {% endif %}
                        {% for p in range(max(1, page-2), min(pages+1, page+3)) %}
                            <a href="{{ url_for('user.admin_users', page=p, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="btn btn-sm {% if p == page %}btn-primary text-white{% else %}btn-outline-secondary{% endif %}">{{ p }}</a>
                        {% endfor %}
                        {% if page < pages %}
                            <a href="{{ url_for('user.admin_users', page=page+1, search=search, sort_by=sort_by, sort_order=sort_order) }}" class="btn btn-outline-secondary btn-sm">Next</a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Show toast message
        function showToast(message, type) {
            Toastify({
                text: message,
                duration: 3000,
                close: true,
                gravity: "top",
                position: "right",
                backgroundColor: type === "error" ? "#ff4444" : "#00C851",
            }).showToast();
        }

        // Delete user
        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                fetch(`/admin/users/${userId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`User "${username}" deleted successfully.`, 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showToast(`Error: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showToast(`Error: ${error}`, 'error');
                });
            }
        }

        // Select all checkboxes
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Apply bulk action
        document.getElementById('applyBulkAction').addEventListener('click', function() {
            const action = document.getElementById('bulkAction').value;
            if (!action) {
                showToast('Please select an action.', 'error');
                return;
            }

            const checkboxes = document.querySelectorAll('.user-checkbox:checked');
            if (checkboxes.length === 0) {
                showToast('Please select at least one user.', 'error');
                return;
            }

            const userIds = Array.from(checkboxes).map(checkbox => parseInt(checkbox.dataset.userId));

            // Parse action and value
            const [actionType, actionValue] = action.split(':');

            // Confirm deletion
            if (actionType === 'delete') {
                if (!confirm(`Are you sure you want to delete ${userIds.length} selected users?`)) {
                    return;
                }
            }

            // Send request
            fetch('/admin/users/bulk_action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    user_ids: userIds,
                    action: actionType,
                    value: actionValue
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Create a more detailed message for role changes that include group assignment
                    let message = `Bulk action applied successfully to ${userIds.length} users.`;

                    // If this was a role change to editor or viewer, add info about group assignment
                    if (actionType === 'role' && (actionValue === 'editor' || actionValue === 'viewer')) {
                        const groupName = actionValue === 'editor' ? 'Editor' : 'Viewer';
                        message = `${userIds.length} users set as ${actionValue.charAt(0).toUpperCase() + actionValue.slice(1)} and assigned to ${groupName} Group with all group permissions.`;
                    }

                    showToast(message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'error');
            });
        });
    </script>
{% endblock %}

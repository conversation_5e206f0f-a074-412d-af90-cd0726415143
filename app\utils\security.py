"""
Security utilities for the user management system.

This module provides utilities for password hashing, token generation,
and other security-related functions.
"""

import os
import re
import uuid
import datetime
import logging
import bcrypt
from typing import Tuple, Optional, Dict, Any
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadSignature
from flask import request, session
from flask_wtf import CSR<PERSON>rotect
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

from app.utils import db_connection as db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Secret key for token generation
SECRET_KEY = os.getenv("FLASK_SECRET_KEY", "1qazxsw23edcvfr4")

# Password reset token expiration (in seconds)
PASSWORD_RESET_EXPIRY = int(os.getenv("PASSWORD_RESET_EXPIRY", "3600"))  # 1 hour

# Email verification token expiration (in seconds)
EMAIL_VERIFY_EXPIRY = int(os.getenv("EMAIL_VERIFY_EXPIRY", "86400"))  # 24 hours

# Account lockout settings
MAX_LOGIN_ATTEMPTS = int(os.getenv("MAX_LOGIN_ATTEMPTS", "5"))
LOCKOUT_WINDOW = int(os.getenv("LOCKOUT_WINDOW", "1800"))  # 30 minutes

# Password expiry (in days)
PASSWORD_EXPIRY_DAYS = int(os.getenv("PASSWORD_EXPIRY_DAYS", "90"))

# Create serializer for tokens
serializer = URLSafeTimedSerializer(SECRET_KEY)

csrf = CSRFProtect()


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt.

    Args:
        password: The plain text password to hash

    Returns:
        The hashed password as a string
    """
    # Use a higher work factor in production
    rounds = 12 if os.getenv("FLASK_ENV") == "production" else 10
    password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(rounds=rounds))
    return password_hash.decode('utf-8')


def verify_password(password: str, password_hash: str) -> bool:
    """
    Verify a password against a hash.

    Args:
        password: The plain text password to verify
        password_hash: The hashed password to compare against

    Returns:
        True if the password matches the hash, False otherwise
    """
    try:
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    except Exception as e:
        logger.error(f"Password verification error: {str(e)}")
        return False


def validate_password_complexity(password: str) -> Tuple[bool, str]:
    """
    Validate password complexity requirements.

    Args:
        password: The password to validate

    Returns:
        A tuple of (is_valid, error_message)
    """
    # At least 8 characters
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"

    # Check for uppercase, lowercase, number, and special character
    has_uppercase = any(char.isupper() for char in password)
    has_lowercase = any(char.islower() for char in password)
    has_number = any(char.isdigit() for char in password)
    has_special = any(not char.isalnum() for char in password)

    if not (has_uppercase and has_lowercase and has_number and has_special):
        return False, "Password must include uppercase, lowercase, number, and special character"

    return True, ""


def generate_token(data: Dict[str, Any], expiry: int = 3600) -> str:
    """
    Generate a secure token for the given data.

    Args:
        data: The data to encode in the token
        expiry: Token expiration time in seconds

    Returns:
        A secure token string
    """
    return serializer.dumps(data)


def verify_token(token: str, expiry: int = 3600) -> Optional[Dict[str, Any]]:
    """
    Verify a token and return the data.

    Args:
        token: The token to verify
        expiry: Token expiration time in seconds

    Returns:
        The data encoded in the token, or None if the token is invalid
    """
    try:
        data = serializer.loads(token, max_age=expiry)
        return data
    except (SignatureExpired, BadSignature):
        return None


def generate_reset_token(user_id: int) -> str:
    """
    Generate a password reset token for a user.

    Args:
        user_id: The ID of the user

    Returns:
        A secure reset token
    """
    # Generate a unique token
    token = str(uuid.uuid4())
    expiry = (datetime.datetime.now() + datetime.timedelta(seconds=PASSWORD_RESET_EXPIRY)).isoformat()

    # Store the token in the database with a hash
    token_hash = hash_password(token)

    db.execute_update(
        "UPDATE users SET reset_token = ?, reset_token_expiry = ? WHERE user_id = ?",
        (token_hash, expiry, user_id)
    )

    # Return the original token for the URL
    return token


def verify_reset_token(token: str, user_id: int) -> bool:
    """
    Verify a password reset token.

    Args:
        token: The token to verify
        user_id: The ID of the user

    Returns:
        True if the token is valid, False otherwise
    """
    # Get the stored token hash and expiry
    result = db.execute_query(
        "SELECT reset_token, reset_token_expiry FROM users WHERE user_id = ?",
        (user_id,)
    )

    if not result:
        return False

    token_hash = result[0]['reset_token']
    expiry_str = result[0]['reset_token_expiry']

    if not token_hash or not expiry_str:
        return False

    # Check if the token has expired
    try:
        expiry = datetime.datetime.fromisoformat(expiry_str)
        if datetime.datetime.now() > expiry:
            return False
    except (ValueError, TypeError):
        return False

    # Verify the token
    return verify_password(token, token_hash)


def generate_verification_token(user_id: int) -> str:
    """
    Generate an email verification token for a user.

    Args:
        user_id: The ID of the user

    Returns:
        A secure verification token
    """
    # Generate a unique token
    token = str(uuid.uuid4())
    expiry = (datetime.datetime.now() + datetime.timedelta(seconds=EMAIL_VERIFY_EXPIRY)).isoformat()

    # Store the token in the database
    db.execute_update(
        "UPDATE users SET verification_token = ?, verification_token_expiry = ? WHERE user_id = ?",
        (token, expiry, user_id)
    )

    return token


def init_csrf(app) -> CSRFProtect:
    """
    Initialize CSRF protection for the application.

    Args:
        app: The Flask application

    Returns:
        The CSRFProtect instance
    """
    csrf.init_app(app)
    return csrf


def init_limiter(app) -> Limiter:
    """
    Initialize rate limiting for the application.

    Args:
        app: The Flask application

    Returns:
        The Limiter instance
    """
    limiter = Limiter(
        app=app,
        key_func=get_remote_address,
        default_limits=["1000 per day", "300 per hour", "100 per minute"]
    )
    return limiter

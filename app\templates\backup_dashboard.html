{% extends "admin_base.html" %}

{% block title %}Backup Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">
                    <i class="fas fa-database me-2"></i>Backup Management
                </h4>
                <div class="page-title-right">
                    <button class="btn btn-primary" onclick="createBackup()">
                        <i class="fas fa-plus me-2"></i>Create Backup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Total Backups</p>
                            <h4 class="mb-0">{{ total_count }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-primary align-self-center overflow-hidden">
                                <span class="avatar-title">
                                    <i class="fas fa-database font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Total Size</p>
                            <h4 class="mb-0">{{ "%.2f"|format(total_size / (1024*1024)) }} MB</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-success align-self-center overflow-hidden">
                                <span class="avatar-title">
                                    <i class="fas fa-hdd font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Retention Days</p>
                            <h4 class="mb-0">{{ retention_days }}</h4>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-warning align-self-center overflow-hidden">
                                <span class="avatar-title">
                                    <i class="fas fa-clock font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card mini-stats-wid">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-grow-1">
                            <p class="text-muted fw-medium">Backup Directory</p>
                            <h6 class="mb-0 text-truncate">{{ backup_dir }}</h6>
                        </div>
                        <div class="flex-shrink-0 align-self-center">
                            <div class="mini-stat-icon avatar-sm rounded-circle bg-info align-self-center overflow-hidden">
                                <span class="avatar-title">
                                    <i class="fas fa-folder font-size-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Backup Actions
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <button class="btn btn-primary btn-lg" onclick="createBackup()">
                                    <i class="fas fa-plus me-2"></i>Create New Backup
                                </button>
                                <small class="text-muted mt-2">
                                    Creates a complete backup of databases, files, and configuration
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <form method="POST" action="{{ url_for('backup.cleanup_backups_route') }}">
                                <div class="row">
                                    <div class="col-8">
                                        <label for="retention_days" class="form-label">Retention Days</label>
                                        <input type="number" class="form-control" id="retention_days" name="retention_days" 
                                               value="{{ retention_days }}" min="1" max="365">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-trash me-2"></i>Cleanup
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    Removes backups older than specified days
                                </small>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Available Backups
                    </h4>
                </div>
                <div class="card-body">
                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Date</th>
                                        <th>Age</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-file-archive me-2 text-primary"></i>
                                            {{ backup.filename }}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ "%.2f"|format(backup.size / (1024*1024)) }} MB
                                            </span>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ backup.date.split('T')[0] }}
                                        </td>
                                        <td>
                                            {% if backup.age_days == 0 %}
                                                <span class="badge bg-success">Today</span>
                                            {% elif backup.age_days == 1 %}
                                                <span class="badge bg-success">Yesterday</span>
                                            {% elif backup.age_days < 7 %}
                                                <span class="badge bg-info">{{ backup.age_days }} days ago</span>
                                            {% elif backup.age_days < 30 %}
                                                <span class="badge bg-warning">{{ backup.age_days }} days ago</span>
                                            {% else %}
                                                <span class="badge bg-danger">{{ backup.age_days }} days ago</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No backups found</h5>
                            <p class="text-muted">Create your first backup to get started</p>
                            <button class="btn btn-primary" onclick="createBackup()">
                                <i class="fas fa-plus me-2"></i>Create First Backup
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Creating Backup...</h5>
                <p class="text-muted">This may take several minutes. Please wait.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function createBackup() {
    // Show loading modal
    $('#loadingModal').modal('show');
    
    // Make API call to create backup
    fetch('{{ url_for("backup.api_create_backup") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        $('#loadingModal').modal('hide');
        
        if (data.success) {
            // Show success message and reload page
            alert('Backup created successfully!');
            location.reload();
        } else {
            // Show error message
            alert('Failed to create backup: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        $('#loadingModal').modal('hide');
        console.error('Error:', error);
        alert('An error occurred while creating the backup');
    });
}
</script>
{% endblock %} 
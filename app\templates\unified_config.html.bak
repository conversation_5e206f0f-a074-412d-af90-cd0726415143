{% extends "admin_base.html" %}

{% block title %}Model Settings{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>

    <!-- utilities.js is already included in admin_base.html -->

    <!-- Include dark-mode.css for consistent dark mode styling -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* Tab Navigation Styles */
        .tab-active {
            @apply bg-blue-700 text-white border-b-2 border-blue-700 font-medium;
            transform: translateY(1px) scale(1.02);
            transition: all 0.3s ease;
        }
        .tab-inactive {
            @apply bg-gray-200 text-gray-600 border-b border-transparent opacity-85;
            transition: all 0.3s ease;
        }
        .tab-inactive:hover {
            @apply bg-gray-300 text-gray-700 opacity-100;
        }

        /* Dark mode tab styles */
        .dark .tab-inactive {
            @apply bg-gray-700 text-gray-300;
        }
        .dark .tab-inactive:hover {
            @apply bg-gray-600 text-gray-200;
        }

        /* Additional text contrast fixes specific to model settings page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Fix for model cards */
        .bg-white .text-gray-900 { color: #1a202c !important; }

        /* Fix for section headers */
        .config-section-header { color: #1a202c !important; }

        /* Fix for section descriptions */
        .config-section-description { color: #4a5568 !important; }

        /* Fix for labels */
        label .text-gray-700 { color: #2d3748 !important; }

        /* Dark mode styles for unified config page */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #1f2937 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }
        .dark .text-blue-800 { color: #93c5fd !important; }
        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .text-green-600 { color: #34d399 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }
        .dark .border-blue-200 { border-color: #3b82f6 !important; }
        .dark .border-blue-500 { border-color: #60a5fa !important; }
        .dark .border-green-200 { border-color: #34d399 !important; }

        /* Model card styles in dark mode */
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Ring colors in dark mode */
        .dark .ring-blue-500 { --tw-ring-color: #60a5fa !important; }

        /* Config section overrides for dark mode */
        .dark .config-section-header { color: #f3f4f6 !important; }
        .dark .config-section-description { color: #d1d5db !important; }

        /* Tab Content Styles */
        .tab-content {
            @apply border-t-4 border-blue-700 rounded-b-lg;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .tab-content.hidden {
            opacity: 0;
            transform: translateY(10px);
            display: none;
        }
        .tab-content:not(.hidden) {
            opacity: 1;
            transform: translateY(0);
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Other UI Elements */
        .dependency-indicator {
            @apply text-xs text-blue-600 font-medium mt-1;
        }
        .config-section {
            @apply bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6;
        }
        .config-section-header {
            @apply text-xl font-semibold text-gray-800 mb-4;
        }
        .config-section-description {
            @apply text-sm text-gray-600 mb-4;
        }
        .config-subsection {
            @apply mt-6 border-t border-gray-200 pt-4;
        }
        .config-subsection-header {
            @apply text-lg font-medium text-gray-800 mb-3;
        }

        /* Accessibility Styles */
        .tab-active:focus, .tab-inactive:focus {
            @apply outline-none ring-2 ring-blue-500 ring-offset-2;
        }

        /* Active Tab Indicator */
        .tab-indicator {
            @apply ml-1 inline-flex items-center justify-center;
            width: 16px;
            height: 16px;
        }
        .tab-indicator svg {
            @apply text-white;
            width: 12px;
            height: 12px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Model Settings</h1>
            <div class="flex space-x-4 items-center">
                <a href="{{ url_for('admin_dashboard') }}" class="text-blue-600 dark:text-blue-400 hover:underline">Admin Dashboard</a>
                <button id="theme-toggle" class="ml-2 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <span id="theme-icon" class="text-xl">🌙</span>
                </button>
            </div>
        </div>

        <div id="statusMessage" class="mb-6 hidden"></div>

            <!-- Configuration Summary -->
            <div class="mb-6 bg-blue-50 dark:bg-blue-900 p-4 rounded-lg border border-blue-200 dark:border-blue-600">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-lg font-semibold text-blue-800 dark:text-blue-200">Configuration Summary</h2>
                    <button id="toggleSummary" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">Show Details</button>
                </div>
                <div id="summaryContent" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div class="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                            <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-2">AI Models</h3>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>LLM:</strong> <span id="summary-llm">{{ selected_model }}</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Embedding:</strong> <span id="summary-embedding">{{ selected_embedding }}</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Vision:</strong> <span id="summary-vision">{{ selected_vision }}</span></p>
                        </div>
                        <div class="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                            <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Query Settings</h3>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Anti-Hallucination:</strong> <span id="summary-hallucination">{{ anti_hallucination_modes.default_mode }}</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Templates:</strong> <span id="summary-templates">5 configured</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Phrases:</strong> <span id="summary-phrases">{{ insufficient_info_phrases|length }} configured</span></p>
                        </div>
                        <div class="bg-white dark:bg-gray-700 p-3 rounded shadow-sm">
                            <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Embedding Settings</h3>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Chunk Size:</strong> <span id="summary-chunk-size">{{ embedding_params.chunk_size }}</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Vision Analysis:</strong> <span id="summary-vision-enabled">{{ 'Enabled' if embedding_params.use_vision_model else 'Disabled' }}</span></p>
                            <p class="text-sm text-gray-700 dark:text-gray-300"><strong>Batch Size:</strong> <span id="summary-batch-size">{{ embedding_params.batch_size }}</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Tabs -->
            <div class="mb-6">
                <ul class="flex flex-wrap border-b border-gray-200 dark:border-gray-600" id="mainTabs" role="tablist">
                    <li class="mr-2" role="presentation">
                        <button class="tab-active inline-block p-4 rounded-t-lg"
                                id="models-tab" data-target="models-content" type="button" role="tab"
                                aria-selected="true" aria-controls="models-content">
                            AI Models
                            <span class="tab-indicator">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="tab-inactive inline-block p-4 rounded-t-lg"
                                id="query-tab" data-target="query-content" type="button" role="tab"
                                aria-selected="false" aria-controls="query-content">
                            Query Configuration
                            <span class="tab-indicator hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                    <li class="mr-2" role="presentation">
                        <button class="tab-inactive inline-block p-4 rounded-t-lg"
                                id="embedding-tab" data-target="embedding-content" type="button" role="tab"
                                aria-selected="false" aria-controls="embedding-content">
                            Embedding Configuration
                            <span class="tab-indicator hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>
                    </li>
                </ul>
            </div>

            <form id="unifiedConfigForm" class="space-y-8">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <!-- AI Models Content -->
                <div id="models-content" class="tab-content">
                    <!-- Tab-specific save button -->
                    <div class="flex justify-end mb-4">
                        <button type="button" id="saveModelsBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            Save Models Settings
                        </button>
                    </div>

                    <!-- LLM Models Section -->
                    <div class="config-section">
                        <h2 class="config-section-header">LLM Models</h2>
                        <p class="config-section-description">Select the language model used for generating responses to user queries.</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for model in models %}
                                <div class="relative">
                                    <input type="radio" id="llm_{{ loop.index }}" name="llm_model" value="{{ model.name }}"
                                           class="hidden peer" {% if model.name == selected_model %}checked{% endif %}>
                                    <label for="llm_{{ loop.index }}"
                                           class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                                                  {% if model.name == selected_model %}border-blue-500 ring-2 ring-blue-500{% endif %}
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                                                  {% if model.name == default_llm %}border-green-200 dark:border-green-600{% endif %}">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">{{ model.name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Size: {{ model.size | filesizeformat }}
                                        </div>
                                        {% if model.name == default_llm %}
                                        <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Embedding Models Section -->
                    <div class="config-section">
                        <h2 class="config-section-header">Embedding Models</h2>
                        <p class="config-section-description">Select the model used for generating vector embeddings of documents.</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for embedding in embeddings %}
                                <div class="relative">
                                    <input type="radio" id="embed_{{ loop.index }}" name="embedding_model" value="{{ embedding.name }}"
                                           class="hidden peer" {% if embedding.name == selected_embedding %}checked{% endif %}>
                                    <label for="embed_{{ loop.index }}"
                                           class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                                                  {% if embedding.name == selected_embedding %}border-blue-500 ring-2 ring-blue-500{% endif %}
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                                                  {% if embedding.name == default_embedding %}border-green-200 dark:border-green-600{% endif %}">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">{{ embedding.name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Size: {{ embedding.size | filesizeformat }}
                                        </div>
                                        {% if embedding.name == default_embedding %}
                                        <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Vision Models Section -->
                    <div class="config-section">
                        <h2 class="config-section-header">Vision Models</h2>
                        <p class="config-section-description">Select the model used for analyzing images in documents.</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for vision in vision_models %}
                                <div class="relative">
                                    <input type="radio" id="vision_{{ loop.index }}" name="vision_model" value="{{ vision.name }}"
                                           class="hidden peer" {% if vision.name == selected_vision %}checked{% endif %}>
                                    <label for="vision_{{ loop.index }}"
                                           class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                                                  {% if vision.name == selected_vision %}border-blue-500 ring-2 ring-blue-500{% endif %}
                                                  peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                                                  hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                                                  {% if vision.name == default_vision %}border-green-200 dark:border-green-600{% endif %}">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">{{ vision.name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Size: {{ vision.size | filesizeformat }}
                                        </div>
                                        {% if vision.name == default_vision %}
                                        <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                                            Default Model
                                        </div>
                                        {% endif %}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Vision Model Selection Section -->
                    <div class="config-section">
                        <h2 class="config-section-header">Vision Model</h2>
                        <p class="config-section-description">Select the vision model used for analyzing images in chat responses. Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported.</p>

                        <div class="flex flex-col space-y-4">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="use_vision" class="form-checkbox h-5 w-5 text-blue-600"
                                       {% if use_vision %}checked{% endif %}>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Enable Vision Model for Chat</span>
                            </label>
                            <div class="dependency-indicator">
                                <span class="text-blue-600 dark:text-blue-400">↑</span> Controls whether images are analyzed during chat
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Query Configuration Content -->
                <div id="query-content" class="tab-content hidden">
                    <!-- Tab-specific save button -->
                    <div class="flex justify-end mb-4">
                        <button type="button" id="saveQueryBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            Save Query Settings
                        </button>
                    </div>

                    <!-- Content will be loaded via JavaScript -->
                    <div id="query-config-container"></div>
                </div>

                <!-- Embedding Configuration Content -->
                <div id="embedding-content" class="tab-content hidden">
                    <!-- Tab-specific save button -->
                    <div class="flex justify-end mb-4">
                        <button type="button" id="saveEmbeddingBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            Save Embedding Settings
                        </button>
                    </div>

                    <!-- Content will be loaded via JavaScript -->
                    <div id="embedding-config-container"></div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <button type="button" id="validateConfigBtn"
                        class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        Validate Configuration
                    </button>
                    <button type="submit"
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Save All Settings
                    </button>
                </div>
            </form>
        </div>
{% endblock %}

{% block scripts %}
    <script>
        // Initialize theme and functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();

            // Add theme toggle button event listener
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');
                    DMSUtils.toggleDarkMode(!isDarkMode);
                });
            }

            // Set initial theme icon based on current theme
            const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                              document.documentElement.classList.contains('dark');
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = isDarkMode ? '☀️' : '🌙';
            }
        });
    </script>
    <script src="/static/unified_config.js"></script>
{% endblock %}

{% extends "admin_base.html" %}

{% block title %}User Details{% endblock %}

{% block head %}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h4 fw-bold text-dark">User Details</h1>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('user.admin_users') }}" class="link-primary">&larr; Back to User Management</a>
                </div>
            </div>
            <div class="row g-4">
                <!-- User Profile Sidebar -->
                <div class="col-md-4">
                    <div class="bg-light p-4 rounded border">
                        <div class="d-flex flex-column align-items-center text-center">
                            {% if user.profile_picture %}
                                <img src="{{ url_for('static', filename=user.profile_picture) }}" alt="Profile Picture" class="rounded-circle border border-white shadow mb-3" style="width: 8rem; height: 8rem; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold border border-white shadow mb-3" style="width: 8rem; height: 8rem; font-size: 2.5rem;">
                                    {{ user.username[0].upper() }}
                                </div>
                            {% endif %}
                            <h2 class="mt-3 h5 fw-semibold">{{ user.full_name or user.username }}</h2>
                            <p class="text-muted">{{ user.email }}</p>
                            <div class="mt-2">
                                <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.role == 'admin' %}purple{% elif user.role == 'editor' %}primary{% else %}success{% endif %}">
                                    {{ user.role.capitalize() }}
                                </span>
                                <span class="badge rounded-pill px-3 py-1 text-bg-{% if user.account_status == 'active' %}success{% elif user.account_status == 'pending' %}warning{% elif user.account_status == 'locked' %}danger{% else %}secondary{% endif %}">
                                    {{ user.account_status.capitalize() }}
                                </span>
                            </div>
                        </div>
                        <div class="mt-4 border-top pt-3">
                            <h3 class="h6 fw-semibold mb-2">Account Information</h3>
                            <ul class="list-unstyled mb-0">
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Username:</span>
                                    <span class="fw-medium">{{ user.username }}</span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">User ID:</span>
                                    <span class="fw-medium">{{ user.user_id }}</span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Email Verified:</span>
                                    <span class="fw-medium">
                                        {% if user.email_verified %}
                                            <span class="text-success">Yes</span>
                                        {% else %}
                                            <span class="text-danger">No</span>
                                        {% endif %}
                                    </span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Permission Group:</span>
                                    <span class="fw-medium">
                                        {% if user.group %}
                                            <span class="text-success">{{ user.group.name }}</span>
                                            <button type="button" class="ms-2 btn btn-link btn-sm p-0 change-group-btn" data-user-id="{{ user.user_id }}" data-group-name="{{ user.group.name }}">
                                                Change
                                            </button>
                                        {% else %}
                                            <span class="text-muted">No group assigned</span>
                                            <button type="button" class="ms-2 btn btn-link btn-sm p-0 change-group-btn" data-user-id="{{ user.user_id }}" data-group-name="">
                                                Assign
                                            </button>
                                        {% endif %}
                                    </span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Created:</span>
                                    <span class="fw-medium">{{ user.created_at.split('T')[0] if user.created_at else 'N/A' }}</span>
                                </li>
                                <li class="d-flex justify-content-between mb-2">
                                    <span class="text-secondary">Last Login:</span>
                                    <span class="fw-medium">{{ user.last_login.split('T')[0] if user.last_login else 'Never' }}</span>
                                </li>
                                <li class="d-flex justify-content-between">
                                    <span class="text-secondary">Failed Logins:</span>
                                    <span class="fw-medium">{{ user.failed_login_attempts }}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-4 d-flex flex-column gap-2">
                            <a href="{{ url_for('user.admin_edit_user', user_id=user.user_id) }}" class="btn btn-primary w-100">
                                Edit User
                            </a>
                            {% if user.user_id != current_user.user_id %}
                                <button type="button" class="btn btn-danger w-100 delete-user-btn" data-user-id="{{ user.user_id }}">
                                    Delete User
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <!-- Main Content Area -->
                <div class="col-md-8">
                    <!-- Dashboard Permissions -->
                    <div class="bg-white p-4 rounded border mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="h6 fw-semibold">Dashboard Permissions</h3>
                            <div class="text-secondary small">
                                <span class="d-inline-flex align-items-center me-3">
                                    <span class="d-inline-block rounded-circle bg-primary me-1" style="width: 1rem; height: 1rem;"></span> Override
                                </span>
                                <span class="d-inline-flex align-items-center me-3">
                                    <span class="d-inline-block rounded-circle bg-success me-1" style="width: 1rem; height: 1rem;"></span> Group
                                </span>
                                <span class="d-inline-flex align-items-center">
                                    <span class="d-inline-block rounded-circle bg-secondary me-1" style="width: 1rem; height: 1rem;"></span> Legacy
                                </span>
                            </div>
                        </div>
                        {% if dashboard_functions %}
                            <div class="row g-3">
                                {% for function in dashboard_functions %}
                                    {% set override = user.permission_overrides.get(function.id) %}
                                    {% set group_permission = user.group_permissions.get(function.id) %}
                                    {% set legacy_permission = user.dashboard_permissions.get(function.id, false) %}
                                    {% if override is not none %}
                                        {% set permission_source = 'override' %}
                                        {% set is_enabled = override %}
                                        {% set bg_color = 'bg-primary bg-opacity-10' %}
                                        {% set border_color = 'border-primary' %}
                                    {% elif group_permission is not none and user.group %}
                                        {% set permission_source = 'group' %}
                                        {% set is_enabled = group_permission %}
                                        {% set bg_color = 'bg-success bg-opacity-10' %}
                                        {% set border_color = 'border-success' %}
                                    {% else %}
                                        {% set permission_source = 'legacy' %}
                                        {% set is_enabled = legacy_permission %}
                                        {% set bg_color = 'bg-secondary bg-opacity-10' %}
                                        {% set border_color = 'border-secondary' %}
                                    {% endif %}
                                    <div class="col-md-6">
                                        <div class="p-3 rounded border {{ bg_color }} {{ border_color }} mb-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h4 class="h6 fw-semibold text-dark mb-1">{{ function.name }}</h4>
                                                    <p class="small text-secondary mb-1">{{ function.description }}</p>
                                                    <div class="mt-1 small">
                                                        {% if permission_source == 'override' %}
                                                            <span class="text-primary">Individual override</span>
                                                        {% elif permission_source == 'group' %}
                                                            <span class="text-success">From group: {{ user.group.name }}</span>
                                                        {% else %}
                                                            <span class="text-muted">Legacy permission</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="ms-3">
                                                    <label class="form-check form-switch">
                                                        <input type="checkbox" class="form-check-input permission-toggle" data-user-id="{{ user.user_id }}" data-function-name="{{ function.id }}" {% if is_enabled %}checked{% endif %}>
                                                        <span class="ms-2 small fw-medium permission-status">{% if is_enabled %}Enabled{% else %}Disabled{% endif %}</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted">No dashboard functions available.</p>
                        {% endif %}
                    </div>
                    <!-- Recent Activity -->
                    <div class="bg-white p-4 rounded border">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="h6 fw-semibold">Recent Activity</h3>
                            <a href="{{ url_for('user.admin_activity_logs', user_id=user.user_id) }}" class="link-primary small">View All Activity</a>
                        </div>
                        {% if user.recent_activity %}
                            <div class="table-responsive">
                                <table class="table table-sm table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col">Timestamp</th>
                                            <th scope="col">Action</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for activity in user.recent_activity %}
                                            <tr>
                                                <td>{{ activity.timestamp.split('T')[0] if activity.timestamp else 'N/A' }} {{ activity.timestamp.split('T')[1].split('.')[0] if activity.timestamp and 'T' in activity.timestamp else '' }}</td>
                                                <td>{{ activity.action_type.replace('_', ' ').capitalize() }}</td>
                                                <td>
                                                    <span class="badge rounded-pill px-3 py-1 text-bg-{% if activity.status == 'success' %}success{% elif activity.status == 'failure' %}danger{% elif activity.status == 'warning' %}warning{% else %}primary{% endif %}">
                                                        {{ activity.status.capitalize() }}
                                                    </span>
                                                </td>
                                                <td class="text-truncate" style="max-width: 200px;">{{ activity.details }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No recent activity found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Group Modal -->
    <div id="changeGroupModal" class="modal fade" tabindex="-1" aria-labelledby="changeGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title h5" id="changeGroupModalLabel">Change Permission Group</h3>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <label for="groupSelect" class="form-label">Select Group</label>
                    <select id="groupSelect" class="form-select">
                        <option value="">-- No Group --</option>
                        <!-- Groups will be loaded dynamically -->
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveGroupChange()">Save</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <!-- Alpine.js for dropdowns -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Change Group Modal -->
    <div id="changeGroupModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-900">Change Permission Group</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeChangeGroupModal()">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="mb-4">
                <label for="groupSelect" class="block text-sm font-medium text-gray-700 mb-2">Select Group</label>
                <select id="groupSelect" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">-- No Group --</option>
                    <!-- Groups will be loaded dynamically -->
                </select>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeChangeGroupModal()">
                    Cancel
                </button>
                <button type="button" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" onclick="saveGroupChange()">
                    Save
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for the change group modal
        let currentUserId = null;

        // Function to get CSRF token from meta tag
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }

        // Add event listeners to permission toggles
        $(document).ready(function() {
            // Permission override toggles
            $('.permission-toggle').change(function() {
                const userId = $(this).data('user-id');
                const functionName = $(this).data('function-name');
                const enabled = $(this).prop('checked');

                // Call the update function with the toggle element
                updatePermissionOverride(userId, functionName, enabled, this);
            });

            // Dashboard permission toggles (for legacy dashboard permissions)
            $('.dashboard-permission-toggle').change(function() {
                const userId = $(this).data('user-id');
                const functionName = $(this).data('function-name');
                const enabled = $(this).prop('checked');

                // Call the update function with the toggle element
                updateDashboardPermission(userId, functionName, enabled, this);
            });

            // Change group buttons
            $('.change-group-btn').click(function() {
                const userId = $(this).data('user-id');
                const groupName = $(this).data('group-name');
                showChangeGroupModal(userId, groupName || null);
            });

            // Delete user buttons
            $('.delete-user-btn').click(function() {
                const userId = $(this).data('user-id');
                confirmDeleteUser(userId);
            });
        });

        // Function to update permission overrides
        function updatePermissionOverride(userId, functionName, enabled, element) {
            console.log(`Updating permission override for user ${userId}, function ${functionName}, enabled: ${enabled}`);

            // Find the card element for visual feedback
            const card = element ? element.closest('.rounded-lg') : null;

            // Add visual feedback during update
            if (card) {
                card.classList.add('opacity-75');
                card.style.position = 'relative';

                // Add a loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-50';
                spinner.innerHTML = '<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>';
                spinner.id = 'permission-spinner';
                card.appendChild(spinner);
            }

            fetch(`/admin/users/${userId}/permission_override`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    function_name: functionName,
                    enabled: enabled
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json();
            })
            .then(data => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('permission-spinner');
                    if (spinner) spinner.remove();
                }

                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Update the label text without reloading the page
                    if (element) {
                        const statusElement = element.closest('label').querySelector('.permission-status');
                        if (statusElement) {
                            statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
                        }
                    }

                    // Change the card background to blue (override)
                    if (card) {
                        card.className = card.className.replace(/bg-\w+-50/g, 'bg-blue-50');
                        card.className = card.className.replace(/border-\w+-200/g, 'border-blue-200');

                        // Update the permission source text
                        const sourceText = card.querySelector('.text-xs span');
                        if (sourceText) {
                            sourceText.className = 'text-blue-600';
                            sourceText.textContent = 'Individual override';
                        }
                    }
                } else {
                    console.error('Permission override error:', data.error);
                    Toastify({
                        text: `Error: ${data.error}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Revert the toggle if there was an error
                    if (element) {
                        element.checked = !enabled;
                    }
                }
            })
            .catch(error => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('permission-spinner');
                    if (spinner) spinner.remove();
                }

                console.error('Permission override exception:', error);
                Toastify({
                    text: `Error: ${error.message}`,
                    duration: 3000,
                    close: true,
                    gravity: "top",
                    position: "right",
                    style: {
                        background: "#EF4444",
                    },
                }).showToast();

                // Revert the toggle if there was an error
                if (element) {
                    element.checked = !enabled;
                }
            });
        }

        // Function to show the change group modal
        function showChangeGroupModal(userId, currentGroupName) {
            currentUserId = userId;

            // Fetch available groups
            fetch('/admin/permission_groups', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const groupSelect = document.getElementById('groupSelect');
                        groupSelect.innerHTML = '<option value="">-- No Group --</option>';

                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_id;
                            option.textContent = group.name;
                            option.selected = group.name === currentGroupName;
                            groupSelect.appendChild(option);
                        });

                        // Show the modal
                        document.getElementById('changeGroupModal').classList.remove('hidden');
                    } else {
                        Toastify({
                            text: `Error: ${data.error || 'Failed to load groups'}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            style: {
                                background: "#EF4444",
                            },
                        }).showToast();
                    }
                })
                .catch(error => {
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                });
        }

        // Function to close the change group modal
        function closeChangeGroupModal() {
            document.getElementById('changeGroupModal').classList.add('hidden');
            currentUserId = null;
        }

        // Function to save the group change
        function saveGroupChange() {
            if (!currentUserId) return;

            const groupId = document.getElementById('groupSelect').value;

            fetch(`/admin/users/${currentUserId}/group`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    group_id: groupId || null
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                if (response.headers.get('content-type') && response.headers.get('content-type').includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                    return null; // Return null to skip the next then block
                }
                return response.json();
            })
            .then(data => {
                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: 'Group updated successfully',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Close the modal
                    closeChangeGroupModal();

                    // Reload the page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    Toastify({
                        text: `Error: ${data.error}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }
            })
            .catch(error => {
                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }
            });
        }

        // Legacy function for dashboard permissions (kept for backward compatibility)
        function updateDashboardPermission(userId, functionName, enabled, element) {
            console.log(`Updating dashboard permission for user ${userId}, function ${functionName}, enabled: ${enabled}`);

            // Find the card element for visual feedback
            const card = element ? element.closest('.rounded-lg') : null;

            // Add visual feedback during update
            if (card) {
                card.classList.add('opacity-75');
                card.style.position = 'relative';

                // Add a loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-50';
                spinner.innerHTML = '<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>';
                spinner.id = 'dashboard-permission-spinner';
                card.appendChild(spinner);
            }

            fetch(`/admin/users/${userId}/dashboard_permissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    function_name: functionName,
                    enabled: enabled
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    // Remove loading spinner
                    if (card) {
                        card.classList.remove('opacity-75');
                        const spinner = document.getElementById('dashboard-permission-spinner');
                        if (spinner) spinner.remove();
                    }

                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json().catch(error => {
                    console.error('Error parsing JSON:', error);
                    return null;
                });
            })
            .then(data => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('dashboard-permission-spinner');
                    if (spinner) spinner.remove();
                }

                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#10B981",
                        },
                    }).showToast();

                    // Update the label text without reloading the page
                    if (element) {
                        const statusElement = element.closest('label').querySelector('.dashboard-permission-status');
                        if (statusElement) {
                            statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
                        }
                    }
                } else {
                    Toastify({
                        text: `Error: ${data.error || 'Unknown error'}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Revert the toggle if there was an error
                    if (element) {
                        element.checked = !enabled;
                    }
                }
            })
            .catch(error => {
                // Remove loading spinner
                if (card) {
                    card.classList.remove('opacity-75');
                    const spinner = document.getElementById('dashboard-permission-spinner');
                    if (spinner) spinner.remove();
                }

                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    console.error('Detailed error:', error);
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        style: {
                            background: "#EF4444",
                        },
                    }).showToast();
                }

                // Revert the toggle if there was an error
                if (element) {
                    element.checked = !enabled;
                }
            });
        }

        // Legacy function for category permissions (kept for backward compatibility)
        function updatePermission(userId, category, permission) {
            fetch(`/admin/users/${userId}/permissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({
                    category: category,
                    permission: permission
                }),
            })
            .then(response => {
                // Check if the response is HTML (likely a redirect to login page)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();

                    // Optionally redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);

                    return null; // Return null to skip the next then block
                }

                return response.json();
            })
            .then(data => {
                if (!data) return; // Skip if we got HTML response

                if (data.success) {
                    Toastify({
                        text: `Permission updated successfully`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#10B981",
                    }).showToast();

                    // Reload the page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    Toastify({
                        text: `Error: ${data.error || 'Unknown error'}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();
                }
            })
            .catch(error => {
                // Check if the error is due to parsing HTML as JSON
                if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                    Toastify({
                        text: 'Your session may have expired. Please refresh the page and try again.',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();

                    // Redirect to login page after a delay
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 2000);
                } else {
                    console.error('Detailed error:', error);
                    Toastify({
                        text: `Error: ${error.message}`,
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                    }).showToast();
                }
            });
        }

        // Function to confirm and delete user
        function confirmDeleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                fetch(`/admin/users/${userId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken()
                    }
                })
                .then(response => {
                    // Check if the response is HTML (likely a redirect to login page)
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('text/html')) {
                        Toastify({
                            text: 'Your session may have expired. Please refresh the page and try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();

                        // Optionally redirect to login page after a delay
                        setTimeout(() => {
                            window.location.href = '/admin';
                        }, 2000);

                        return null; // Return null to skip the next then block
                    }

                    return response.json();
                })
                .then(data => {
                    if (!data) return; // Skip if we got HTML response

                    if (data.success) {
                        Toastify({
                            text: `User deleted successfully`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#10B981",
                        }).showToast();

                        // Redirect to user list
                        setTimeout(() => {
                            window.location.href = "{{ url_for('user.admin_users') }}";
                        }, 1000);
                    } else {
                        Toastify({
                            text: `Error: ${data.error || 'Unknown error'}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();
                    }
                })
                .catch(error => {
                    // Check if the error is due to parsing HTML as JSON
                    if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
                        Toastify({
                            text: 'Your session may have expired. Please refresh the page and try again.',
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();

                        // Redirect to login page after a delay
                        setTimeout(() => {
                            window.location.href = '/admin';
                        }, 2000);
                    } else {
                        console.error('Detailed error:', error);
                        Toastify({
                            text: `Error: ${error.message}`,
                            duration: 3000,
                            close: true,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                        }).showToast();
                    }
                });
            }
        }
    </script>
{% endblock %}

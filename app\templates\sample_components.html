{% extends "admin_base.html" %}
{% from "macros/ui_components.html" import button, card, alert, badge, input, select, textarea, pagination %}

{% block title %}UI Components{% endblock %}

{% block content_title %}UI Component Examples{% endblock %}

{% block content %}
    <div class="row mb-5">
        <div class="col-12">
            {{ alert("This page demonstrates the usage of reusable UI components through Jinja2 macros.", "info", True) }}
        </div>
    </div>

    <!-- Buttons Section -->
    <section class="mb-5">
        <h2 class="mb-4">Buttons</h2>
        <div class="card bg-card border-standard">
            <div class="card-body">
                <h5 class="card-title">Button Variants</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ button("Primary", "primary") }}
                        {{ button("Secondary", "secondary") }}
                        {{ button("Success", "success") }}
                        {{ button("Danger", "danger") }}
                        {{ button("Warning", "warning") }}
                        {{ button("Info", "info") }}
                        {{ button("Light", "light") }}
                        {{ button("Dark", "dark") }}
                        {{ button("Link", "link") }}
                    </div>
                </div>

                <h5 class="card-title">Outline Buttons</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ button("Primary", "outline-primary") }}
                        {{ button("Secondary", "outline-secondary") }}
                        {{ button("Success", "outline-success") }}
                        {{ button("Danger", "outline-danger") }}
                        {{ button("Warning", "outline-warning") }}
                        {{ button("Info", "outline-info") }}
                        {{ button("Light", "outline-light") }}
                        {{ button("Dark", "outline-dark") }}
                    </div>
                </div>

                <h5 class="card-title">Button Sizes</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2 align-items-center">
                        {{ button("Small", "primary", "sm") }}
                        {{ button("Medium", "primary", "md") }}
                        {{ button("Large", "primary", "lg") }}
                    </div>
                </div>

                <h5 class="card-title">Buttons with Icons</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ button("Add User", "primary", "md", "fas fa-user-plus") }}
                        {{ button("Delete", "danger", "md", "fas fa-trash") }}
                        {{ button("Settings", "secondary", "md", "fas fa-cog") }}
                        {{ button("Download", "success", "md", "fas fa-download") }}
                    </div>
                </div>

                <h5 class="card-title">Link Buttons</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ button("Go to Dashboard", "primary", "md", "fas fa-tachometer-alt", "/admin") }}
                        {{ button("External Link", "info", "md", "fas fa-external-link-alt", "https://example.com", None, "", {"target": "_blank"}) }}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cards Section -->
    <section class="mb-5">
        <h2 class="mb-4">Cards</h2>
        <div class="row">
            <div class="col-md-6 mb-4">
                {% call card(title="Basic Card", subtitle="Card with content") %}
                    <p>This is a basic card with title, subtitle, and content.</p>
                    <p>The content is passed using the Jinja2 call block syntax.</p>
                {% endcall %}
            </div>
            
            <div class="col-md-6 mb-4">
                {{ card(
                    title="Card with Footer",
                    content="<p>This card has a footer section.</p>",
                    footer='<div class="d-flex justify-content-end"><button class="btn btn-primary">Save</button></div>'
                ) }}
            </div>
            
            <div class="col-md-6 mb-4">
                {% call card(title="Card with Custom Classes", card_classes="border-primary", header_classes="bg-primary text-white") %}
                    <p>This card has custom classes for styling the card and header.</p>
                {% endcall %}
            </div>
            
            <div class="col-md-6 mb-4">
                {% call card() %}
                    <h5 class="card-title">Card without Header</h5>
                    <p>This card doesn't have a header section, the title is part of the content.</p>
                {% endcall %}
            </div>
        </div>
    </section>

    <!-- Alerts Section -->
    <section class="mb-5">
        <h2 class="mb-4">Alerts</h2>
        <div class="card bg-card border-standard">
            <div class="card-body">
                {{ alert("This is a primary alert", "primary") }}
                {{ alert("This is a secondary alert", "secondary") }}
                {{ alert("This is a success alert", "success") }}
                {{ alert("This is a danger alert", "danger") }}
                {{ alert("This is a warning alert", "warning") }}
                {{ alert("This is an info alert", "info") }}
                {{ alert("This is a dismissible alert", "info", True) }}
                {{ alert("This is an alert with custom icon", "primary", False, "fas fa-star") }}
            </div>
        </div>
    </section>

    <!-- Badges Section -->
    <section class="mb-5">
        <h2 class="mb-4">Badges</h2>
        <div class="card bg-card border-standard">
            <div class="card-body">
                <h5 class="card-title">Badge Variants</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ badge("Primary", "primary") }}
                        {{ badge("Secondary", "secondary") }}
                        {{ badge("Success", "success") }}
                        {{ badge("Danger", "danger") }}
                        {{ badge("Warning", "warning") }}
                        {{ badge("Info", "info") }}
                        {{ badge("Light", "light") }}
                        {{ badge("Dark", "dark") }}
                    </div>
                </div>

                <h5 class="card-title">Pill Badges</h5>
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {{ badge("Primary", "primary", True) }}
                        {{ badge("Secondary", "secondary", True) }}
                        {{ badge("Success", "success", True) }}
                        {{ badge("Danger", "danger", True) }}
                    </div>
                </div>

                <h5 class="card-title">Badges in Context</h5>
                <div class="mb-4">
                    <h5>Example heading {{ badge("New", "primary") }}</h5>
                    <button class="btn btn-primary">
                        Notifications {{ badge("4", "light", True, "text-dark") }}
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Form Elements Section -->
    <section class="mb-5">
        <h2 class="mb-4">Form Elements</h2>
        <div class="card bg-card border-standard">
            <div class="card-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            {{ input(
                                type="text",
                                name="username",
                                id="username",
                                label="Username",
                                placeholder="Enter username",
                                required=True
                            ) }}
                        </div>
                        <div class="col-md-6">
                            {{ input(
                                type="email",
                                name="email",
                                id="email",
                                label="Email",
                                placeholder="Enter email",
                                required=True,
                                help_text="We'll never share your email with anyone else."
                            ) }}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            {{ input(
                                type="password",
                                name="password",
                                id="password",
                                label="Password",
                                required=True,
                                error="Password must be at least 8 characters long."
                            ) }}
                        </div>
                        <div class="col-md-6">
                            {{ select(
                                name="role",
                                id="role",
                                label="Role",
                                options=[
                                    {"value": "", "text": "-- Select Role --"},
                                    {"value": "admin", "text": "Administrator"},
                                    {"value": "editor", "text": "Editor"},
                                    {"value": "viewer", "text": "Viewer"}
                                ],
                                selected="editor",
                                required=True
                            ) }}
                        </div>
                    </div>

                    {{ textarea(
                        name="bio",
                        id="bio",
                        label="Biography",
                        placeholder="Tell us about yourself",
                        rows=4
                    ) }}

                    <div class="mt-4">
                        {{ button("Submit", "primary", "md", "fas fa-save") }}
                        {{ button("Cancel", "secondary", "md") }}
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Pagination Section -->
    <section class="mb-5">
        <h2 class="mb-4">Pagination</h2>
        <div class="card bg-card border-standard">
            <div class="card-body">
                <h5 class="card-title">Default Pagination</h5>
                <div class="mb-4">
                    {{ pagination(3, 10, "/sample?page=PAGE") }}
                </div>

                <h5 class="card-title">Pagination Sizes</h5>
                <div class="mb-4">
                    <div class="mb-3">
                        {{ pagination(3, 10, "/sample?page=PAGE", "start", "sm") }}
                    </div>
                    <div class="mb-3">
                        {{ pagination(3, 10, "/sample?page=PAGE", "start", "md") }}
                    </div>
                    <div>
                        {{ pagination(3, 10, "/sample?page=PAGE", "start", "lg") }}
                    </div>
                </div>

                <h5 class="card-title">Pagination Alignment</h5>
                <div class="mb-4">
                    <div class="mb-3">
                        {{ pagination(3, 10, "/sample?page=PAGE", "start") }}
                    </div>
                    <div class="mb-3">
                        {{ pagination(3, 10, "/sample?page=PAGE", "center") }}
                    </div>
                    <div>
                        {{ pagination(3, 10, "/sample?page=PAGE", "end") }}
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% extends "admin_base.html" %}

{% block title %}Add New User{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h4 fw-bold text-dark">Add New User</h1>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('user.admin_users') }}" class="link-primary">&larr; Back to User Management</a>
                </div>
            </div>
            <div class="mx-auto" style="max-width: 700px;">
                <div class="bg-white p-4 rounded border">
                    <form method="POST" action="{{ url_for('user.admin_new_user') }}">
                        {{ form.csrf_token }}
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="username" class="form-label">Username</label>
                                {{ form.username(class="form-control") }}
                                {% if form.username.errors %}
                                    <div class="form-text text-danger">{{ form.username.errors[0] }}</div>
                                {% endif %}
                                <div class="form-text text-muted">3-20 characters, alphanumeric and underscores only</div>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                {{ form.email(class="form-control") }}
                                {% if form.email.errors %}
                                    <div class="form-text text-danger">{{ form.email.errors[0] }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name</label>
                            {{ form.full_name(class="form-control") }}
                            {% if form.full_name.errors %}
                                <div class="form-text text-danger">{{ form.full_name.errors[0] }}</div>
                            {% endif %}
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="role" class="form-label">Role</label>
                                {{ form.role(class="form-select", onchange="showRoleGroupInfo(this.value)") }}
                                {% if form.role.errors %}
                                    <div class="form-text text-danger">{{ form.role.errors[0] }}</div>
                                {% endif %}
                                <div id="roleGroupInfo" class="form-text text-muted d-none"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="account_status" class="form-label">Account Status</label>
                                {{ form.account_status(class="form-select") }}
                                {% if form.account_status.errors %}
                                    <div class="form-text text-danger">{{ form.account_status.errors[0] }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-4 border-top pt-3">
                            <h4 class="h6 fw-semibold mb-2">Password</h4>
                            <div class="form-text text-muted mb-3">Leave blank to generate a temporary password</div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="password" class="form-label">Password</label>
                                    {{ form.password(class="form-control") }}
                                    {% if form.password.errors %}
                                        <div class="form-text text-danger">{{ form.password.errors[0] }}</div>
                                    {% endif %}
                                    <div class="form-text text-muted">Minimum 8 characters with mixed case, numbers, and symbols</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    {{ form.confirm_password(class="form-control") }}
                                    {% if form.confirm_password.errors %}
                                        <div class="form-text text-danger">{{ form.confirm_password.errors[0] }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('user.admin_users') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-success">Create User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Function to show information about group assignment based on role
        function showRoleGroupInfo(role) {
            const infoElement = document.getElementById('roleGroupInfo');

            if (role === 'editor') {
                infoElement.textContent = 'User will be automatically assigned to the Editor Group with all Editor permissions.';
                infoElement.classList.remove('d-none');
                infoElement.classList.add('text-primary');
            } else if (role === 'viewer') {
                infoElement.textContent = 'User will be automatically assigned to the Viewer Group with all Viewer permissions.';
                infoElement.classList.remove('d-none');
                infoElement.classList.add('text-success');
            } else {
                infoElement.classList.add('d-none');
            }
        }

        // Show info on page load based on current selection
        document.addEventListener('DOMContentLoaded', function() {
            const roleSelect = document.querySelector('select[name="role"]');
            if (roleSelect) {
                showRoleGroupInfo(roleSelect.value);
            }
        });
    </script>
{% endblock %}

{% extends "admin_base.html" %}

{% block title %}Manage Files{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow-sm">
        <div class="card-body">
            <h1 class="h2 fw-bold text-dark mb-4">Manage Files</h1>

            <!-- Search Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('list_files') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="search_query" class="form-label">Search Files</label>
                            <input type="text" class="form-control" id="search_query" name="search"
                                   value="{{ request.args.get('search', '') }}"
                                   placeholder="Search by filename, title, or author...">
                        </div>
                        <div class="col-md-3">
                            <label for="category_filter" class="form-label">Category</label>
                            <select class="form-select" id="category_filter" name="category">
                                <option value="">All Categories</option>
                                {% for category in files_data.keys() %}
                                <option value="{{ category }}" {% if request.args.get('category') == category %}selected{% endif %}>
                                    {{ category }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type_filter" class="form-label">Type</label>
                            <select class="form-select" id="type_filter" name="type">
                                <option value="">All Types</option>
                                <option value="pdf" {% if request.args.get('type') == 'pdf' %}selected{% endif %}>PDF</option>
                                <option value="url" {% if request.args.get('type') == 'url' %}selected{% endif %}>URL</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </form>
                    {% if request.args.get('search') or request.args.get('category') or request.args.get('type') %}
                    <div class="mt-3">
                        <a href="{{ url_for('list_files') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- List Files by Category -->
            {% if files_data %}
                <div class="mb-4">
                    {% for category, files in files_data.items() %}
                        <div class="bg-light p-4 rounded mb-4">
                            <h2 class="h4 fw-semibold text-dark mb-3">{{ category }}</h2>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-muted fw-medium">File/URL Name</th>
                                            <th class="text-muted fw-medium">Type</th>
                                            <th class="text-muted fw-medium">Metadata</th>
                                            <th class="text-muted fw-medium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for file in files %}
                                            <tr>
                                                <td class="text-dark">
                                                    {% if file.type == 'url' %}
                                                        <a href="{{ file.original_filename }}" target="_blank" class="text-primary text-decoration-underline text-truncate d-block" style="max-width: 300px;" title="{{ file.original_filename }}">
                                                            {{ file.original_filename }}
                                                        </a>
                                                        {% if file.scrape_depth is defined and file.scrape_depth > 0 %}
                                                            <span class="small text-muted mt-1 d-block">
                                                                Depth: {{ file.scrape_depth }}
                                                                {% if file.pages_scraped is defined %}
                                                                    ({{ file.pages_scraped }} pages)
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.database_retrieval is defined and file.database_retrieval %}
                                                            <span class="small text-success mt-1 d-block">
                                                                <svg class="d-inline-block me-1" width="12" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                </svg>
                                                                Using database content
                                                                {% if file.url_last_scraped is defined %}
                                                                    (Last scraped: {{ file.url_last_scraped.split('T')[0] }})
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.source_url_id is defined %}
                                                            <span class="small text-muted mt-1 d-block">
                                                                Database ID: {{ file.source_url_id }}
                                                            </span>
                                                        {% endif %}
                                                    {% elif file.original_url %}
                                                                                                <div>
                                            <span title="{{ file.source }}">{{ file.source }}</span>
                                            <div class="mt-1">
                                                                <a href="{{ file.original_url }}" target="_blank" class="small text-primary text-decoration-underline text-truncate d-block" style="max-width: 300px;" title="{{ file.original_url }}">
                                                                    Source: {{ file.original_url }}
                                                                </a>
                                                            </div>

                                                            {% if file.database_retrieval is defined and file.database_retrieval %}
                                                                <span class="small text-success mt-1 d-block">
                                                                    <svg class="d-inline-block me-1" width="12" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                    </svg>
                                                                    Using database content
                                                                </span>
                                                            {% endif %}

                                                            {% if file.source_url_id is defined or file.pdf_document_id is defined or file.cover_image_id is defined %}
                                                                <div class="small text-muted mt-1">
                                                                    {% if file.source_url_id is defined %}
                                                                        <span class="d-block">URL ID: {{ file.source_url_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.pdf_document_id is defined %}
                                                                        <span class="d-block">PDF ID: {{ file.pdf_document_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.cover_image_id is defined %}
                                                                        <span class="d-block">Cover Image ID: {{ file.cover_image_id }}</span>
                                                                    {% endif %}
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                                                        {% else %}
                                        <span title="{{ file.source }}">{{ file.source }}</span>
                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge {% if file.type == 'pdf' %}bg-primary{% else %}bg-success{% endif %}">
                                                        {{ file.type | upper }}
                                                    </span>

                                                    {% if file.database_retrieval is defined and file.database_retrieval %}
                                                        <span class="badge bg-success ms-1">
                                                            DB
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if file.type == 'pdf' and file.pdf_document_id %}
                                                        <div class="small">
                                                            {% if file.pdf_title %}
                                                                <div class="text-truncate" style="max-width: 200px;" title="{{ file.pdf_title }}">
                                                                    <strong>{{ file.pdf_title }}</strong>
                                                                </div>
                                                            {% endif %}
                                                            {% if file.pdf_author %}
                                                                <div class="text-muted text-truncate" style="max-width: 200px;" title="{{ file.pdf_author }}">
                                                                    {{ file.pdf_author }}
                                                                </div>
                                                            {% endif %}
                                                            {% if file.page_count %}
                                                                <span class="badge bg-light text-dark">{{ file.page_count }} pages</span>
                                                            {% endif %}
                                                            {% if file.file_size %}
                                                                <span class="badge bg-light text-dark">
                                                                    {% if file.file_size > 1048576 %}
                                                                        {{ (file.file_size / 1024 / 1024) | round(1) }} MB
                                                                    {% else %}
                                                                        {{ (file.file_size / 1024) | round(1) }} KB
                                                                    {% endif %}
                                                                </span>
                                                            {% endif %}
                                                        </div>
                                                    {% elif file.type == 'url' %}
                                                        <div class="small text-muted">
                                                            {% if file.url_last_scraped %}
                                                                Last scraped: {{ file.url_last_scraped.split('T')[0] }}
                                                            {% endif %}
                                                        </div>
                                                    {% else %}
                                                        <span class="text-muted small">No metadata</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        {% if file.type == 'pdf' and file.pdf_document_id %}
                                                            <a href="{{ url_for('view_pdf_details', pdf_id=file.pdf_document_id) }}"
                                                               class="btn btn-info btn-sm" title="View PDF Details">
                                                                <i class="fas fa-info-circle"></i>
                                                            </a>
                                                        {% endif %}
                                                        <a href="{{ url_for('view_vector_data', category=category, filename=file.source) }}"
                                                           class="btn btn-primary btn-sm" title="View Vector Data">
                                                            <i class="fas fa-search"></i>
                                                        </a>
                                                        <form method="POST" action="{{ url_for('delete_file_route', category=category, filename=file.source) }}"
                                                              onsubmit="return handleDeleteSubmit(this, '{{ file.type }}');" class="d-inline">
                                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-warning d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <svg class="me-2" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div>
                        <p class="mb-0">
                            No files or URLs found. <a href="{{ url_for('upload_file') }}" class="fw-medium text-decoration-underline">Upload some content</a> to get started.
                        </p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
function handleDeleteSubmit(form, fileType) {
    // Prevent double submission
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton.disabled) {
        return false;
    }
    
    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete this ${fileType}? This will also delete all associated vector data.`)) {
        return false;
    }
    
    // Disable the submit button to prevent double submission
    submitButton.disabled = true;
    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Deleting...';
    
    // Allow the form to submit normally - the server will redirect after deletion
    // Don't re-enable the button since we're redirecting anyway
    
    return true;
}
</script>
{% endblock %}